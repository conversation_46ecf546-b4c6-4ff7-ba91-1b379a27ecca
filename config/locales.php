<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Supported Locales Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the locale settings for the application.
    | The 'supported' array contains all locales that the application supports.
    | The 'default' locale is required for all content creation.
    | The 'fallback' locale is used when content is not available in the requested locale.
    |
    */

    'locales' => [
        'supported' => ['en', 'zh_cn'],
        'default' => 'en',
        'fallback' => 'zh_cn',
    ],

    /*
    |--------------------------------------------------------------------------
    | Locale Labels and Metadata
    |--------------------------------------------------------------------------
    |
    | This array contains display labels and metadata for each supported locale.
    |
    */

    'locale_metadata' => [
        'en' => [
            'label' => 'English',
            'flag' => '🇺🇸',
            'required' => true,
            'is_default' => true,
        ],
        'zh_cn' => [
            'label' => '中文',
            'flag' => '🇨🇳',
            'required' => false,
            'is_default' => false,
        ],
    ],
];
