<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Supported Locales
    |--------------------------------------------------------------------------
    |
    | Specify the locales which your site supports. This setting is used
    | to determine which translations should be generated and which
    | fallback locale should be used when a translation is missing.
    |
    */

    'locales' => array_filter(explode(',', env('SUPPORTED_LOCALES', 'en,zh_CN'))),

    /*
    |--------------------------------------------------------------------------
    | Locale Separator
    |--------------------------------------------------------------------------
    |
    | This is the separator used when retrieving locale from the request
    | or storing translations. You may use any string here but make sure
    | to choose one that doesn't conflict with your locale names.
    |
    */

    'locale_separator' => '-',

    /*
    |--------------------------------------------------------------------------
    | Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale defines which locale should be used when a translation
    | in the current locale is not available. You may specify a single locale
    | to always fallback to, or set to null to use the app fallback.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Locale Key Name
    |--------------------------------------------------------------------------
    |
    | This is the name of the key in the translations table that is used
    | to store the locale information. You may change this to any
    | other column name if needed.
    |
    */

    'locale_key' => 'locale',

    /*
    |--------------------------------------------------------------------------
    | Use Fallback
    |--------------------------------------------------------------------------
    |
    | Determines whether the package should use the fallback locale when a
    | translation for the requested locale doesn't exist. Set to false if
    | you want to receive null instead of the fallback translation.
    |
    */

    'use_fallback' => true,

    /*
    |--------------------------------------------------------------------------
    | Use Property Fallback
    |--------------------------------------------------------------------------
    |
    | When a translation is requested for a property that doesn't exist in
    | the translation table, the package will try to get the value from
    | the model's base table column. Set to false to disable.
    |
    */

    'use_property_fallback' => true,

    /*
    |--------------------------------------------------------------------------
    | To Array
    |--------------------------------------------------------------------------
    |
    | Determines whether translations should be returned when using the
    | `toArray` method. If false, the translations will not be included
    | in the array representation of the model.
    |
    */

    'to_array_always_loads_translations' => true,
];
