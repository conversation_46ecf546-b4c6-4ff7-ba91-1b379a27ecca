<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],
    'senhuo' => [
        'domain' => env('SENHUO_DOMAIN'),
        'merchant' => env('SENHUO_USERNAME', ''),
        'key' => env('SENHUO_KEY', ''),
        'prefix' => env('SENHUO_VERSION_PREFIX', 'lctest'),
        'development' => env('SENHUO_DEVELOPMENT', true),
        'proxy' => 'http://proxy:proxy123123@*************:34566',
        'withdraw_callback_url' => env('WITHDRAW_CALLBACK_URL', 'https://api-jingdong.1bit.vip/api/v1/integrations/senhuo/withdraw/callback'),
    ],
    'api' => [
        'deposit_status_url' => env('DEPOSIT_STATUS_URL', 'https://api-jingdong.1bit.vip/api/v1/integrations/deposit/check-status'),
    ],
    'ali_cloud_api' => [
        'app_key' => env('ALI_CLOUD_API_APP_KEY'),
        'app_secret' => env('ALI_CLOUD_API_APP_SECRET'),
        'app_code' => env('ALI_CLOUD_API_APP_CODE'),
        'iden_auth_url' => env('ALI_CLOUD_API_IDEN_AUTH_URL'),
    ],
    'payment_service' => [
        'enabled' => env('PAYMENT_SERVICE_ENABLED', true),
        'domain' => env('PAYMENT_SERVICE_DOMAIN'),
        'api_key' => env('PAYMENT_SERVICE_API_KEY'),
    ],
    'api_service' => [
        'url' => env('API_SERVICE_URL', 'http://api/api/v1'),
    ],
];
