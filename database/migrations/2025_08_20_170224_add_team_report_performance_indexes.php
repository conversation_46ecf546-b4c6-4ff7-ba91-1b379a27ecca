<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('deposits', function (Blueprint $table) {
            $table->index(['user_id', 'created_at', 'status'], 'idx_deposits_user_created_status');
            $table->index(['user_id', 'status', 'created_at'], 'idx_deposits_user_status_created');
        });

        Schema::table('contributes', function (Blueprint $table) {
            $table->index(['user_id', 'status', 'created_at'], 'idx_contributes_user_status_created');
        });

        Schema::table('withdraws', function (Blueprint $table) {
            $table->index(['wallet_id', 'status'], 'idx_withdraws_wallet_status');
        });

        Schema::table('investment_logs', function (Blueprint $table) {
            $table->index('user_id', 'idx_investment_logs_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('deposits', function (Blueprint $table) {
            $table->dropIndex('idx_deposits_user_created_status');
            $table->dropIndex('idx_deposits_user_status_created');
        });

        Schema::table('contributes', function (Blueprint $table) {
            $table->dropIndex('idx_contributes_user_status_created');
        });

        Schema::table('withdraws', function (Blueprint $table) {
            $table->dropIndex('idx_withdraws_wallet_status');
        });

        Schema::table('investment_logs', function (Blueprint $table) {
            $table->dropIndex('idx_investment_logs_user');
        });
    }
};
