# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel-based Tornado Admin API system built with PHP 8.2+ and Laravel 11. The application provides comprehensive administrative functionality for managing users, finances, activities, VIP levels, and various system components. It uses the Larke Admin package for administrative interface and includes features like wallet management, payment processing, and user rewards.

## Development Commands

### Starting Development Environment
```bash
# Install dependencies
composer install

# Setup environment and run migrations (as mentioned in README)
php artisan route:import

# Start development server with all services
composer run dev
# This runs: server, queue worker, logs (pail), and vite concurrently
```

### Testing
```bash
# Run tests with Pest
php artisan test
# or
./vendor/bin/pest

# Run specific test suite
./vendor/bin/pest tests/Feature
./vendor/bin/pest tests/Unit
```

### Code Quality
```bash
# Format code with Laravel Pint
./vendor/bin/pint

# Check code style
./vendor/bin/phpcs

# Static analysis
./vendor/bin/phpstan
```

### Frontend Assets
```bash
# Build for development
npm run dev

# Build for production
npm run build
```

## Architecture Overview

### Core Structure
- **Admin Controllers**: Located in `app/Admin/Http/Controllers/` - handles administrative operations
- **Services**: Business logic layer in `app/Services/` with specialized wallet services
- **Models**: Eloquent models in `app/Models/` representing database entities
- **Repositories**: Data access layer in `app/Repositories/` for complex queries
- **Constants**: System constants and enums in `app/Constants/`

### Key Components

#### Wallet System
- **WalletService**: Main service for wallet operations with factory pattern
- **Specialized Wallets**: Commission, Deposit, Partnership, Profit, Promotion, Reward wallets
- **Abstractions**: `WalletServiceAbstract` and `WalletServiceInterface` for consistency

#### Authentication & Authorization
- **Larke Admin**: Primary admin authentication system
- **2FA Support**: Google2FA integration for enhanced security
- **Route Protection**: Admin routes protected with `auth:larke` middleware

#### API Structure
- **Admin API**: Routes in `routes/admin-api.php` with `/admin-api` prefix
- **OpenAPI Documentation**: Available in `docs/openapi.json`
- **Request Validation**: FormRequest classes in `app/Http/Requests/`

### Database Architecture
- **Multi-wallet System**: Users can have multiple wallet types
- **Activity Tracking**: Activities with transactions and versioning
- **VIP System**: Levels, conditions, rewards, and history tracking
- **Transaction Logging**: Comprehensive audit trail for financial operations

## Configuration Notes

- **Environment**: Uses `.env` for configuration
- **Admin Route Prefix**: `/admin-api` (configurable via `LARKE_ADMIN_ROUTE_PREFIX`)
- **Two-Factor Auth**: Enabled by default (`LARKE_ADMIN_TWO_FA_ENABLED`)
- **Queue Processing**: Required for background tasks
- **Telescope**: Debugging tool (disabled in testing)

## Testing Configuration

- **Framework**: Uses Pest testing framework
- **Database**: In-memory SQLite for testing (commented out in phpunit.xml)
- **Test Structure**: Feature tests for HTTP endpoints, Unit tests for business logic
- **Environment**: Isolated testing environment with array drivers

## Important Notes

- The system requires a separate API repository setup before running migrations
- Pre-commit hooks are automatically installed via Composer scripts
- Uses Vite for frontend asset compilation
- Activity logging is implemented via Spatie package
- Payment processing integrates with external services via HTTP clients