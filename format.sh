#!/bin/sh

# Get the list of changed PHP files
CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep '\.php$')

if [ -z "$CHANGED_FILES" ]; then
  echo "No PHP files to check."
  exit 0
fi

echo "Running Code Formatter on changed files..."

# Run phpcs on the changed files
./vendor/bin/pint -q $CHANGED_FILES
./vendor/bin/phpcbf -q --standard=phpcs.xml $CHANGED_FILES
for file in $CHANGED_FILES; do
  ./vendor/bin/phpcs -q --standard=phpcs.xml $file
  if [ $? -ne 0 ]; then
    echo "Code Formatter found errors in $file. Commit aborted."
    exit 1
  fi
done
git add $CHANGED_FILES
