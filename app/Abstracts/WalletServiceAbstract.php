<?php

namespace App\Abstracts;

use App\Constants\TransactionType;
use App\Constants\WalletAmountType;
use App\Constants\WalletType;
use App\Contracts\WalletBalanceAccessInterface;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\InvalidAmount;
use App\Models\Wallet;
use App\Utils\Math;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

abstract class WalletServiceAbstract implements WalletBalanceAccessInterface
{
    protected string $type;

    // abstract protected function setType(): void;

    public function __construct(private Wallet $wallet)
    {
        //
    }

    public function fluctuateProfit(string|float $amount, string|float|null $specificAmount = null): void
    {
        if ($specificAmount) {
            $this->fluctuate($amount, WalletAmountType::Profit, $specificAmount);
        } else {
            $this->fluctuate($amount, WalletAmountType::Profit);
        }
    }

    public function fluctuateReward(string|float $amount): void
    {
        $this->fluctuate($amount, WalletAmountType::Reward);
    }

    public function fluctuateDeposit(string|float $amount): void
    {
        $this->fluctuate($amount, WalletAmountType::Deposit);
    }

    public function fluctuateCommission(string|float $amount): void
    {
        $this->fluctuate($amount, WalletAmountType::Commission);
    }

    public function getWallet(): Wallet
    {
        return $this->wallet;
    }

    public function getBalance(): float
    {
        // always get newest value
        return $this->wallet->balance;
    }

    public function getProfit(): float
    {
        // always get newest value
        return $this->wallet->profit;
    }

    /**
     * based function for all wallets to add money to wallet may have to overwrite on specific wallet
     *
     * @throws InvalidAmount
     *
     * @deprecated
     */
    public function addFund(
        float|string $amount,
        TransactionType $type,
        ?string $transactionCode = null,
        ?string $remark = null,
        ?string $reference = null,
        ?int $referenceId = null,
        ?array $metadata = []
    ): void {
        if (Math::compare($amount, 0) < 0) {
            throw new InvalidAmount();
        }
        $oldBalance = $this->wallet->balance;
        $this->fluctuateBalance($amount);
        $currentBalance = $this->wallet->balance;
        $this->saveTransactionLogs(
            amount: $amount,
            balance: $currentBalance,
            oldBalance: $oldBalance,
            type: $type,
            remark: $remark ?? 'add funds',
            transactionCode: $transactionCode,
            reference: $reference,
            referenceId: $referenceId,
            metadata: $metadata
        );
    }

    public function fluctuateBalance(string|float $amount): void
    {
        $this->fluctuate($amount);
    }

    public function saveTransactionLogs(
        string|float $amount,
        float $balance,
        float $oldBalance,
        TransactionType $type,
        string $remark,
        ?string $transactionCode = null,
        ?string $reference = null,
        ?int $referenceId = null,
        ?array $metadata = null,
    ) {
        return $this->wallet->transactions()->create([
            'code' => $transactionCode ?? Str::uuid(),
            'user_id' => $this->wallet->user_id,
            'amount' => $amount,
            'remark' => $remark,
            'type' => $type->value,
            'new_balance' => $balance,
            'old_balance' => $oldBalance,
            'reference' => $reference,
            'reference_id' => $referenceId,
            'metadata' => $metadata,
        ]);
    }

    /**
     * based function for all wallets to add money to wallet may have to overwrite on specific wallet
     *
     * @throws InsufficientBalanceException|InvalidAmount
     *
     * @deprecated
     */
    public function deduceFund(
        float|string $amount,
        TransactionType $type,
        ?string $transactionCode = null,
        ?string $remark = null,
        ?string $reference = null,
        ?int $referenceId = null
    ): void {
        if (Math::compare($amount, 0) < 0) {
            throw new InvalidAmount();
        }

        $oldBalance = $this->wallet->balance;
        $this->fluctuateBalance(-$amount);
        // double check balance
        $this->checkBalance();

        $currentBalance = $this->wallet->balance;
        $this->saveTransactionLogs(
            amount: $amount,
            balance: $currentBalance,
            oldBalance: $oldBalance,
            type: $type,
            remark: $remark ?? 'deduce funds',
            transactionCode: $transactionCode,
            reference: $reference,
            referenceId: $referenceId,
        );
    }

    /**
     * @throws InsufficientBalanceException
     */
    public function checkBalance(string|float|null $amount = null): void
    {
        if (!$amount && Math::compare($this->wallet->balance, 0) < 0) {
            throw new InsufficientBalanceException();
        }
        if ($amount && Math::compare(Math::sub($this->wallet->balance, $amount), 0) < 0) {
            throw new InsufficientBalanceException();
        }
    }

    public function checkProfitBalance(string|float|null $amount = null): void
    {
        if (!$amount && Math::compare($this->wallet->refresh()->profit, 0) < 0) {
            throw new InsufficientBalanceException();
        }
        if ($amount && Math::compare(Math::sub($this->wallet->refresh()->profit, $amount), 0) < 0) {
            throw new InsufficientBalanceException();
        }
    }

    public function checkRewardBalance(string|float|null $amount = null): void
    {
        if (!$amount && Math::compare($this->wallet->refresh()->reward, 0) < 0) {
            throw new InsufficientBalanceException();
        }
        if ($amount && Math::compare(Math::sub($this->wallet->refresh()->reward, $amount), 0) < 0) {
            throw new InsufficientBalanceException();
        }
    }

    public function checkDepositBalance(string|float|null $amount = null): void
    {
        if (!$amount && Math::compare($this->wallet->refresh()->deposit, 0) < 0) {
            throw new InsufficientBalanceException();
        }
        if ($amount && Math::compare(Math::sub($this->wallet->refresh()->deposit, $amount), 0) < 0) {
            throw new InsufficientBalanceException();
        }
    }

    public function isBalanceWallet(WalletType $walletType): bool
    {
        return in_array($walletType, [WalletType::General]);
    }

    protected function fluctuate($amount, ?WalletAmountType $type = null, ?float $specificAmount = null): void
    {
        $changeAmount = (float) $amount;
        if ($type) {
            if (Math::compare($changeAmount, 0) < 0) {
                throw new InvalidAmount('Wrong fluctuate amount', 400);
            }
        }
        DB::beginTransaction();
        try {
            if ($changeAmount < 0) {
                $this->wallet->decrement('balance', abs($changeAmount));
            } else {
                $this->wallet->increment('balance', abs($changeAmount));
            }
            if ($type) {
                if ($specificAmount && Math::compare($specificAmount, 0) == 1) {
                    $this->wallet->increment($type->value, abs($specificAmount));
                } else {
                    // specific amount must be positive because we don't deduct amount of other type

                    $this->wallet->increment($type->value, abs($changeAmount));
                }
            }
            DB::commit();
            // update new wallet data
            $this->wallet = $this->wallet->refresh();
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            DB::rollBack();
        }
    }
}
