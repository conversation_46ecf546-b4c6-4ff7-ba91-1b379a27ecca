<?php

namespace App\Utils;

class Math
{
    /**
     * @param  null  $scale
     */
    public static function formatNumber($number, bool $isScale = false): string
    {
        $scale = $isScale ? config('app.bcmath.scale') : 10;

        return number_format($number, $scale, '.', '');
    }

    /**
     * @param  null  $scale
     */
    public static function add($number1, $number2, $scale = null): string
    {
        $number1 = static::formatNumber($number1);
        $number2 = static::formatNumber($number2);

        return bcadd($number1, $number2, $scale ?? config('app.bcmath.scale'));
    }

    /**
     * @param  null  $scale
     */
    public static function sub($number1, $number2, $scale = null): string
    {
        $number1 = static::formatNumber($number1);
        $number2 = static::formatNumber($number2);

        return bcsub($number1, $number2, $scale ?? config('app.bcmath.scale'));
    }

    /**
     * @param  null  $scale
     */
    public static function mul($number1, $number2, $scale = null): string
    {
        $number1 = static::formatNumber($number1);
        $number2 = static::formatNumber($number2);

        return bcmul($number1, $number2, $scale ?? config('app.bcmath.scale'));
    }

    /**
     * @param  null  $scale
     */
    public static function div($number1, $number2, $scale = null): string
    {
        $number1 = static::formatNumber($number1);
        $number2 = static::formatNumber($number2);

        return bcdiv($number1, $number2, $scale ?? config('app.bcmath.scale'));
    }

    /**
     * Raises a number to a specified power.
     *
     * @param  null  $scale
     */
    public static function pow($number, $power, $scale = null): string
    {
        if (is_string($power)) {
            $power = (int) $power;
        }
        $number = static::formatNumber($number);

        return bcpow($number, (string) $power, $scale ?? config('app.bcmath.scale'));
    }

    /**
     * Raises a number to a specified power.
     *
     * @param  null  $scale
     */
    public static function compare($number1, $number2, $scale = null): int
    {
        $number1 = static::formatNumber($number1);
        $number2 = static::formatNumber($number2);

        return bccomp($number1, $number2, $scale ?? config('app.bcmath.scale'));
    }
}
