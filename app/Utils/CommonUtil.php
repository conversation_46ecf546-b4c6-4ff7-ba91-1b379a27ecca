<?php

namespace App\Utils;

class CommonUtil
{
    public static function validateJson($data, $return = true): bool|array
    {
        if (!empty($data)) {
            $json = @json_decode($data, true);

            return (json_last_error() === JSON_ERROR_NONE) ? ($return ? $json : true) : false;
        }

        return false;
    }

    public static function convertBooleanString($data): ?bool
    {
        if (!empty($data)) {
            return $data === 'True' || $data === true;
        }

        return null;
    }

    /**
     * Ensures a domain string has or does not have a trailing slash.
     *
     * Removes any existing trailing slashes from the given domain string, then
     * appends a trailing slash if $endSlash is true.
     *
     * @param string $domain    The domain string to process.
     * @param bool   $endSlash  Whether to ensure the domain ends with a slash.
     *                          Set to true to add a trailing slash, or false to leave it off.
     * @return string           The processed domain string with or without a trailing slash.
     */
    public static function parseDomain(string $domain, bool $endSlash = true): string
    {
        return rtrim($domain, '/') . ($endSlash ? '/' : '');
    }

    /**
     * Recursively removes all null values from the given array.
     *
     * This method will also remove any arrays that become empty after processing nested values.
     *
     * @param array $array The input array to process.
     * @return array The array with all null values and empty arrays removed.
     */
    public static function removeNullValues(array $array): array
    {
        foreach ($array as $key => &$value) {
            if (is_array($value)) {
                $value = static::removeNullValues($value);
                if (empty($value)) {
                    unset($array[$key]);
                }
            } elseif (is_null($value)) {
                unset($array[$key]);
            }
        }

        return $array;
    }
}
