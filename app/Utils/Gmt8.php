<?php

namespace App\Utils;

use Carbon\Carbon;

class Gmt8
{
    public static function toUtc(string $datetime, bool $endOfDay = false): string
    {
        if ($endOfDay === true) {
            $gmt8 = Carbon::parse($datetime)->format('Y-m-d\T23:59:59+08:00');
        } else {
            $gmt8 = Carbon::parse($datetime)->format('Y-m-d\TH:i:s+08:00');
        }

        return Carbon::parse($gmt8)->tz('UTC')->format('Y-m-d H:i:s');
    }

    public static function toUtcTodayRange(string $datetime): array
    {
        $gmt8 = Carbon::parse($datetime)->format('Y-m-d\TH:i:s+08:00');
        $gmt = Carbon::parse($gmt8);

        $from = static::toUtc($gmt->startOfDay());
        $to = static::toUtc($gmt->endOfDay());

        return [$from, $to];
    }

    public static function now(): string
    {
        return Carbon::now()->addHours(8)->format('Y-m-d H:i:s');
    }

    public static function today(): string
    {
        return Carbon::now()->addHours(8)->format('Y-m-d');
    }

    public static function nowGmt8Instance(): Carbon
    {
        return Carbon::now()->addHours(8);
    }

    public static function toUtcHourRange(string $datetime): array
    {
        $gmt8 = Carbon::parse($datetime)->format('Y-m-d\TH:i:s+08:00');
        $gmt = Carbon::parse($gmt8);

        $from = static::toUtc($gmt->startOfHour());
        $to = static::toUtc($gmt->endOfHour());

        return [$from, $to];
    }

    public static function toGmt8(string $dateTime)
    {
        $utc = Carbon::parse($dateTime)->format('Y-m-d\TH:i:s+00:00');
        $gmt8 = Carbon::parse($utc)->addHours(8);

        return $gmt8->toDateTimeString();
    }

    public static function yestedday(): string
    {
        return Carbon::now()->addHours(8)->subDay()->format('Y-m-d');
    }
}
