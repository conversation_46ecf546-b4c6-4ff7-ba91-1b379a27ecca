<?php

namespace App\Contracts;

use App\Constants\TransactionType;

interface WalletBalanceAccessInterface
{
    public function deduceFund(
        string|float $amount,
        TransactionType $type,
        ?string $transactionCode = null,
        ?string $remark = null,
        ?string $reference = null,
        ?int $referenceId = null
    ): void;

    public function addFund(
        string|float $amount,
        TransactionType $type,
        ?string $transactionCode = null,
        ?string $remark = null,
        ?string $reference = null,
        ?int $referenceId = null
    ): void;

    public function checkBalance(string|float $amount);
}
