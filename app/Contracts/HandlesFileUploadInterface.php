<?php

namespace App\Contracts;

use App\Services\UploadService;
use Illuminate\Http\UploadedFile;

/**
 * Interface HandlesFileUploadInterface
 *
 * This interface defines the contract for classes that handle file uploads.
 * Classes implementing this interface must provide access to an UploadService instance.
 */
interface HandlesFileUploadInterface
{
    /**
     * Get the UploadService instance
     */
    public function getUploadService(): UploadService;

    /**
     * Upload a file and return the storage path
     *
     * @param UploadedFile $file The file to upload
     * @param bool $allowDuplicate Whether to allow duplicate files
     * @return string The storage path
     *
     * @throws \Exception If upload fails
     */
    public function uploadFileAndGetStoragePath(UploadedFile $file, bool $allowDuplicate = false): string;

    /**
     * Delete a file from the given path
     *
     * @param string $path The path to delete (can be URL, /storage/ path, or relative path)
     */
    public function deleteFileFromPath(string $path): void;
}
