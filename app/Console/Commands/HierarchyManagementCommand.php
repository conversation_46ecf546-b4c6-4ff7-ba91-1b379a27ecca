<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\UserHierarchyService;
use Illuminate\Console\Command;

class HierarchyManagementCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:hierarchy {action : Action to perform (stats|validate|fix|migrate|integrity)} {--user= : User ID for user-specific actions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage user hierarchies in the admin system';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $service = app(UserHierarchyService::class);

        switch ($action) {
            case 'stats':
                return $this->showStatistics($service);
            case 'validate':
                return $this->validateHierarchies($service);
            case 'fix':
                return $this->fixHierarchies($service);
            case 'migrate':
                return $this->migrateExistingUsers($service);
            case 'integrity':
                return $this->checkIntegrity($service);
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: stats, validate, fix, migrate, integrity');

                return 1;
        }
    }

    /**
     * Show hierarchy statistics
     */
    private function showStatistics(UserHierarchyService $service): int
    {
        $this->info('Fetching hierarchy statistics...');

        try {
            $stats = $service->getHierarchyStatistics();

            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Users', number_format($stats['total_users'])],
                    ['Users with Hierarchy', number_format($stats['users_with_hierarchy'])],
                    ['Root Users', number_format($stats['root_users'])],
                    ['Maximum Depth', $stats['max_depth']],
                    ['Average Depth', number_format($stats['average_depth'], 2)],
                ]
            );

            if (!empty($stats['depth_distribution'])) {
                $this->info("\nDepth Distribution:");
                $rows = [];
                foreach ($stats['depth_distribution'] as $depth => $count) {
                    $rows[] = ["Level {$depth}", number_format($count)];
                }
                $this->table(['Depth Level', 'User Count'], $rows);
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to fetch statistics: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Validate user hierarchies
     */
    private function validateHierarchies(UserHierarchyService $service): int
    {
        $userId = $this->option('user');

        if ($userId) {
            return $this->validateSingleUser($service, $userId);
        }

        $this->info('Validating all user hierarchies...');

        $invalidCount = 0;
        $totalCount = 0;

        User::chunk(100, function ($users) use ($service, &$invalidCount, &$totalCount) {
            foreach ($users as $user) {
                $validation = $service->validateUserHierarchy($user);
                $totalCount++;

                if (!$validation['is_valid']) {
                    $invalidCount++;
                    $this->warn("User {$user->id}: Invalid hierarchy");
                    $this->line("  Expected: {$validation['expected_hierarchy']}");
                    $this->line("  Actual: {$validation['actual_hierarchy']}");
                }
            }
        });

        $validCount = $totalCount - $invalidCount;
        $this->info("\nValidation complete:");
        $this->info("Valid hierarchies: {$validCount}");
        $this->warn("Invalid hierarchies: {$invalidCount}");
        $this->info("Total users checked: {$totalCount}");

        return $invalidCount > 0 ? 1 : 0;
    }

    /**
     * Validate a single user's hierarchy
     */
    private function validateSingleUser(UserHierarchyService $service, int $userId): int
    {
        $user = User::find($userId);

        if (!$user) {
            $this->error("User {$userId} not found");

            return 1;
        }

        $this->info("Validating hierarchy for user {$userId}...");

        $validation = $service->validateUserHierarchy($user);

        if ($validation['is_valid']) {
            $this->info("✓ User {$userId} has valid hierarchy");
            $this->line("Hierarchy: {$validation['actual_hierarchy']}");
            $this->line("Depth: {$validation['actual_depth']}");
        } else {
            $this->warn("✗ User {$userId} has invalid hierarchy");
            $this->line("Expected: {$validation['expected_hierarchy']}");
            $this->line("Actual: {$validation['actual_hierarchy']}");
            $this->line("Expected depth: {$validation['expected_depth']}");
            $this->line("Actual depth: {$validation['actual_depth']}");
        }

        return $validation['is_valid'] ? 0 : 1;
    }

    /**
     * Fix invalid hierarchies
     */
    private function fixHierarchies(UserHierarchyService $service): int
    {
        $userId = $this->option('user');

        if ($userId) {
            return $this->fixSingleUser($service, $userId);
        }

        if (!$this->confirm('This will fix all invalid hierarchies. Continue?')) {
            $this->info('Operation cancelled');

            return 0;
        }

        $this->info('Fixing invalid user hierarchies...');

        $fixedCount = 0;
        $errorCount = 0;
        $totalCount = 0;

        User::chunk(100, function ($users) use ($service, &$fixedCount, &$errorCount, &$totalCount) {
            foreach ($users as $user) {
                $validation = $service->validateUserHierarchy($user);
                $totalCount++;

                if (!$validation['is_valid']) {
                    if ($service->fixUserHierarchy($user)) {
                        $fixedCount++;
                        $this->info("✓ Fixed hierarchy for user {$user->id}");
                    } else {
                        $errorCount++;
                        $this->error("✗ Failed to fix hierarchy for user {$user->id}");
                    }
                }
            }
        });

        $this->info("\nFix operation complete:");
        $this->info("Hierarchies fixed: {$fixedCount}");
        $this->error("Fix errors: {$errorCount}");
        $this->info("Total users checked: {$totalCount}");

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Fix a single user's hierarchy
     */
    private function fixSingleUser(UserHierarchyService $service, int $userId): int
    {
        $user = User::find($userId);

        if (!$user) {
            $this->error("User {$userId} not found");

            return 1;
        }

        $this->info("Fixing hierarchy for user {$userId}...");

        $validation = $service->validateUserHierarchy($user);

        if ($validation['is_valid']) {
            $this->info("User {$userId} already has valid hierarchy");

            return 0;
        }

        if ($service->fixUserHierarchy($user)) {
            $this->info("✓ Successfully fixed hierarchy for user {$userId}");
            $this->line("Old: {$validation['actual_hierarchy']}");
            $this->line("New: {$validation['expected_hierarchy']}");

            return 0;
        } else {
            $this->error("✗ Failed to fix hierarchy for user {$userId}");

            return 1;
        }
    }

    /**
     * Migrate existing users to closure table
     */
    private function migrateExistingUsers(UserHierarchyService $service): int
    {
        if (!$this->confirm('This will populate the closure table with existing user hierarchies. Continue?')) {
            $this->info('Operation cancelled');

            return 0;
        }

        $this->info('Migrating existing users to closure table...');

        try {
            $service->migrateExistingUsers();
            $this->info('✓ Migration completed successfully');

            return 0;
        } catch (\Exception $e) {
            $this->error("✗ Migration failed: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Check closure table integrity
     */
    private function checkIntegrity(UserHierarchyService $service): int
    {
        $this->info('Checking hierarchy integrity...');

        try {
            $issues = $service->validateHierarchyIntegrity();

            if (empty($issues)) {
                $this->info('✓ No integrity issues found');

                return 0;
            }

            $this->warn('✗ Integrity issues found:');

            if (isset($issues['missing_self_references'])) {
                $count = count($issues['missing_self_references']);
                $this->line("Missing self-references: {$count} users");
                $this->line('User IDs: ' . implode(', ', array_slice($issues['missing_self_references'], 0, 10)) . ($count > 10 ? '...' : ''));
            }

            if (isset($issues['depth_inconsistencies'])) {
                $count = count($issues['depth_inconsistencies']);
                $this->line("Depth inconsistencies: {$count} users");
                foreach (array_slice($issues['depth_inconsistencies'], 0, 5) as $inconsistency) {
                    $this->line("  User {$inconsistency->id}: expected depth {$inconsistency->expected_depth}, actual {$inconsistency->actual_max_depth}");
                }
                if ($count > 5) {
                    $this->line('  ... and ' . ($count - 5) . ' more');
                }
            }

            return 1;
        } catch (\Exception $e) {
            $this->error("✗ Integrity check failed: {$e->getMessage()}");

            return 1;
        }
    }
}
