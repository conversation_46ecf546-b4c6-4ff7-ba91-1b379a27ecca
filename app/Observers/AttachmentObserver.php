<?php

declare(strict_types=1);

namespace App\Observers;

use App\Models\Attachment;
use <PERSON>rke\Admin\Support\Uuid;

class AttachmentObserver
{
    public function creating(Attachment $model)
    {
        $model->id = Uuid::toString();

        $model->update_time = time();
        $model->update_ip = request()->ip();
        $model->create_time = time();
        $model->create_ip = request()->ip();
    }

    public function updating(AttachmentModel $model)
    {
        $model->update_time = time();
        $model->update_ip = request()->ip();
    }
}
