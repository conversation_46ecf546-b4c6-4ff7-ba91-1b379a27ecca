<?php

namespace App\Observers;

use App\Models\BannerSlider;
use Illuminate\Support\Str;

class BannerSliderObserver
{
    /**
     * Handle the BannerSlider "created" event.
     */
    public function creating(BannerSlider $bannerSlider): void
    {
        $bannerSlider->id = Str::uuid();
        $bannerSlider->category = 'banner-slider';
        $bannerSlider->update_time = time();
        $bannerSlider->update_ip = request()->ip();
        $bannerSlider->create_time = time();
        $bannerSlider->create_ip = request()->ip();
    }

    /**
     * Handle the BannerSlider "updated" event.
     */
    public function updating(BannerSlider $bannerSlider): void
    {
        $bannerSlider->update_time = time();
        $bannerSlider->update_ip = request()->ip();
    }

    /**
     * Handle the BannerSlider "deleted" event.
     */
    public function deleted(BannerSlider $bannerSlider): void
    {
        //
    }

    /**
     * Handle the BannerSlider "restored" event.
     */
    public function restored(BannerSlider $bannerSlider): void
    {
        //
    }

    /**
     * Handle the BannerSlider "force deleted" event.
     */
    public function forceDeleted(BannerSlider $bannerSlider): void
    {
        //
    }
}
