<?php

namespace App\Observers;

use App\Models\LuckySpinAssignment;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class LuckySpinAssignmentObserver
{
    /**
     * Handle the LuckySpinAssignment "created" event.
     */
    public function creating(LuckySpinAssignment $luckySpinAssignment): void
    {
        $luckySpinAssignment->assigned_by = Auth::user()->id;
        $luckySpinAssignment->assigned_at = now();
        $luckySpinAssignment->remaining_chances = $luckySpinAssignment->total_chances;
    }

    public function created(LuckySpinAssignment $luckySpinAssignment): void
    {
        if ($luckySpinAssignment->isIndividual()) {
            $luckySpinAssignment->userChances()->create([
                'user_id' => $luckySpinAssignment->target_id,
                'event_id' => $luckySpinAssignment->event_id,
                'total_chances' => $luckySpinAssignment->total_chances,
                'guaranteed_rewards' => $luckySpinAssignment->guaranteed_rewards,
            ]);
        }
        if ($luckySpinAssignment->isGroup()) {
            $users = User::where('group_id', $luckySpinAssignment->target_id)->get();
            $insertData = collect();
            foreach ($users as $user) {
                $insertData->push([
                    'user_id' => $user->id,
                    'event_id' => $luckySpinAssignment->event_id,
                    'assignment_id' => $luckySpinAssignment->id,
                    'total_chances' => $luckySpinAssignment->total_chances,
                    'guaranteed_rewards' => json_encode($luckySpinAssignment->guaranteed_rewards),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            $luckySpinAssignment->userChances()->insert($insertData->toArray());
        }
    }

    /**
     * Handle the LuckySpinAssignment "updated" event.
     */
    public function updated(LuckySpinAssignment $luckySpinAssignment): void
    {
        //
    }

    /**
     * Handle the LuckySpinAssignment "deleted" event.
     */
    public function deleted(LuckySpinAssignment $luckySpinAssignment): void
    {
        //
    }

    /**
     * Handle the LuckySpinAssignment "restored" event.
     */
    public function restored(LuckySpinAssignment $luckySpinAssignment): void
    {
        //
    }

    /**
     * Handle the LuckySpinAssignment "force deleted" event.
     */
    public function forceDeleted(LuckySpinAssignment $luckySpinAssignment): void
    {
        //
    }
}
