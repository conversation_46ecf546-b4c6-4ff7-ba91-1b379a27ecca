<?php

namespace App\Entities\Auth;

use Illuminate\Support\Facades\Auth;
use <PERSON>rk<PERSON>\Admin\Facade\Permission as AuthPermission;

class Admin extends \Larke\Admin\Auth\Admin
{
    public function getProfile(): array
    {
        if (empty($this->data)) {
            return [];
        }

        $data = collect($this->data)->only([
            'id',
            'name',
            'email',
            'nickname',
            'avatar',
            'introduce',
            'groups',
            'last_active',
            'last_ip',
            'telegram_id',
            'two_fa_active',
        ]);
        $data['two_fa_active'] = $data['two_fa_active'] == 1;
        $data['groups'] = collect($data['groups'])
            ->map(function ($data) {
                return [
                    'id' => $data['id'],
                    'parentid' => $data['parentid'],
                    'title' => $data['title'],
                    'description' => $data['description'],
                ];
            });

        return $data->toArray();
    }

    public function isTelegramGlobalAdmin(): bool
    {
        return AuthPermission::enforce($this->id, 'larke-admin.telegram.is-super-admin', 'GET');
    }

    public function getSessionId(): string
    {
        return $this->data['session_id'];
    }

    public function getManageTelegramGroups(): array
    {
        if (Auth::check()) {
            $telegramGroupAccess = Auth::user()->manageTelegramGroups()->get();
            if ($telegramGroupAccess->count() > 0) {
                return $telegramGroupAccess->pluck('telegram_group_id')->toArray();
            }
        }

        return [];
    }
}
