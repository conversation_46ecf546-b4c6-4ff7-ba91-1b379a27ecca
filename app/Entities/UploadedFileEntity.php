<?php

namespace App\Entities;

class UploadedFileEntity
{
    public function __construct(
        public $name,
        public $mimeType,
        public $uploadDisk,
        public $path,
        public $extension,
        public $size,
        public $md5,
        public $sha1,
        public $driver
    ) {
    }

    public static function make($name, $mimeType, $uploadDisk, $path, $extension, $size, $md5, $sha1, $driver): UploadedFileEntity
    {
        return new self($name, $mimeType, $uploadDisk, $path, $extension, $size, $md5, $sha1, $driver);
    }
}
