<?php

namespace App\Entities\Product;

use App\Constants\PaymentType;
use App\Constants\ReturnType;
use Illuminate\Support\Carbon;

class InvestmentProductMetadataEntity
{
    public function __construct(
        public string $fund_company_name,
        public int $invest_period,
        public ReturnType $interest_return_type,
        public int $minimum_vip_level,
        public float $min_invest_amount,
        public float $max_invest_amount,
        public int $limit_purchase_items,
        public float $total_stocks,
        public Carbon $start_date_time,
        public Carbon $end_date_time,
        public bool $auto_extend,
        public float $remaining_stocks,
        public PaymentType $payment_type
    ) {
    }

    public static function make(
        string $fund_company_name,
        string $invest_period,
        string $interest_return_type,
        string $minimum_vip_level,
        string $min_invest_amount,
        string $max_invest_amount,
        string $limit_purchase_items,
        string $total_stocks,
        string $start_date_time,
        string $end_date_time,
        string $auto_extend,
        string $remaining_stocks,
        string $payment_type
    ): InvestmentProductMetadataEntity {
        return new self(
            fund_company_name: $fund_company_name,
            invest_period: (int) $invest_period,
            interest_return_type: ReturnType::getType((int) $interest_return_type),
            minimum_vip_level: (int) $minimum_vip_level,
            min_invest_amount: (float) $min_invest_amount,
            max_invest_amount: (float) $max_invest_amount,
            limit_purchase_items: (int) $limit_purchase_items,
            total_stocks: (float) $total_stocks,
            start_date_time: $start_date_time ? Carbon::parse($start_date_time) : null,
            end_date_time: $end_date_time ? Carbon::parse($end_date_time) : null,
            auto_extend: $auto_extend == 1,
            remaining_stocks: (float) $remaining_stocks,
            payment_type: PaymentType::tryFrom($payment_type)
        );
    }

    public function toArray(): array
    {
        return [
            'fund_company_name' => $this->fund_company_name,
            'invest_period' => $this->invest_period,
            'interest_return_type' => $this->interest_return_type,
            'minimum_vip_level' => $this->minimum_vip_level,
            'min_invest_amount' => $this->min_invest_amount,
            'max_invest_amount' => $this->max_invest_amount,
            'limit_purchase_items' => $this->limit_purchase_items,
            'total_stocks' => $this->total_stocks,
            'start_date_time' => $this->start_date_time,
            'end_date_time' => $this->end_date_time,
            'auto_extend' => $this->auto_extend,
            'remaining_stocks' => $this->remaining_stocks,
            'payment_type' => $this->payment_type,
        ];
    }
}
