<?php

namespace App\Exceptions;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use <PERSON>rke\Admin\Traits\ResponseJson as ResponseJsonTrait;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ExceptionRenderService
{
    use ResponseJsonTrait;

    public function __construct(private \Exception $exception)
    {
    }

    public function renderException()
    {
        switch (true) {
            case $this->exception instanceof ValidationException:
                $errors = $this->exception->errors();

                return $this->error('Validation Error', 422, $errors);
            case $this->exception instanceof ModelNotFoundException:
                return $this->error('Validation Error', 404, $this->exception->getMessage());
            case $this->exception instanceof NotFoundHttpException:
                return $this->error($this->exception->getMessage(), (int) $this->exception->getStatusCode());
            default:
                return $this->error($this->exception->getMessage(), 500);
        }
    }
}
