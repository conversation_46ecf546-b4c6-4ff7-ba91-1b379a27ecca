<?php

namespace App\Providers;

use App\Contracts\WalletServiceInterface;
use App\Services\WalletService;
use Illuminate\Support\ServiceProvider;

class BindingProvider extends ServiceProvider
{
    public $bindings = [
        WalletServiceInterface::class => WalletService::class,
    ];

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
