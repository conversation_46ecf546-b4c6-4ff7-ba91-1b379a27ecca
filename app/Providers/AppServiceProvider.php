<?php

namespace App\Providers;

use App\Models\Admin;
use App\Repositories\Contracts\PageRepositoryInterface;
use App\Repositories\PageRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Auth::viaRequest('larke-driver', function (Request $request) {
            return Admin::find(app('larke-admin.auth-admin')->getId());
        });

        // Repository bindings
        $this->app->bind(PageRepositoryInterface::class, PageRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
