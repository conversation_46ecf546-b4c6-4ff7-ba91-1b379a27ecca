<?php

namespace App\Providers;

use App\Auth\Token;
use App\Entities\Auth\Admin;
use App\Models\Attachment;
use App\Observers\AttachmentObserver;
use App\Observers\LakeAuthRulesAccessObserver;
use App\Observers\LarkeAuthGroupAccessObserver;
use App\Utils\JWT;
use Larke\Admin\Model\AuthGroupAccess;
use Larke\Admin\Model\AuthRuleAccess;
use Larke\Admin\ServiceProvider;

class LarkeOverwriteProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //        parent::register();
        $this->bindingOverwrite();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->extendObserver();
    }

    protected function bindingOverwrite(): void
    {
        $this->app->singleton('larke-admin.auth-admin', Admin::class);
        $this->app->singleton('larke-admin.auth-token', Token::class);
        $this->app->bind('larke-admin.jwt', function ($app) {
            $jwt = new JWT(
                $app['larke-admin.jwt-driver'],
                $app['larke-admin.crypto'],
                collect(config('larkeadmin.jwt'))
            );

            return $jwt;
        });
    }

    protected function extendObserver(): void
    {
        AuthGroupAccess::observe(LarkeAuthGroupAccessObserver::class);
        AuthRuleAccess::observe(LakeAuthRulesAccessObserver::class);
        \App\Models\Admin::observe(\Larke\Admin\Observer\Admin::class);
        \App\Models\AuthRuleAccess::observe(LakeAuthRulesAccessObserver::class);
        Attachment::observe(AttachmentObserver::class);
    }
}
