<?php

namespace App\Rules;

use App\Services\SystemSettingService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ExcludePhonePrefixValidationRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->passes($attribute, $value)) {
            $fail($this->message());
        }
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $prefixes = SystemSettingService::getSetting('not_allowed_phone_prefix', '162,165,167,170,171,192');
        $notAllowed = explode(',', str_replace(' ', '', trim($prefixes)));
        $pattern = '/^(' . implode('|', $notAllowed) . ')/';

        if (preg_match($pattern, $value)) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('common.errors.not_allowed_phone');
    }
}
