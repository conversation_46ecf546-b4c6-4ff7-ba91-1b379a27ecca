<?php

namespace App\Constants;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum UserBankType: string
{
    case Bank = 'bank';
    case Crypto = 'crypto';

    public static function getType(string $type): UserBankType
    {
        $message = __('common.errors.not_found', ['item' => $type]);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }
}
