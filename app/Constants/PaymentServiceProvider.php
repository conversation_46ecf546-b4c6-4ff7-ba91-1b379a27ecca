<?php

namespace App\Constants;

use App\Traits\EnumToArray;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum PaymentServiceProvider: string
{
    use EnumToArray;

    case Senhuo = 'senhuo';
    case Xiguapay = 'xiguapay';
    case Chuangwei = 'chuangwei';
    case YsPay = 'yspay';
    case JzPay = 'jzpay';
    case Mmiao = 'mmiao';

    public static function getType(string $type): PaymentServiceProvider
    {
        $message = __('common.errors.not_found', ['item' => $type]);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }
}
