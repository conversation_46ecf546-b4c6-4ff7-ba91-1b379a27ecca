<?php

namespace App\Constants;

use App\Traits\EnumToArray;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum WalletType: string
{
    use EnumToArray;

    case General = 'general';

    public static function getType(string $type): WalletType
    {
        $message = __('common.errors.not_found', ['item' => __('common.name.wallet')]);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }

    public static function isAvailableToTransfer(WalletType $from, WalletType $to): bool
    {
        return match ($from->value) {
            default => false,
        };
    }

    public static function getWithdrawableWallets(): array
    {
        return [
            WalletType::General,
        ];
    }
}
