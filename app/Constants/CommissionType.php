<?php

namespace App\Constants;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum CommissionType: string
{
    case Investment = 'investment';
    case Rebate = 'rebate';
    case ProfitRebate = 'profit_rebate';

    public static function getType(string $type): CommissionType
    {
        $message = __('common.errors.not_found', ['item' => $type]);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }
}
