<?php

namespace App\Constants\Alibaba;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum MessageTemplates: string
{
    case otp = '{"id": "SMS_298131185", "structure": ["code"]}';

    public static function getType(string $type): MessageTemplates
    {
        $message = __('common.errors.not_found', ['item' => 'template']);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }

    public static function fromKey(string $key): ?self
    {
        $message = __('common.errors.not_found', ['item' => 'template']);

        // Check if the key exists among the enum case names
        return constant("self::$key") ?? throw new BadRequestHttpException($message);
    }
}
