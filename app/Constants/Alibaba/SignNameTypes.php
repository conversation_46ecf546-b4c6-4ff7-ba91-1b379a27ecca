<?php

namespace App\Constants\Alibaba;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum SignNameTypes: string
{
    case default = '苏州峰博物业管理';

    public static function getType(string $type): SignNameTypes
    {
        $message = __('common.errors.not_found', ['item' => 'signName']);

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }

    public static function fromKey(string $key): ?self
    {
        $message = __('common.errors.not_found', ['item' => 'template']);

        // Check if the key exists among the enum case names
        return constant("self::$key") ?? throw new BadRequestHttpException($message);
    }
}
