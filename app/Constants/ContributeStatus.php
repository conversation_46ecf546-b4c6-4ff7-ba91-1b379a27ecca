<?php

namespace App\Constants;

enum ContributeStatus: int
{
    case Invalid = 0;
    case Valid = 1;

    /**
     * Get the label for the status.
     */
    public function label(): string
    {
        return match ($this) {
            self::Invalid => 'Invalid',
            self::Valid => 'Valid',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Invalid => 'Invalid',
            self::Valid => 'Valid',
        };
    }

    /**
     * Get the description for the status.
     */
    public function description(): string
    {
        return match ($this) {
            self::Invalid => 'Contribution is invalid and not counted',
            self::Valid => 'Contribution is valid and counted',
        };
    }

    /**
     * Check if the status is valid.
     */
    public function isValid(): bool
    {
        return $this === self::Valid;
    }

    /**
     * Check if the status is invalid.
     */
    public function isInvalid(): bool
    {
        return $this === self::Invalid;
    }

    /**
     * Get all status options as array.
     */
    public static function options(): array
    {
        return [
            self::Invalid->value => self::Invalid->label(),
            self::Valid->value => self::Valid->label(),
        ];
    }
}
