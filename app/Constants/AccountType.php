<?php

namespace App\Constants;

enum AccountType: string
{
    case Normal = 'normal';
    case Test = 'test';

    public function getLabel(): string
    {
        return match ($this) {
            self::Normal => trans('account.types.normal'),
            self::Test => trans('account.types.test'),
        };
    }

    public function isTest(): bool
    {
        return $this === self::Test;
    }

    public function isNormal(): bool
    {
        return $this === self::Normal;
    }
}
