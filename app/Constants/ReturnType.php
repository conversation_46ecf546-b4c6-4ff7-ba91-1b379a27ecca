<?php

namespace App\Constants;

use App\Traits\EnumToArray;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum ReturnType: int
{
    use EnumToArray;

    case Daily = 1;
    case Weekly = 2;
    case Monthly = 3;
    case OnDueDate = 4;

    public static function getType(int $type): ReturnType
    {
        $message = __('common.errors.unsupported');

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }

    public static function getDays(ReturnType $type): int
    {
        return match ($type) {
            self::Monthly => 30,
            self::Yearly => 365,
            self::Daily => 1,
        };
    }
}
