<?php

namespace App\Constants;

enum AgentType: string
{
    case Regular = 'regular';
    case RegionalSuperAgent = 'regional_super_agent';
    case SilverSuperAgent = 'silver_super_agent';
    case GoldSuperAgent = 'gold_super_agent';

    public function getLabel(): string
    {
        return match ($this) {
            self::Regular => trans('agent.types.regular'),
            self::RegionalSuperAgent => trans('agent.types.regional_super_agent'),
            self::SilverSuperAgent => trans('agent.types.silver_super_agent'),
            self::GoldSuperAgent => trans('agent.types.gold_super_agent'),
        };
    }

    public function getCommissionLevels(): int
    {
        return match ($this) {
            self::Regular => 3,
            self::RegionalSuperAgent => 4,
            self::SilverSuperAgent => 5,
            self::GoldSuperAgent => 6,
        };
    }
}
