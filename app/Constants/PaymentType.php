<?php

namespace App\Constants;

use App\Traits\EnumToArray;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum PaymentType: string
{
    use EnumToArray;

    case Wallet = 'wallet';
    case OnlineTransfer = 'transfer';
    case Both = 'both';

    public static function getType(string $type): PaymentType
    {
        $message = __('common.errors.unsupported');

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }
}
