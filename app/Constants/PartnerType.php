<?php

namespace App\Constants;

enum PartnerType: string
{
    case Agent = 'agent';
    case Elementary = 'elementary';
    case Senior = 'senior';
    case Premium = 'premium';

    public function getLabel(): string
    {
        return match ($this) {
            self::Agent => trans('partner.types.agent'),
            self::Elementary => trans('partner.types.elementary'),
            self::Senior => trans('partner.types.senior'),
            self::Premium => trans('partner.types.premium'),
        };
    }

    public function getCommissionLevels(): int
    {
        return match ($this) {
            self::Agent => 0,
            self::Elementary => 1,
            self::Senior => 2,
            self::Premium => 3,
        };
    }
}
