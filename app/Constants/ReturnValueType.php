<?php

namespace App\Constants;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

enum ReturnValueType: string
{
    case Amount = 'amount';
    case Percentage = 'percentage';

    public static function getType(string $type): ReturnValueType
    {
        $message = __('common.errors.unsupported');

        return self::tryFrom($type) ?? throw new BadRequestHttpException($message);
    }
}
