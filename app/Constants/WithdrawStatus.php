<?php

namespace App\Constants;

enum WithdrawStatus: int
{
    // 0: pending, 1: processing, 2: approved, 3: rejected
    case Pending = 0;
    case Processing = 1;
    case Approved = 2;
    case Rejected = 3;
    case Failed = 4;
    case PendingForFaceAuth = 5;

    public static function statusText(WithdrawStatus $withdrawStatus): string
    {
        return __('messages.withdraw_status.' . $withdrawStatus->name);
    }

    public static function statusTextFromValue(int $value): string
    {
        return __('messages.withdraw_status.' . self::from($value)->name);
    }
}
