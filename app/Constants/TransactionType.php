<?php

namespace App\Constants;

enum TransactionType: string
{
    case Transfer = 'transfer';
    case Receive = 'receive';
    case Deposit = 'deposit';
    case Withdraw = 'withdraw';
    case Withdraw_rollback = 'withdraw_rollback';
    case Order = 'order';
    case Rebate = 'rebate';
    case ProfitRebate = 'profit_rebate';
    case Investment = 'investment';
    case Activity_sign_in = 'activity_sign_in';
    case Activity_register = 'activity_register';
    case Activity = 'activity';
    case Subordinate_verification_invited = 'subordinate_verification_invited_reward';
    case Promotion_card_order = 'promotion_card_order';
    case OrderReward = 'order_reward';
    case System_adjustment = 'system_adjustment'; // System adjustment
    case Contribute = 'contribute';
    case VipLevelUpgrade = 'vip_level_upgrade';
    case ContributeProfit = 'contribute_profit';
    case ContributeRefund = 'contribute_refund';
    case ManualDeposit = 'manual_deposit';
    case ManualDeduce = 'manual_deduce';

    case ManualReward = 'manual_reward';

    public static function getRewardTypes(): array
    {
        return [
            self::Activity_register->value,
            self::Activity_register->value,
            self::Subordinate_verification_invited->value,
        ];
    }
}
