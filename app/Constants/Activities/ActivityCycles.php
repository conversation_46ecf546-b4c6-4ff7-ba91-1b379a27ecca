<?php

namespace App\Constants\Activities;

enum ActivityCycles: string
{
    case DAILY = 'daily';
    case ONE_TIME = 'one_time';
    //    case WEEKLY = 'weekly';
    //    case MONTHLY = 'monthly';

    public function label(): string
    {
        return match ($this) {
            self::ONE_TIME => 'one_time',
            self::DAILY => 'daily',
            //            self::MONTHLY => 'monthly',
            //            self::WEEKLY => 'weekly',
        };
    }
}
