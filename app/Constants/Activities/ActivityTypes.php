<?php

namespace App\Constants\Activities;

use App\Traits\EnumToArray;

enum ActivityTypes: string
{
    use EnumToArray;

    case SIGN_IN = 'sign_in';
    case REGISTRATION = 'registration';
    case REFERRAL = 'referral';

    public function label(): string
    {
        return match ($this) {
            self::SIGN_IN => 'sign_in',
            self::REGISTRATION => 'registration',
            self::REFERRAL => 'referral',
        };
    }
}
