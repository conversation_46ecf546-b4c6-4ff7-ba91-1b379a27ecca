<?php

namespace App\Traits;

use App\Services\UploadService;
use Illuminate\Http\UploadedFile;

/**
 * Trait HandlesFileUpload
 *
 * This trait provides file upload functionality for classes that need to handle file uploads.
 *
 * Requirements:
 * - Classes using this trait MUST implement the getUploadService() method
 * - Classes using this trait MUST inject UploadService as a dependency
 * - Classes using this trait SHOULD implement HandlesFileUploadInterface
 */
trait HandlesFileUpload
{
    /**
     * Get the UploadService instance
     *
     * Classes using this trait must implement this method to provide
     * the UploadService dependency.
     */
    abstract public function getUploadService(): UploadService;

    /**
     * Extract relative path from URL or storage path for file deletion
     * Handles full URLs, /storage/ paths, and relative paths
     */
    protected function extractRelativePathFromUrl(string $path): string
    {
        // If it starts with /storage/, remove the prefix to get the relative path
        if (str_starts_with($path, '/storage/')) {
            return substr($path, 9); // Remove '/storage/' prefix
        }

        // If it's a full URL, extract the path
        if (str_starts_with($path, 'http')) {
            $parsedUrl = parse_url($path);
            if (isset($parsedUrl['path'])) {
                $relativePath = $parsedUrl['path'];
                // Remove /storage/ prefix if present
                if (str_starts_with($relativePath, '/storage/')) {
                    $relativePath = substr($relativePath, 9);
                }

                return $relativePath;
            }
        }

        // If it's already a relative path, return as is
        return $path;
    }

    /**
     * Upload a file and return the storage path
     *
     * @param UploadedFile $file The file to upload
     * @param bool $allowDuplicate Whether to allow duplicate files
     * @return string The storage path (e.g., /storage/uploads/files/filename.ext)
     *
     * @throws \Exception If upload fails
     */
    public function uploadFileAndGetStoragePath(UploadedFile $file, bool $allowDuplicate = false): string
    {
        // Upload file using UploadService
        $uploadedFile = $this->getUploadService()->uploadFile($file, $allowDuplicate);

        if (!$uploadedFile) {
            throw new \Exception('Failed to upload file');
        }

        // Get the storage path (e.g., /storage/uploads/files/filename.ext)
        return '/storage/' . $uploadedFile->path;
    }

    /**
     * Delete a file using the UploadService with path conversion
     *
     * @param string $path The path to delete (can be URL, /storage/ path, or relative path)
     */
    public function deleteFileFromPath(string $path): void
    {
        // Convert URL to relative path if necessary for deletion
        $pathToDelete = $this->extractRelativePathFromUrl($path);

        // Delete file using UploadService
        $this->getUploadService()->deleteFile($pathToDelete);
    }
}
