<?php

namespace App\Traits;

use Carbon\Carbon;

trait BaseListFilters
{
    protected int $page;

    protected int $limit;

    protected int $offset;

    protected int $start;

    protected ?string $startDate;

    protected ?string $endDate;

    protected ?string $orderField;

    protected ?string $orderType;

    protected array $filters = [];

    public function setBaseFilters(array $params = []): void
    {
        $this->page = $params['page'] ?? 1;
        $this->limit = $params['limit'] ?? 10;
        $this->offset = $params['offset'] ?? 0;
        $this->start = $params['start'] ?? 0;
        $this->startDate = isset($params['time'][0]) ? Carbon::parse($params['time'][0])->startOfDay() : null;
        $this->endDate = isset($params['time'][1]) ? Carbon::parse($params['time'][1])->endOfDay() : null;
        $this->orderField = $params['larke_order_field'] ?? null;
        $this->orderType = $params['larke_order_type'] ?? null;
        $this->filters = $params['filters'] ?? [];
    }
}
