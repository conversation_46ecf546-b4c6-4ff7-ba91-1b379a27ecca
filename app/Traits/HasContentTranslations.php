<?php

namespace App\Traits;

use App\Models\ContentTranslation;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;

trait HasContentTranslations
{
    public function translations(): MorphMany
    {
        return $this->morphMany(ContentTranslation::class, 'entity');
    }

    public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();

        if ($locale === 'en') {
            return $this->{$field};
        }

        $translation = ContentTranslation::getTranslation(
            static::class,
            $this->id,
            $field,
            $locale
        );

        return $translation ?? $this->{$field};
    }

    public function setTranslation(string $field, string $locale, string $content): void
    {
        ContentTranslation::setTranslation(
            static::class,
            $this->id,
            $field,
            $locale,
            $content
        );
    }

    public function setTranslations(string $field, array $translations): void
    {
        foreach ($translations as $locale => $content) {
            $this->setTranslation($field, $locale, $content);
        }
    }

    public function getTranslations(string $field): array
    {
        $translations = ['en' => $this->{$field}];

        $dbTranslations = $this->translations()
            ->where('field_key', $field)
            ->get(['locale', 'content']);

        foreach ($dbTranslations as $translation) {
            $translations[$translation->locale] = $translation->content;
        }

        return $translations;
    }

    public function getAllTranslations(): array
    {
        $result = [];

        foreach ($this->getTranslatableFields() as $field) {
            $result[$field] = $this->getTranslations($field);
        }

        return $result;
    }

    public function hasTranslation(string $field, string $locale): bool
    {
        if ($locale === 'en') {
            return !empty($this->{$field});
        }

        return ContentTranslation::where('entity_type', static::class)
            ->where('entity_id', $this->id)
            ->where('field_key', $field)
            ->where('locale', $locale)
            ->exists();
    }

    public function deleteTranslations(): void
    {
        ContentTranslation::deleteTranslations(static::class, $this->id);
    }

    public function updateTranslationsFromRequest(array $data): void
    {
        DB::transaction(function () use ($data) {
            $hasChanges = false;
            // Get supported locales from config, with fallback for testing
            $supportedLocales = app()->bound('config')
                ? config('locales.locales.supported', ['en'])
                : ['en', 'zh_cn'];

            foreach ($this->getTranslatableFields() as $field) {
                if (isset($data[$field])) {
                    // Delete existing translations for this field
                    ContentTranslation::deleteFieldTranslations(static::class, $this->id, $field);

                    // Prepare bulk insert data
                    $translationsToInsert = [];

                    if (is_array($data[$field])) {
                        // Handle array data (multiple locales provided)
                        foreach ($data[$field] as $locale => $content) {
                            if ($locale === 'en') {
                                // Update main model field for English content
                                if ($this->{$field} !== $content) {
                                    $this->{$field} = $content;
                                    $hasChanges = true;
                                }
                            }

                            // Add to bulk insert if content is valid
                            if (is_string($content) && trim($content) !== '') {
                                $translationsToInsert[] = [
                                    'entity_type' => static::class,
                                    'entity_id' => $this->id,
                                    'field_key' => $field,
                                    'locale' => $locale,
                                    'content' => $content,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ];
                            }
                        }

                        // Create empty translation entries for missing supported locales
                        $providedLocales = array_keys($data[$field]);
                        $missingLocales = array_diff($supportedLocales, $providedLocales);

                        foreach ($missingLocales as $locale) {
                            // Use fallback content: prefer English content, or empty string as last resort
                            $fallbackContent = $this->{$field} ?? '';
                            if ($fallbackContent !== '') {
                                $translationsToInsert[] = [
                                    'entity_type' => static::class,
                                    'entity_id' => $this->id,
                                    'field_key' => $field,
                                    'locale' => $locale,
                                    'content' => $fallbackContent,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ];
                            }
                        }
                    } else {
                        // Handle string data (single value for all locales)
                        if ($this->{$field} !== $data[$field]) {
                            $this->{$field} = $data[$field];
                            $hasChanges = true;
                        }

                        // Create translations for all supported locales
                        foreach ($supportedLocales as $locale) {
                            $translationsToInsert[] = [
                                'entity_type' => static::class,
                                'entity_id' => $this->id,
                                'field_key' => $field,
                                'locale' => $locale,
                                'content' => $data[$field],
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                    }

                    // Bulk insert translations
                    if (!empty($translationsToInsert)) {
                        ContentTranslation::insert($translationsToInsert);
                    }
                }
            }

            // Save main model if there are changes
            if ($hasChanges) {
                $this->save();
            }
        });
    }

    protected function getTranslatableFields(): array
    {
        return property_exists($this, 'translatable') ? $this->translatable : [];
    }

    protected static function bootHasContentTranslations(): void
    {
        static::deleting(function ($model) {
            $model->deleteTranslations();
        });
    }
}
