<?php

namespace App\Traits;

use App\Services\UserHierarchyService;
use Illuminate\Support\Facades\Log;

/**
 * Trait ManagesHierarchy
 *
 * Provides hierarchy management functionality for models that have hierarchical relationships.
 * This version includes closure table sync functionality adapted for admin-api environment.
 *
 * This trait manages user hierarchies by:
 * - Automatically syncing hierarchy data when users are created or updated
 * - Using synchronous sync for admin interfaces (no queued processing)
 * - Providing utility methods to calculate hierarchy depth and detect deep hierarchies
 * - Maintaining closure table consistency for efficient hierarchy queries
 */
trait ManagesHierarchy
{
    /**
     * Boot the ManagesHierarchy trait
     *
     * Registers model event listeners for automatic hierarchy synchronization.
     * This method is automatically called by <PERSON><PERSON> when the trait is used.
     */
    protected static function bootManagesHierarchy(): void
    {
        // Handle hierarchy sync on user creation and updates
        static::created(function ($model) {
            static::syncHierarchyForUser($model);
        });

        static::updated(function ($model) {
            // Re-sync if parent_id or hierarchy changes
            if ($model->isDirty(['parent_id', 'hierarchy'])) {
                static::syncHierarchyForUser($model);
            }
        });
    }

    /**
     * Sync user hierarchy for admin-api (synchronous approach)
     *
     * Uses synchronous sync suitable for admin interfaces where immediate
     * feedback is preferred over background processing.
     *
     * @param \App\Models\User $user The user whose hierarchy needs to be synced
     */
    protected static function syncHierarchyForUser($user): void
    {
        try {
            app(UserHierarchyService::class)->syncUserHierarchy($user);
        } catch (\Exception $e) {
            // Log error but don't fail the user creation/update
            Log::error('Hierarchy sync failed in admin-api', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get the depth of user's hierarchy
     *
     * Calculates the hierarchy depth by counting the pipe-separated segments
     * in the hierarchy string (e.g., "1|2|3|" = depth 3).
     *
     * @return int The hierarchy depth (0 if no hierarchy)
     */
    public function getHierarchyDepth(): int
    {
        if (!$this->hierarchy) {
            return 0;
        }

        $trimmed = trim($this->hierarchy, '|');

        return $trimmed ? count(explode('|', $trimmed)) : 0;
    }

    /**
     * Check if user has deep hierarchy (6+ levels)
     *
     * Deep hierarchies may require special handling in admin interfaces
     * to prevent performance issues during display or management operations.
     *
     * @return bool True if hierarchy depth > 5
     */
    public function hasDeepHierarchy(): bool
    {
        return $this->getHierarchyDepth() > 5;
    }

    /**
     * Get hierarchy as array of user IDs
     *
     * Converts the pipe-separated hierarchy string into an array of user IDs
     * for easier manipulation and display in admin interfaces.
     *
     * @return array Array of user IDs in the hierarchy
     */
    public function getHierarchyArray(): array
    {
        if (!$this->hierarchy) {
            return [];
        }

        $trimmed = trim($this->hierarchy, '|');

        return $trimmed ? explode('|', $trimmed) : [];
    }

    /**
     * Build hierarchy string from array of user IDs
     *
     * Creates a properly formatted hierarchy string from an array of user IDs.
     * Useful for hierarchy manipulation in admin operations.
     *
     * @param array $userIds Array of user IDs to build hierarchy from
     * @return string Formatted hierarchy string (e.g., "1|2|3|")
     */
    public function buildHierarchyString(array $userIds): string
    {
        if (empty($userIds)) {
            return '';
        }

        // Filter out null/empty values and ensure all are strings
        $filteredIds = array_filter($userIds, function ($id) {
            return !is_null($id) && $id !== '';
        });

        return empty($filteredIds) ? '' : implode('|', $filteredIds) . '|';
    }

    /**
     * Get parent user IDs from hierarchy
     *
     * Returns all parent user IDs in the hierarchy chain.
     * Useful for admin interfaces that need to display parent relationships.
     *
     * @return array Array of parent user IDs
     */
    public function getParentIds(): array
    {
        return $this->getHierarchyArray();
    }

    /**
     * Check if user is descendant of another user
     *
     * Determines if this user is a descendant of the specified user ID
     * by checking if the user ID exists in the hierarchy chain.
     *
     * @param int $userId User ID to check for ancestry
     * @return bool True if the user is a descendant of the specified user
     */
    public function isDescendantOf(int $userId): bool
    {
        return in_array((string) $userId, $this->getHierarchyArray());
    }

    /**
     * Get hierarchy level (distance from root)
     *
     * Returns the level in the hierarchy where 0 is root level,
     * 1 is first level children, etc.
     *
     * @return int Hierarchy level (0-based)
     */
    public function getHierarchyLevel(): int
    {
        return $this->getHierarchyDepth();
    }

    /**
     * Validate hierarchy string format
     *
     * Checks if the hierarchy string follows the expected format
     * and contains valid user IDs.
     *
     * @param string|null $hierarchy Hierarchy string to validate
     * @return bool True if hierarchy format is valid
     */
    public function validateHierarchyFormat(?string $hierarchy = null): bool
    {
        $hierarchyToCheck = $hierarchy ?? $this->hierarchy;

        if (empty($hierarchyToCheck)) {
            return true; // Empty hierarchy is valid
        }

        // Check if it ends with |
        if (!str_ends_with($hierarchyToCheck, '|')) {
            return false;
        }

        // Check if all segments are numeric (user IDs)
        $segments = explode('|', rtrim($hierarchyToCheck, '|'));
        foreach ($segments as $segment) {
            if (!is_numeric($segment) || (int) $segment <= 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get hierarchy display string for admin interfaces
     *
     * Returns a human-readable hierarchy path for display in admin interfaces.
     * Optionally includes user names if a callback is provided.
     *
     * @param callable|null $userNameCallback Optional callback to get user names by ID
     * @return string Formatted hierarchy display string
     */
    public function getHierarchyDisplayString(?callable $userNameCallback = null): string
    {
        $parentIds = $this->getParentIds();

        if (empty($parentIds)) {
            return 'Root Level';
        }

        if ($userNameCallback) {
            $names = array_map($userNameCallback, $parentIds);

            return implode(' → ', $names) . ' → ' . ($this->name ?? "User #{$this->id}");
        }

        return 'Level ' . count($parentIds) . ' (Parent IDs: ' . implode(', ', $parentIds) . ')';
    }
}
