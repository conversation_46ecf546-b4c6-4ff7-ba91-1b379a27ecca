<?php

namespace App\Services;

use App\Constants\DepositStatus;
use App\Http\Requests\HourlyWithdrawRequest;
use App\Models\DepositRequests;
use App\Traits\BaseListFilters;
use App\Utils\Gmt8;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DepositService
{
    use BaseListFilters;

    private ?string $user_id = null;

    private ?string $code = null;

    private $amount;

    private ?string $start_date = null;

    private ?string $end_date = null;

    private ?int $status = null;

    private int $pageSize;

    public function getHourlyTransactions(HourlyWithdrawRequest $request): array
    {
        $date = $request->date ?? Gmt8::today();
        $todayRange = Gmt8::toUtcTodayRange($date);
        // Base query
        $query = DepositRequests::query()
            ->select([
                DB::raw('EXTRACT(HOUR FROM created_at) as hour'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
            ])
            ->whereBetween('created_at', $todayRange)
            ->where('status', 'success');

        // Apply status filter if provided
        // $query->where('status', DepositStatus::Approved->value);

        // Group by hour and get results
        $results = $query->groupBy(DB::raw('EXTRACT(HOUR FROM created_at)'))
            ->get()
            ->each(function ($item) {
                $item->hour = Carbon::now()->setHour($item->hour)->tz('Asia/Taipei')->hour;
            })
            ->keyBy('hour');

        // Initialize array with all 24 hours
        $hourlyData = collect(range(0, 23))->map(function ($hour) use ($results) {
            $hourData = $results->get($hour);

            return [
                'hour' => $hour,
                'total_amount' => $hourData ? (float) $hourData->total_amount : 0,
                'transaction_count' => $hourData ? $hourData->transaction_count : 0,
                'unique_users' => $hourData ? $hourData->unique_users : 0,
            ];
        })->values();

        // Calculate totals
        $totals = $hourlyData->reduce(function ($carry, $item) {
            return [
                'total_amount' => $carry['total_amount'] + $item['total_amount'],
                'total_transactions' => $carry['total_transactions'] + $item['transaction_count'],
            ];
        }, ['total_amount' => 0, 'total_transactions' => 0]);

        return [
            'date' => $date,
            'hourly_data' => $hourlyData,
            'summary' => [
                'total_amount' => $totals['total_amount'],
                'total_transactions' => $totals['total_transactions'],
            ],
        ];
    }
}
