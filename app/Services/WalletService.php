<?php

namespace App\Services;

use App\Abstracts\WalletServiceAbstract;
use App\Constants\WalletType;
use App\Contracts\WalletServiceInterface;
use App\Models\User;
use App\Models\Wallet;
use App\Repositories\WalletRepository;
use App\Services\Wallet\GeneralWalletService;
use Illuminate\Database\Eloquent\Collection;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class WalletService implements WalletServiceInterface
{
    public function __construct(private WalletRepository $repository)
    {
        //
    }

    public static function make(User $user, WalletType $type): WalletServiceAbstract
    {
        $wallet = WalletRepository::getWalletByUser($user->id, $type->value);
        if (!$wallet) {
            $message = __('common.errors.not_found', ['item' => __('common.name.wallet')]);
            throw new BadRequestHttpException($message);
        }

        return match ($type) {
            WalletType::General => new GeneralWalletService($wallet),
        };
    }

    public function getUserWallets(User $user): Collection
    {
        return $this->repository->getUserWallets($user->id);
    }

    public function getWalletDetails(User $user, WalletType $type): ?Wallet
    {
        return $this->repository->getWalletByUser($user->id, $type->value);
    }

    public function createWallets(User $user): bool
    {
        return $this->repository->createWallets($user);
    }
}
