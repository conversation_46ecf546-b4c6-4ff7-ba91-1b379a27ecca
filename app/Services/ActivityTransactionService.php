<?php

namespace App\Services;

use App\Models\ActivityTransaction;
use App\Traits\BaseListFilters;
use App\Utils\Gmt8;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ActivityTransactionService
{
    use BaseListFilters;

    public function index(array $params): array
    {
        $query = ActivityTransaction::query()->with(['activity', 'user']);
        $this->applyFilters($query, $params);

        $countQuery = clone $query;
        $total = $countQuery->count();

        $query->orderBy('id', 'desc');
        $list = $query->offset($this->offset)->limit($this->limit)->get();

        return [
            'start' => $this->start,
            'limit' => $this->limit,
            'total' => $total,
            'list' => $list,
        ];
    }

    public function detail($id): ?ActivityTransaction
    {
        return ActivityTransaction::query()->find($id);
    }

    private function applyFilters(Builder $query, array $params): void
    {
        $this->setBaseFilters($params);

        if (isset($this->filters['phone'])) {
            $query->where('phone', 'like', '%' . $this->filters['phone'] . '%');
        }
        if (isset($this->filters['name'])) {
            $query->whereRelation('user', 'name', 'like', "%{$this->filters['name']}%");
        }
        if (isset($this->filters['activity_type'])) {
            $query->where('activity_type', $this->filters['activity_type']);
        }
        if (isset($this->filters['activity_cycle'])) {
            $query->where('activity_cycle', $this->filters['activity_cycle']);
        }
        if (isset($this->filters['created_from'])) {
            $query->where('created_at', '>=', Gmt8::toUtc($this->filters['created_from']));
        }
        if (isset($this->filters['created_to'])) {
            $query->where('created_at', '<=', Gmt8::toUtc($this->filters['created_to']));
        }
    }

    public function transactionReport($filters)
    {
        $this->setBaseFilters($filters);
        $query = ActivityTransaction::query()
            ->select([
                DB::raw('DATE(activity_transactions.created_at) as groupCreatedAt'),
                'activities.name as activity_name',
                DB::raw('COUNT(DISTINCT activity_transactions.user_id) as dailyTotalUser'),
                DB::raw('SUM(activity_transactions.activity_amount) as dailyTotalAmount'),
                DB::raw('SUM(activity_transactions.activity_amount) / COUNT(DISTINCT activity_transactions.user_id) as dailyAvgAmount'),
            ])
            ->join('activities', 'activities.id', '=', 'activity_transactions.activity_id')
            ->groupBy(DB::raw('DATE(activity_transactions.created_at)'), 'activities.name')
            ->orderBy(DB::raw('DATE(activity_transactions.created_at)'), 'desc');
        // Apply date range filter if provided
        if (!empty($this->filters['created_from'])) {
            $query->whereDate('activity_transactions.created_at', '>=', $this->filters['created_from']);
        }

        if (!empty($this->filters['created_to'])) {
            $query->whereDate('activity_transactions.created_at', '<=', $this->filters['created_to']);
        }

        // Apply activity filter if provided
        if (!empty($filters['activity_id'])) {
            $query->where('activity_transactions.activity_id', $filters['activity_id']);
        }

        // Order by date descending

        $countQuery = clone $query;
        $total = $countQuery->count();
        $list = $query->offset($this->offset)->limit($this->limit)->get();

        return [
            'start' => $this->start,
            'limit' => $this->limit,
            'total' => $total,
            'list' => $list,
        ];
    }
}
