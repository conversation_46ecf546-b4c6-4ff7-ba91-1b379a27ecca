<?php

namespace App\Services;

use App\Models\Announcement;
use App\Models\User;
use App\Repositories\AnnouncementsRepository;
use App\Utils\Gmt8;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AnnouncementService
{
    public function create(array $data): ?Announcement
    {
        // Handle non-translatable fields
        if (!isset($data['type'])) {
            $data['type'] = 'notification';
        }

        $data['force_popup'] = $data['force_popup'] ?? false;
        if ($data['force_popup']) {
            $data['release_time'] = Carbon::now()->format('Y-m-d H:i:s');
        } else {
            $data['release_time'] = Gmt8::toUtc($data['release_time']);
        }

        if (!empty($data['user_phone'])) {
            $user = User::where('phone', $data['user_phone'])->first();
            $data['user_id'] = $user?->id;
        }

        // Prepare main model data (extract English values for main table)
        $mainData = $data;
        foreach (['title', 'message', 'button_text'] as $field) {
            if (isset($data[$field]) && is_array($data[$field])) {
                $mainData[$field] = $data[$field]['en'] ?? '';
            }
        }

        $announcement = AnnouncementsRepository::create($mainData);

        // Only set translations if the announcement was created successfully and has an ID
        if ($announcement && $announcement->id) {
            $announcement->updateTranslationsFromRequest($data);
        }

        return $announcement;
    }

    public function update(array $data, Announcement $announcement): bool
    {
        if ($announcement->status == 1) {
            return true;
        }

        // Handle non-translatable fields
        if (!isset($data['type'])) {
            $data['type'] = 'notification';
        }

        $data['force_popup'] = $data['force_popup'] ?? false;
        if ($data['force_popup']) {
            $data['release_time'] = Carbon::now()->format('Y-m-d H:i:s');
        } else {
            $data['release_time'] = Gmt8::toUtc($data['release_time']);
        }

        if (!empty($data['user_phone'])) {
            $user = User::where('phone', $data['user_phone'])->first();
            $data['user_id'] = $user?->id;
        }

        // Prepare main model data (extract English values for main table)
        $mainData = [];

        // Add non-translatable fields
        foreach (['type', 'user_id', 'subordinate_type', 'status', 'release_time', 'force_popup'] as $field) {
            if (isset($data[$field])) {
                $mainData[$field] = $data[$field];
            }
        }

        // Handle translatable fields - extract English content for main model
        foreach (['title', 'message', 'button_text'] as $field) {
            if (isset($data[$field])) {
                if (is_array($data[$field]) && isset($data[$field]['en'])) {
                    $mainData[$field] = $data[$field]['en'];
                } elseif (is_string($data[$field])) {
                    $mainData[$field] = $data[$field];
                }
            }
        }

        // Update main model fields first
        $result = AnnouncementsRepository::update($mainData, $announcement);

        // Handle translations for all languages (including creating new ones)
        if ($announcement->id && $result) {
            $announcement->updateTranslationsFromRequest($data);
        }

        return $result;
    }

    public function getList(Request $request, ?string $locale = null): array
    {
        $type = $request->input('type', null);
        $request = json_decode(data_get($request, 'page'));
        $page = (int) data_get($request, 'page', 1);
        $limit = (int) data_get($request, 'pageSize', 10);

        $offset = ($page - 1) * $limit;
        /** @var Announcement $query */
        $query = AnnouncementsRepository::getList();

        // If locale is specified, filter announcements that have content in that locale
        if ($locale) {
            $query->whereHas('translations', function ($q) use ($locale) {
                $q->where('locale', $locale)
                    ->whereIn('field_key', ['title', 'message', 'button_text']);
            });
        }

        if ($type) {
            $query->where('type', $type);
        } else {
            $query->where('type', 'notification');
        }
        $totalQuery = clone $query;
        $announcements = $query->offset($offset)->limit($limit)->get();

        // Transform the data to include translations
        if ($locale) {
            $transformedList = $announcements->map(function ($announcement) use ($locale) {
                return [
                    'id' => $announcement->id,
                    'type' => $announcement->type,
                    'user_id' => $announcement->user_id,
                    'subordinate_type' => $announcement->subordinate_type,
                    'status' => $announcement->status,
                    'release_time' => $announcement->release_time,
                    'force_popup' => $announcement->force_popup,
                    'title' => $announcement->getTitle($locale),
                    'message' => $announcement->getMessage($locale),
                    'button_text' => $announcement->getButtonText($locale),
                    'created_at' => $announcement->created_at,
                    'updated_at' => $announcement->updated_at,
                    // Include translations object for frontend compatibility
                    'translations' => [
                        'title' => $announcement->getTranslations('title'),
                        'message' => $announcement->getTranslations('message'),
                        'button_text' => $announcement->getTranslations('button_text'),
                    ],
                    // Include user relationship if loaded
                    'user' => $announcement->user,
                ];
            });
        } else {
            // Return with all translations for no specific locale
            $transformedList = $announcements->map(function ($announcement) {
                return $announcement->toArrayWithTranslations();
            });
        }

        return [
            'current_page' => $page,
            'start' => $offset,
            'limit' => $limit,
            'total' => $totalQuery->count(),
            'list' => $transformedList,
        ];
    }

    public function getAnnouncement(string $newsId): ?Announcement
    {
        return AnnouncementsRepository::getAnnouncement($newsId);
    }
}
