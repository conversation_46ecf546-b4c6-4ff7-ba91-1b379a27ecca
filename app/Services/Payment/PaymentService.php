<?php

namespace App\Services\Payment;

use App\Constants\WithdrawRequestStatus;
use App\Models\PaymentChannel;
use App\Models\Withdraw;
use App\Models\WithdrawRequest;
use Illuminate\Support\Str;

class PaymentService
{
    public function prepareWithdrawRequest(Withdraw $withdraw): void
    {
        /** @var PaymentChannel $paymentChannel */
        $paymentChannel = PaymentChannel::withTrashed()->findOrFail($withdraw->payment_channel_id);
        $userBank = $withdraw->userBank;

        $withdrawRequest = new WithdrawRequest();
        $withdrawRequest->provider = $paymentChannel->service_provider;
        $withdrawRequest->order_external_id = Str::uuid()->toString();
        $withdrawRequest->withdraw_id = $withdraw->id;
        $withdrawRequest->user_id = $withdraw->user_id;
        $withdrawRequest->wallet_id = $withdraw->wallet_id;
        $withdrawRequest->payment_channel_id = $paymentChannel->id;
        // $withdrawRequest->amount = $withdraw->amount;
        $withdrawRequest->amount = $withdraw->actual_amount;
        $withdrawRequest->fee = $params['fee'] ?? 0;
        $withdrawRequest->status = WithdrawRequestStatus::Pending->value;
        $withdrawRequest->bank_account_name = $userBank->account_name;
        $withdrawRequest->bank_account_number = $userBank->account;
        $withdrawRequest->bank_name = $userBank->name;
        $withdrawRequest->phone = $withdraw->user->phone;

        $withdrawRequest->save();
    }
}
