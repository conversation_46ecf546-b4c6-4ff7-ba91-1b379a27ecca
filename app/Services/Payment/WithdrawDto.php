<?php

namespace App\Services\Payment;

use App\Models\PaymentChannel;
use App\Models\User;
use App\Models\Wallet;
use App\Utils\Math;
use Illuminate\Support\Str;

class WithdrawDto
{
    public string $externalId;

    public string $currency;

    public string $clientIp;

    public function __construct(
        public User $user,
        public Wallet $wallet,
        public PaymentChannel $paymentChannel,
        public string $amount,
        public ?string $type = 'deposit',
        public array $metadata = []
    ) {
        $this->externalId = Str::uuid()->toString();
        $this->amount = Math::formatNumber($this->amount, true);
        $this->currency = 'CNY';
        $this->clientIp = request()->header('X-Real-Ip') ?? request()->ip();
        $this->metadata = array_merge($this->metadata, [
            'client_ip' => $this->clientIp,
        ]);
    }
}
