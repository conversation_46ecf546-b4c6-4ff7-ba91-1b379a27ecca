<?php

namespace App\Services;

use App\Constants\WithdrawStatus;
use App\Http\Requests\HourlyWithdrawRequest;
use App\Models\Commission;
use App\Models\DepositRequests;
use App\Models\Withdraw;
use App\Services\Senhuo\SenhuoService;
use App\Traits\BaseListFilters;
use App\Utils\Gmt8;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class WithdrawService
{
    use BaseListFilters;

    private ?string $user_id = null;

    private ?string $code = null;

    private $amount;

    private ?string $start_date = null;

    private ?string $end_date = null;

    private ?int $status = null;

    private int $pageSize;

    private SenhuoService $senhuoService;

    public function __construct(
        SenhuoService $senhuoService
    ) {
        $this->senhuoService = $senhuoService;
    }

    public function getList(array $filters = []): LengthAwarePaginator
    {
        set_time_limit(30);
        $query = Withdraw::query()
            ->from('withdraws as w')
            ->leftJoin('users as u', 'u.id', '=', 'w.user_id')
            ->leftJoin('user_banks as ub', 'ub.id', '=', 'w.user_bank_id')
            ->leftJoin('wallets', 'wallets.id', '=', 'w.wallet_id');
        // Apply filters
        $this->applyFilters($query, $filters);
        $this->applySorting($query, request());
        $paginate = $query->clone()->paginate($filters['pageSize'] ?? 10);
        // First CTE: User Withdraw Stats
        $userWithdrawStats = Withdraw::query()
            ->select('user_id')
            ->selectRaw('SUM(amount) as accumulated_withdraw_amount')
            ->selectRaw('COUNT(id) as total_withdraw')
            ->selectRaw('COUNT(CASE WHEN status = 2 THEN 1 END) as total_withdraw_approved_time')
            ->groupBy('user_id');

        // Third CTE: User Wallet Profits
        //        $userWalletProfits = DB::table('wallets as w')
        //            ->leftJoin('transaction_logs as tl', function ($join) {
        //                $join->on('w.id', '=', 'tl.wallet_id')
        //                    ->where('tl.user_id', '=', DB::raw('w.user_id'));
        //            })
        //            ->select('w.user_id')
        //            ->selectRaw('SUM(CASE WHEN w.type = ? AND tl.type = ? THEN tl.amount ELSE 0 END) as general_wallet_profit', ['general', 'investment'])
        //            ->selectRaw('SUM(CASE WHEN w.type = ? AND tl.type = ? THEN tl.amount ELSE 0 END) as commission_wallet_profit', ['commission', 'investment'])
        //            ->selectRaw('SUM(CASE WHEN w.type = ? AND tl.type = ? THEN tl.amount ELSE 0 END) as saudi_wallet_profit', ['saudi', 'investment'])
        //            ->groupBy('w.user_id');
        $selfClaimed = Commission::whereType('investment')
//            ->where('category_id', 2)
            ->where('created_at', '>=', '2024-12-11 16:00:00')
            ->whereNotNull('processed_at')
            ->groupBy('user_id')
            ->select(['user_id', DB::raw('count(id) as self_claimed')]);

        // $depositQuery = Deposit::groupBy('user_id')
        //     ->addSelect(['user_id', DB::raw('sum(amount) as deposit_amount'), DB::raw('count(id) as deposit_number')]);
        // $orderQuery = Order::where('payment_type', 'direct')
        //     ->where('status', OrderStatus::Processed->value)
        //     ->groupBy('user_id')
        //     ->addSelect(['user_id', DB::raw('sum(amount) as deposit_amount'), DB::raw('count(id) as deposit_number')]);

        //        $totalDeposit = DepositRequests::query()
        //            ->groupBy('user_id')
        //            ->where('status', 'success')
        //            ->select(['user_id', DB::raw('sum(amount) as total_deposits')]);
        // Main Query
        $query = $query
            ->leftJoin('users as p', 'u.parent_id', '=', 'p.id')
            ->leftJoin('payment_channels as pc', 'pc.id', '=', 'w.payment_channel_id')
//            ->leftJoin('senhuo_withdraws as sw', 'w.id', '=', 'sw.withdraw_id')
            // ->leftJoin('deposits as d', 'w.user_id', '=', 'd.user_id')
//            ->leftJoinSub($totalDeposit, 'd', 'w.user_id', '=', 'd.user_id')
            ->leftJoinSub($userWithdrawStats, 'uws', function ($join) {
                $join->on('w.user_id', '=', 'uws.user_id');
            })
//            ->leftJoinSub($userWalletProfits, 'uwp', function ($join) {
//                $join->on('w.user_id', '=', 'uwp.user_id');
//            })
            ->leftJoinSub($selfClaimed, 'self_claimed', 'w.user_id', '=', 'self_claimed.user_id')
            ->select([
                'w.code',
                'w.wallet_id',
                'w.id',
                'w.notes',
                'w.created_at',
                'u.id as user_id',
                'u.phone as mobile_number',
                'u.name as name',
                'ub.account',
                'ub.account_name',
                'ub.name as bank_name',
                'ub.id as bank_id',
                'ub.type as payment_type',
                'p.phone as parent_phone',
                'w.amount',
                'w.fee',
                'w.actual_amount',
                'w.status',
                'w.metadata',
                'w.payment_channel_id',
                'pc.name as payment_channel',
                'pc.service_provider',
                'pc.merchant_name',
                'uws.accumulated_withdraw_amount',
                'uws.total_withdraw',
                'uws.total_withdraw_approved_time',
                //                DB::raw('COALESCE(d.total_deposits, 0) as total_deposits'),
                //                'uwp.general_wallet_profit',
                //                'uwp.commission_wallet_profit',
                //                'uwp.saudi_wallet_profit',
                DB::raw('COALESCE(self_claimed.self_claimed, 0) as self_claimed'),
            ])
            ->groupBy([
                'w.id', 'w.notes', 'w.created_at', 'u.id', 'u.phone',
                'ub.account', 'ub.account_name', 'ub.name', 'p.phone',
                'w.amount', 'w.fee', 'w.actual_amount', 'w.status',
                'w.payment_channel_id', 'pc.name', 'pc.service_provider',
                'pc.merchant_name', 'uws.accumulated_withdraw_amount',
                'uws.total_withdraw',
                //                'uwp.general_wallet_profit',
                //                'uwp.commission_wallet_profit',
                //                'uwp.saudi_wallet_profit',
                //                'd.total_deposits',
            ])
            ->orderBy('w.created_at');

        $query->with(['wallet', 'user']);
        // dont show record of locked user
        // dd($paginate->currentPage());
        $withdraws = $query->offset(($paginate->currentPage() - 1) * $filters['pageSize'] ?? 10)->limit($filters['pageSize'] ?? 10)->get();
        $parentIds = $withdraws->unique('user_id')->pluck('user_id');
        $arr = implode(',', $parentIds->toArray());
        // $claimedCommission = empty($arr) ? collect() : collect(DB::select(
        //     <<<SQL
        //     select subordinate_claimed.*
        //     from
        //     (select claimed.parent as user_id, count(children) as claimed_subordinates, sum(claimed_time) as subordinates_claimed_times
        //     from (select pa.user_id       as parent,
        //                  children.user_id as children,
        //                  count(c.id)      as claimed_time
        //           from team_statistic as pa
        //                    left join team_statistic as children on CASE
        //                                                                WHEN children.hierarchy LIKE CONCAT('%|', pa.user_id, '|') THEN true
        //                                                                WHEN children.hierarchy LIKE CONCAT(pa.user_id, '|%') THEN true
        //                                                                WHEN children.hierarchy LIKE CONCAT('%|', pa.user_id, '|%') THEN true
        //               END
        //                    left join commissions as c on c.user_id = children.user_id
        //               and c.type = 'investment'
        //               and c.category_id = 2
        //               and c.created_at >= '2024-12-11 16::00:00'
        //               and c.processed_at is not null
        //           where pa.user_id in ($arr)
        //             and c.id is not null
        //           group by pa.user_id, children) as claimed
        //     group by parent) as subordinate_claimed;
        // SQL
        // ));
        $claimedCommission = collect();
        $withdraws->each(function ($item) use ($claimedCommission) {
            $data = $claimedCommission->where('user_id', $item->user_id)->first();
            if ($data) {
                $item->claimed_subordinates = $data->claimed_subordinates ?? 0;
                $item->subordinates_claimed_times = $data->subordinates_claimed_times ?? 0;
            } else {
                $item->claimed_subordinates = 0;
                $item->subordinates_claimed_times = 0;
            }
        });
        $paginate->setCollection($withdraws);

        return $paginate;
    }

    protected function params(array $params): void
    {
        $this->setBaseFilters($params);
        $this->user_id = $params['user_id'] ?? null;
        $this->code = $params['code'] ?? null;
        $this->amount = $params['amount'] ?? null;
        $this->start_date = $params['start_date'] ?? null;
        $this->end_date = $params['end_date'] ?? null;
        $this->status = $params['status'] ?? null;
        $this->pageSize = $params['pageSize'] ?? null;
    }

    protected function applyFilters($query, array $filters = [])
    {
        // Phone number filter (searches both user and parent phone)
        if (!empty($filters['bank_mobile_number'])) {
            $phone = $filters['bank_mobile_number'];
            $query->where(function ($q) use ($phone) {
                $q->where('u.phone', 'LIKE', "%{$phone}%");
            });
        }

        // Status filter
        if (!empty($filters['status'])) {
            $filters['status'] = WithdrawStatus::tryFromString($filters['status']);
            $query->where('w.status', $filters['status']);
        }

        // Account number filter
        if (!empty($filters['bank_account'])) {
            $query->where('ub.account', 'LIKE', "%{$filters['bank_account']}%");
        }

        // Account name filter
        if (!empty($filters['bank_name'])) {
            $query->where('u.id_name', 'LIKE', "%{$filters['bank_name']}%");
        }

        // Date range filter
        if (!empty($filters['start_date'])) {
            $query->where('w.created_at', '>=', $filters['start_date']);
        }
        if (!empty($filters['end_date'])) {
            $query->where('w.created_at', '<=', $filters['end_date']);
        }

        // Amount range filter
        if (!empty($filters['amount'])) {
            $query->where('w.amount', $filters['amount']);
        }

        // wallet filter
        if (!empty($filters['wallet_type'])) {
            $query->where('wallets.type', $filters['wallet_type']);
        }

        return $query;
    }

    protected function applySorting($query, $request)
    {
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        $sortableFields = [
            'amount' => 'w.amount',
            'created_at' => 'w.created_at',
            'status' => 'w.status',
        ];

        if (isset($sortableFields[$sortBy])) {
            $query->orderBy($sortableFields[$sortBy], $sortDirection);
        }

        return $query;
    }

    public function getHourlyTransactions(HourlyWithdrawRequest $request): array
    {
        $date = $request->date ?? Gmt8::today();
        $todayRange = Gmt8::toUtcTodayRange($date);

        // Base query
        $query = Withdraw::query()
            ->select([
                DB::raw('EXTRACT(HOUR FROM created_at) as hour'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('SUM(actual_amount) as total_amount'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
            ])
            ->whereBetween('created_at', $todayRange);

        // Apply status filter if provided
        $query->where('status', WithdrawStatus::Approved->value);

        // Group by hour and get results
        $results = $query->groupBy(DB::raw('EXTRACT(HOUR FROM created_at)'))
            ->get()
            ->each(function ($item) {
                $item->hour = Carbon::now()->setHour($item->hour)->tz('Asia/Taipei')->hour;
            })
            ->keyBy('hour');

        // Initialize array with all 24 hours
        $hourlyData = collect(range(0, 23))->map(function ($hour) use ($results) {
            $hourData = $results->get($hour);

            return [
                'hour' => $hour,
                'total_amount' => $hourData ? (float) $hourData->total_amount : 0,
                'transaction_count' => $hourData ? $hourData->transaction_count : 0,
                'unique_users' => $hourData ? $hourData->unique_users : 0,
            ];
        })->values();

        // Calculate totals
        $totals = $hourlyData->reduce(function ($carry, $item) {
            return [
                'total_amount' => $carry['total_amount'] + $item['total_amount'],
                'total_transactions' => $carry['total_transactions'] + $item['transaction_count'],
            ];
        }, ['total_amount' => 0, 'total_transactions' => 0]);

        return [
            'date' => $date,
            'hourly_data' => $hourlyData,
            'summary' => [
                'total_amount' => $totals['total_amount'],
                'total_transactions' => $totals['total_transactions'],
            ],
        ];
    }
}
