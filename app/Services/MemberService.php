<?php

namespace App\Services;

use App\Constants\ModifyBalanceType;
use App\Constants\TransactionType;
use App\Constants\WalletType;
use App\Exceptions\Wallet\InvalidAmount;
use App\Models\Coupon;
use App\Models\PromotionCard;
use App\Models\TeamStatistic;
use App\Models\User;
use App\Models\UserCoupon;
use App\Models\UserLog;
use App\Models\UserPromotionCard;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class MemberService
{
    public function getListMember(array $filter = [], int $pageSize = 10)
    {
        // Initial   ize base query
        $userQuery = $this->initializeBaseQuery();

        // Apply filters
        $userQuery = $this->applyFilters($userQuery, $filter);

        if (isset($filter['sortBy'], $filter['sort'])) {
            $userQuery = $this->applySorting($userQuery, $filter['sortBy'], $filter['sort']);
        } else {
            $userQuery->orderBy('users.created_at', 'desc');
        }

        return $userQuery->with(['group', 'wallet'])->paginate($pageSize);
    }

    /**
     * Get the query builder for testing purposes
     * This method is used to check the SQL query that would be generated
     */
    public function getQueryForTesting(array $filter = []): Builder
    {
        // Initialize base query
        $userQuery = $this->initializeBaseQuery();

        // Apply filters
        $userQuery = $this->applyFilters($userQuery, $filter);

        // Add team statistics
        $userQuery = $this->addTeamStatistics($userQuery);
        $lastLoginInfo = $this->getLastLoginInfo();

        // Apply sorting if specified
        if (isset($filter['sortBy'], $filter['sort'])) {
            $userQuery = $this->applySorting($userQuery, $filter['sortBy'], $filter['sort']);
        }

        // Finalize query with all necessary joins and selects
        $userQuery = $this->finalizeQuery($userQuery, $lastLoginInfo);

        return $userQuery;
    }

    /**
     * @throws InvalidAmount|\App\Exceptions\Wallet\InsufficientBalanceException
     */
    public function modifyBalance(
        User $user,
        float|string $amount,
        ModifyBalanceType $type,
        WalletType $walletType,
        ?string $remark = null
    ) {
        $key = 'admin_manual_deposit_' . $user->id . '_' . $amount;

        return Cache::lock($key, 5)->get(function () use ($user, $amount, $type, $walletType, $remark) {
            /* @var \App\Services\Wallet\GeneralWalletService $generalWallet */
            $wallet = WalletService::make($user, $walletType);

            try {
                DB::beginTransaction();
                switch ($type) {
                    case ModifyBalanceType::Add:
                        $wallet->addFund(
                            amount: $amount,
                            type: TransactionType::ManualDeposit,
                            remark: $remark
                                ? $remark
                                : __('transactions.balance.manual_deposit', [
                                    'by' => Auth::user()->name,
                                ])
                        );
                        break;
                    case ModifyBalanceType::Reduce:
                        $wallet->deduceFund(
                            amount: $amount,
                            type: TransactionType::ManualDeduce,
                            remark: $remark
                                ? $remark
                                : __('transactions.balance.manual_deduct', [
                                    'by' => Auth::user()->name,
                                ])
                        );
                        break;
                    case ModifyBalanceType::Reward:
                        $oldBalance = $wallet->getBalance();
                        $wallet->fluctuateReward($amount);
                        $wallet->saveTransactionLogs(
                            amount: $amount,
                            balance: $wallet->getBalance(),
                            oldBalance: $oldBalance,
                            type: TransactionType::ManualReward,
                            remark: $remark
                                ? $remark
                                : __('transactions.balance.manual_reward', [
                                    'by' => Auth::user()->name,
                                ])
                        );
                        break;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        });
    }

    public function getMemberByPhone($phone)
    {
        return User::where('phone', '=', $phone)->active()->first();
    }

    public function getClaimedData($member)
    {
        $member->subordinates_claimed_times = 0;
        $member->claimed_subordinates = 0;

        return $member;
        // Get subordinate IDs for this member
        $subordinateIds = TeamStatistic::select('user_id')
            ->where(function ($query) use ($member) {
                $query->where('hierarchy', 'like', $member->id . '|%')
                    ->orWhere('hierarchy', 'like', '%|' . $member->id . '|%')
                    ->orWhere('hierarchy', 'like', '%|' . $member->id);
            })
            ->pluck('user_id')
            ->toArray();

        if (empty($subordinateIds)) {
            $member->subordinates_claimed_times = 0;
            $member->claimed_subordinates = 0;

            return $member;
        }
        // Get claimed data for subordinates
        $claimedData = DB::select("
            SELECT
                subordinate_claimed.*
            FROM (
                SELECT
                    claimed.parent as user_id,
                    COUNT(children) as claimed_subordinates,
                    SUM(claimed_time) as subordinates_claimed_times
                FROM (
                    SELECT
                        pa.user_id as parent,
                        children.user_id as children,
                        COUNT(c.id) as claimed_time
                    FROM team_statistic as pa
                    LEFT JOIN team_statistic as children ON
                        children.hierarchy LIKE CONCAT('%|', pa.user_id, '|') OR
                        children.hierarchy LIKE CONCAT(pa.user_id, '|%') OR
                        children.hierarchy LIKE CONCAT('%|', pa.user_id, '|%')
                    LEFT JOIN commissions as c ON
                        c.user_id = children.user_id AND
                        c.type = 'investment' AND
                        c.category_id = 2 AND
                        c.created_at >= '2024-12-11 16:00:00' AND
                        c.processed_at IS NOT NULL
                    WHERE pa.user_id = ?
                        AND c.id IS NOT NULL
                    GROUP BY pa.user_id, children.user_id
                ) as claimed
                GROUP BY parent
            ) as subordinate_claimed

        ", [$member->id]);

        // Add the results to the member object
        if (!empty($claimedData)) {
            $claimInfo = $claimedData[0];
            $member->subordinates_claimed_times = $claimInfo->subordinates_claimed_times;
            $member->claimed_subordinates = $claimInfo->claimed_subordinates;
        } else {
            $member->subordinates_claimed_times = 0;
            $member->claimed_subordinates = 0;
        }

        return $member;
    }

    public function sendPromotionCard(array $ids, $promotionCardId, $quantity)
    {
        $users = User::whereIn('id', $ids)->get();
        $promotionCard = PromotionCard::find($promotionCardId);
        if (!$promotionCard) {
            throw new \Exception('Promotion card not found');
        }
        try {
            foreach ($users as $user) {
                for ($i = 0; $i < $quantity; $i++) {
                    UserPromotionCard::create([
                        'user_id' => $user->id,
                        'promotion_card_id' => $promotionCard->id,
                        'source' => 'gift',
                    ]);
                }
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function sendCoupon(array $ids, int $couponId, int $quantity): void
    {
        $coupon = Coupon::find($couponId);
        User::whereIn('id', $ids)->chunk(100, function ($users) use ($coupon, $quantity) {
            foreach ($users as $user) {
                $coupons = array_fill(0, $quantity, [
                    'user_id' => $user->id,
                    'coupon_id' => $coupon->id,
                    'created_at' => now(),
                ]);
                UserCoupon::insert($coupons);
            }
        });
    }

    /**
     * Initialize the base query for member listing
     */
    private function initializeBaseQuery(): Builder
    {
        return User::query();
    }

    /**
     * Apply filters to the query based on the provided filter array
     */
    private function applyFilters(Builder $query, array $filter): Builder
    {
        // Filter by member type (agent or individual)
        if (isset($filter['member_type'])) {
            $query = $this->filterByMemberType($query, $filter['member_type']);
        }

        // Filter by member level
        if (isset($filter['member_level'])) {
            $query->where('users.level', '=', $filter['member_level']);
        }

        // Filter by VIP level
        if (!empty($filter['vip_level_id'])) {
            $query->where('users.vip_level_id', $filter['vip_level_id']);
        }

        // Filter subordinates by agent phone number
        if (isset($filter['agent_number'])) {
            $query = $this->filterByAgentNumber($query, $filter['agent_number']);
        }

        // Filter by phone number
        if (isset($filter['phone'])) {
            $query->where('users.phone', 'like', "%{$filter['phone']}%");
        }

        // filter by email
        if (isset($filter['email'])) {
            $query->where('users.email', 'like', "%{$filter['email']}%");
        }

        // Filter by partner type
        if (!empty($filter['partner_type'])) {
            $query->where('users.partner_type', $filter['partner_type']);
        }

        return $query;
    }

    /**
     * Filter query by member type (agent or individual)
     */
    private function filterByMemberType(Builder $query, string $memberType): Builder
    {
        $query->leftJoin('users as member', 'member.parent_id', '=', 'users.id');

        switch ($memberType) {
            case 'agent':
                $query->having(DB::raw('COUNT(member.id)'), '>', 0);
                break;
            case 'individual':
                $query->having(DB::raw('COUNT(member.id)'), '=', 0);
                break;
        }

        $query->select(['users.*', DB::raw('COUNT(member.id) as members')]);

        return $query;
    }

    /**
     * Filter subordinates by agent phone number
     */
    private function filterByAgentNumber(Builder $query, string $agentNumber): Builder
    {
        return $query->whereIn('users.parent_id', function ($subQuery) use ($agentNumber) {
            $subQuery->select('id')
                ->from('users')
                ->where('phone', $agentNumber);
        });
    }

    /**
     * Get last login information subquery
     */
    private function getLastLoginInfo(): Builder
    {
        $lastLoginQuery = UserLog::groupBy('user_id')
            ->select(['user_id', DB::raw('max(time) as last_login')]);

        return UserLog::from('user_logs as l')->joinSub($lastLoginQuery, 'w', function (JoinClause $join) {
            $join->on('l.time', '=', 'w.last_login')
                ->whereRaw('l.user_id = w.user_id');
        })
            ->select([
                'l.user_id',
                DB::raw('l.time as last_login'),
                DB::raw('l.ip as ip'),
                DB::raw('l.ip_country as ip_country'),
            ]);
    }

    /**
     * Apply sorting to the query
     */
    private function applySorting(Builder $query, string $sortBy, string $sortOrder): Builder
    {
        // Check if sorting by wallet fields
        if (preg_match('/^wallets\.(\w+)\.(\w+)$/', $sortBy, $matches)) {
            return $this->sortByWalletField($query, $matches[1], $matches[2], $sortOrder);
        }

        // Sort by team statistics fields
        $teamStatFields = [
            'total_members',
            'total_investment',
            'investment_commission',
            'team_investment_commission',
        ];

        if (in_array($sortBy, $teamStatFields)) {
            return $query->orderBy('ts.' . $sortBy, $sortOrder);
        }

        // Sort by last login
        if ($sortBy === 'last_login') {
            return $query->orderBy('login.' . $sortBy, $sortOrder);
        }

        // Sort by user fields
        return $query->orderBy('users.' . $sortBy, $sortOrder);
    }

    /**
     * Sort query by wallet field
     */
    private function sortByWalletField(Builder $query, string $walletType, string $walletColumn, string $sortOrder): Builder
    {
        // Join the wallets table with the specified type
        $query->leftJoin('wallets', function ($join) use ($walletType) {
            $join->on('wallets.user_id', '=', 'users.id')
                ->where('wallets.type', '=', $walletType);
        });

        // Apply sorting based on the wallet column
        $query->orderBy(DB::raw("MAX(wallets.$walletColumn)"), $sortOrder);
        $query->groupBy('wallets.user_id');

        return $query;
    }

    /**
     * Finalize the query with all necessary joins and selects
     */
    private function finalizeQuery(Builder $query, Builder $lastLoginInfo): Builder
    {
        $query->groupBy('users.id', 'last_login', 'ip_country', 'ip');

        $query->leftJoinSub($lastLoginInfo, 'login', function ($join) {
            $join->on('login.user_id', '=', 'users.id');
        })
            ->select(['users.*'])
            ->addSelect(DB::raw('login.last_login as last_login'))
            ->addSelect([DB::raw('login.ip_country as ip_country'), DB::raw('login.ip as ip')])
            ->addSelect([
                DB::raw('ts.investment_commission as investment_commission'),
                DB::raw('ts.team_investment_commission as team_investment_commission'),
            ]);

        $query->with(['teamStatistic', 'wallets', 'parent', 'group']);

        return $query;
    }

    /**
     * Get results with pagination offset
     */
    private function getResultsWithPagination(Builder $query, LengthAwarePaginator $pagination, int $pageSize): Collection
    {
        return $query->offset(($pagination->currentPage() - 1) * $pageSize)
            ->limit($pageSize)
            ->get();
    }

    /**
     * Process the collection of members
     */
    private function processCollection(Collection $collection): void
    {
        foreach ($collection as $member) {
            /* @var User $member */
            $member->setRelation('wallets', $member->wallets->keyBy('type'));
            // Uncomment if needed: $this->getClaimedData($member);
        }
    }
}
