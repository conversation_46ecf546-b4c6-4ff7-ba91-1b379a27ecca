<?php

namespace App\Services;

use App\Models\TeamDailyReport;
use App\Models\User;
use App\Models\UserHierarchy;
use App\Utils\Math;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class TeamReportService
{
    private Builder $query;
    private $defaultValue = [
        'register_user' => 0,
        'deposit_user' => 0,
        'first_deposit_user' => 0,
        'contribute_user' => 0,
        'deposit_amount' => 0,
        'withdraw_amount' => 0,
        'reward_amount' => 0,
        'contribute_amount' => 0,
        'investment_interest_amount' => 0,
        'commission_amount' => 0,
    ];

    public function getTeamStats(User $user, string $startTime, string $endTime, string $period): array
    {
        if (in_array($period, ['today', 'this_week', 'this_month'])) {
            $todayData = collect(DB::select('call get_daily_report(?, ?, ?)', [$startTime, $endTime, $user->id]));
        } else {
            $todayData = null;
        }
        if ($period != 'today') {
            $teamStats = UserHierarchy::query()
                ->from('user_hierarchies as uh')
                ->where('uh.ancestor_id', $user->id)
                ->where('uh.depth', '<=', 1)
                ->join('user_hierarchies as child', 'uh.descendant_id', '=', 'child.ancestor_id')
                ->join('team_daily_reports as td', function ($join) use ($startTime, $endTime) {
                    $join->on('td.user_id', '=', 'child.descendant_id')
                        ->whereBetween('td.report_date', [$startTime, $endTime]);
                })
                ->join('users as u', 'u.id', '=', 'uh.descendant_id')
                // may need to filter test account here
                ->groupBy('uh.descendant_id')
                ->select([
                    'uh.descendant_id as user_id',
                    'u.email',
                    DB::raw('sum(td.is_registered) as register_user'),
                    DB::raw('sum(td.is_deposit) as deposit_user'),
                    DB::raw('sum(td.is_first_deposit) as first_deposit_user'),
                    DB::raw('sum(td.is_contribute) as contribute_user'),
                    DB::raw('sum(td.deposit_amount) as deposit_amount'),
                    DB::raw('sum(td.withdraw_amount) as withdraw_amount'),
                    DB::raw('sum(td.reward_amount) as reward_amount'),
                    DB::raw('sum(td.contribute_profit_amount) as contribute_amount'),
                    DB::raw('sum(td.investment_interest_amount) as investment_interest_amount'),
                    DB::raw('sum(td.commission_amount) as commission_amount'),
                ])
                ->get();
            $overallStats = $teamStats->firstWhere('user_id', '=', $user->id)?->toArray() ?? $this->defaultValue;
            $teamDetails = $teamStats->filter(function ($item) use ($user) {
                return $item->user_id != $user->id;
            })->values();
            $personalStats = $this->getPersonalStats($user, $startTime, $endTime);
            // team stats equal deduct personal stats from overall stats by using Utils Math::sub
            $teamStats = $overallStats;
            foreach ($personalStats as $key => $value) {
                if (is_numeric($value)) {
                    $teamStats[$key] = Math::sub($teamStats[$key], $value);
                }
            }
        } else {
            $overallStats = (array) $todayData->firstWhere('user_type', '=', 'parent_summary') ?? $this->defaultValue;
            $personalStats = (array) $todayData->firstWhere('user_type', '=', 'self') ?? $this->defaultValue;
            $teamDetails = $todayData->filter(function ($item) use ($user) {
                return $item->user_id != $user->id;
            })->values();

            // team stats equal deduct personal stats from overall stats by using Utils Math::sub
            $teamStats = $overallStats;
            foreach ($personalStats as $key => $value) {
                if (is_numeric($value)) {
                    $teamStats[$key] = Math::sub($teamStats[$key], $value);
                }
            }
        }


        // todo need today team stats too

        return [
            'overallStats' => $overallStats,
            'team_stats' => $teamStats,
            'teamDetails' => $teamDetails,
        ];
    }

    private function getPersonalStats(User $user, string $startTime, string $endTime)
    {
        $result = TeamDailyReport::query()
            ->where('user_id', $user->id)
            ->whereBetween('report_date', [$startTime, $endTime])
            ->join('users as u', 'u.id', '=', 'user_id')
            ->select([
                DB::raw('sum(is_registered) as register_user'),
                DB::raw('sum(is_deposit) as deposit_user'),
                DB::raw('sum(is_first_deposit) as first_deposit_user'),
                DB::raw('sum(is_contribute) as contribute_user'),
                DB::raw('sum(deposit_amount) as deposit_amount'),
                DB::raw('sum(withdraw_amount) as withdraw_amount'),
                DB::raw('sum(reward_amount) as reward_amount'),
                DB::raw('sum(contribute_profit_amount) as contribute_amount'),
                DB::raw('sum(investment_interest_amount) as investment_interest_amount'),
                DB::raw('sum(commission_amount) as commission_amount'),
            ])
            ->groupBy('user_id')
            ->first();
        if (!$result) {
            return $this->defaultValue;
        }

        return $result->toArray();
    }
    /*
        public function getDirectSubordinateStats(User $user, string $startTime, string $endTime): array
        {
            $this->prepareQuery($user);

            $results = $this->query->where('user_hierarchies.depth', 1)
                ->leftJoin('wallets', 'wallets.user_id', '=', 'users.id')
                ->leftJoin('deposits', function ($join) use ($startTime, $endTime) {
                    $join->on('deposits.user_id', '=', 'users.id')
                        ->where('deposits.status', DepositStatus::Approved->value)
                        ->whereBetween('deposits.created_at', [$startTime, $endTime]);
                })
                ->leftJoin('deposits as first_deposits', function ($join) use ($startTime, $endTime) {
                    $join->on('first_deposits.user_id', '=', 'users.id')
                        ->where('first_deposits.status', DepositStatus::Approved->value)
                        ->whereBetween('first_deposits.created_at', [$startTime, $endTime])
                        ->whereRaw('NOT EXISTS (
                         SELECT 1 FROM deposits d2
                         WHERE d2.user_id = users.id
                         AND d2.created_at < ?
                     )', [$startTime]);
                })
                ->leftJoin('contributes', function ($join) use ($startTime, $endTime) {
                    $join->on('contributes.user_id', '=', 'users.id')
                        ->where('contributes.status', ContributeStatus::Invalid->value)
                        ->whereBetween('contributes.created_at', [$startTime, $endTime]);
                })
                ->leftJoin('withdraws', function ($join) {
                    $join->on('withdraws.wallet_id', '=', 'wallets.id')
                        ->where('withdraws.status', WithdrawStatus::Approved->value);
                })
                ->leftJoin('investment_logs', 'investment_logs.user_id', '=', 'users.id')
                ->leftJoin('contributes as profit_contributes', function ($join) {
                    $join->on('profit_contributes.user_id', '=', 'users.id')
                        ->where('profit_contributes.status', ContributeStatus::Invalid->value);
                })
                ->select([
                    'users.id as user_id',
                    'users.name as user_name',
                    'users.phone as user_phone',
                    'users.email as user_email',
                    DB::raw('CASE WHEN users.created_at BETWEEN ? AND ? THEN 1 ELSE 0 END as registered_users'),
                    DB::raw('CASE WHEN COUNT(DISTINCT deposits.id) > 0 THEN 1 ELSE 0 END as deposit_users'),
                    DB::raw('CASE WHEN COUNT(DISTINCT first_deposits.id) > 0 THEN 1 ELSE 0 END as first_deposit_users'),
                    DB::raw('CASE WHEN COUNT(DISTINCT contributes.id) > 0 THEN 1 ELSE 0 END as contribute_users'),
                    DB::raw('COALESCE(SUM(wallets.balance), 0) as remain_balance'),
                    DB::raw('COALESCE(SUM(wallets.deposit), 0) as total_deposit'),
                    DB::raw('COALESCE(SUM(wallets.reward), 0) as total_reward'),
                    DB::raw('COALESCE(SUM(withdraws.amount), 0) as total_withdraw'),
                    DB::raw('COALESCE(SUM(investment_logs.amount), 0) as total_investment'),
                    DB::raw('COALESCE(SUM(profit_contributes.profit), 0) as total_contribute'),
                ])
                ->addBinding([$startTime, $endTime], 'select')
                ->groupBy([
                    'users.id',
                    'users.name',
                    'users.phone',
                    'users.email',
                    'users.created_at',
                ])
                ->get();

            return $results->toArray();
        }

        public function getDirectRegisteredUsers(string $startTime, string $endTime)
        {
            $registeredUsers = $this->query->where('user_hierarchies.depth', 1)
                ->whereBetween('users.created_at', [$startTime, $endTime])
                ->selectRaw('count(distinct users.id) as registered_users')
                ->first();

            if ($registeredUsers) {
                return $registeredUsers->registered_users;
            }

            return 0;
        }

        public function getDirectDepositUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('deposits', 'deposits.user_id', '=', 'users.id')
                ->whereBetween('deposits.created_at', [$startTime, $endTime])
                ->where('deposits.status', DepositStatus::Approved->value)
                ->select(DB::raw('count(distinct users.id) as deposit_users'))
                ->first();

            if ($result) {
                return $result->deposit_users;
            }

            return 0;
        }

        public function getDirectFirstDepositUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('deposits', 'deposits.user_id', '=', 'users.id')
                ->whereBetween('deposits.created_at', [$startTime, $endTime])
                ->where('deposits.status', DepositStatus::Approved->value)
                ->whereRaw('NOT EXISTS (
            SELECT 1 FROM deposits d2
            WHERE d2.user_id = users.id
            AND d2.created_at < ?
        )', [$startTime])
                ->selectRaw('count(distinct users.id) as first_deposit_users')
                ->first();

            if ($result) {
                return $result->first_deposit_users;
            }

            return 0;
        }

        public function getDirectContributeUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('contributes', 'contributes.user_id', '=', 'users.id')
                ->whereBetween('contributes.created_at', [$startTime, $endTime])
                ->where('contributes.status', ContributeStatus::Invalid->value)
                ->selectRaw('count(distinct users.id) as contribute_users')
                ->first();

            if ($result) {
                return $result->contribute_users;
            }

            return 0;
        }

        public function getDirectWalletStats()
        {
            $walletStats = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('wallets', 'wallets.user_id', '=', 'users.id')
                ->select([
                    DB::raw('SUM(wallets.balance) as remain_balance'),
                    DB::raw('SUM(wallets.deposit) as total_deposit'),
                    DB::raw('SUM(wallets.reward) as total_reward'),
                ])
                ->first();

            return $walletStats;
        }

        public function getDirectTotalWithdraw()
        {
            $withdrawStats = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('wallets', 'wallets.user_id', '=', 'users.id')
                ->join('withdraws', 'withdraws.wallet_id', '=', 'wallets.id')
                ->where('withdraws.status', WithdrawStatus::Approved->value)
                ->select([
                    DB::raw('SUM(withdraws.amount) as total_withdraw'),
                ])
                ->first();

            return $withdrawStats->total_withdraw ?? 0;
        }

        public function getDirectTotalInvestment()
        {
            $investmentStats = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('investment_logs', 'investment_logs.user_id', '=', 'users.id')
                ->select([
                    DB::raw('SUM(investment_logs.amount) as total_investment'),
                ])
                ->first();

            return $investmentStats->total_investment ?? 0;
        }

        public function getDirectTotalContribute()
        {
            $contributeStats = $this->query->clone()->where('user_hierarchies.depth', 1)
                ->join('contributes', 'contributes.user_id', '=', 'users.id')
                ->where('contributes.status', ContributeStatus::Invalid->value)
                ->select([
                    DB::raw('SUM(contributes.profit) as total_contribute'),
                ])
                ->first();

            return $contributeStats->total_contribute ?? 0;
        }

        public function getRegisteredUsers(string $startTime, string $endTime)
        {
            $registeredUsers = $this->query->where('user_hierarchies.depth', '<=', 1)
                ->whereBetween('users.created_at', [$startTime, $endTime])
                ->selectRaw('count(distinct users.id) as registered_users')
                ->first();
            if ($registeredUsers) {
                return $registeredUsers->registered_users;
            }

            return 0;
        }

        public function getDepositUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', '<=', 1)
                ->join('deposits', 'deposits.user_id', '=', 'users.id')
                ->whereBetween('deposits.created_at', [$startTime, $endTime])
                ->where('deposits.status', DepositStatus::Approved->value)
                ->select(DB::raw('count(distinct users.id) as deposit_users'))
                ->first();
            if ($result) {
                return $result->deposit_users;
            }

            return 0;
        }

        // depositUsers

        public function getFirstDepositUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', '<=', 1)
                ->join('deposits', 'deposits.user_id', '=', 'users.id')
                ->whereBetween('deposits.created_at', [$startTime, $endTime])
                ->where('deposits.status', DepositStatus::Approved->value)
                ->whereRaw('NOT EXISTS (
            SELECT 1 FROM deposits d2
            WHERE d2.user_id = users.id
            AND d2.created_at < ?
        )', [$startTime])
                ->selectRaw('count(distinct users.id) as first_deposit_users')
                ->first();
            if ($result) {
                return $result->first_deposit_users;
            }

            return 0;
        }

        // first Deposit users

        public function getContributeUsers(string $startTime, string $endTime)
        {
            $result = $this->query->clone()->where('user_hierarchies.depth', '<=', 1)
                ->join('contributes', 'contributes.user_id', '=', 'users.id')
                ->whereBetween('contributes.created_at', [$startTime, $endTime])
                ->where('contributes.status', ContributeStatus::Invalid->value)  // todo: check with sir dean about that case
                ->selectRaw('count(distinct users.id) as contribute_users')
                ->first();
            if ($result) {
                return $result->contribute_users;
            }

            return 0;
        }

        public function getTeamWalletStats()
        {
            $walletStats = $this->query->clone()
                ->join('wallets', 'wallets.user_id', '=', 'users.id')
                ->select([
                    DB::raw('SUM(wallets.balance) as remain_balance'),
                    DB::raw('SUM(wallets.deposit) as total_deposit'),
                    DB::raw('SUM(wallets.reward) as total_reward'),
                ])
                ->first();

            return $walletStats;
        }

        public function getTeamTotalWithdraw()
        {
            $withdrawStats = $this->query->clone()
                ->join('wallets', 'wallets.user_id', '=', 'users.id')
                ->join('withdraws', 'withdraws.wallet_id', '=', 'wallets.id')
                ->where('withdraws.status', WithdrawStatus::Approved->value)
                ->select([
                    DB::raw('SUM(withdraws.amount) as total_withdraw'),
                ])
                ->first();

            return $withdrawStats->total_withdraw ?? 0;
        }

        // get team total withdraw

        public function getTeamTotalInvestment()
        {
            // dont need to care about the status because that is the profit of investment and always is claimable
            $investmentStats = $this->query->clone()
                ->join('investment_logs', 'investment_logs.user_id', '=', 'users.id')
                ->select([
                    DB::raw('SUM(investment_logs.amount) as total_investment'),
                ])
                ->first();

            return $investmentStats->total_investment ?? 0;
        }

        public function getTeamTotalContribute()
        {
            // only count the contribute that already expired
            $contributeStats = $this->query->clone()
                ->join('contributes', 'contributes.user_id', '=', 'users.id')
                ->where('contributes.status', ContributeStatus::Invalid->value)
                ->select([
                    DB::raw('SUM(contributes.profit) as total_contribute'),
                ])
                ->first();

            return $contributeStats->total_contribute ?? 0;
        }

        protected function prepareQuery(User $user)
        {
            $this->query = $user->subordinates()->getQuery();
        }*/
}
