<?php

namespace App\Services;

use App\Constants\DateTimeFormat;
use App\Models\Activity;
use App\Models\ActivityTransaction;
use App\Traits\BaseListFilters;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ActivityService
{
    use BaseListFilters;

    private ?string $name = null;

    private ?string $type = null;

    private ?int $amount = 0;

    private ?string $wallet = null;

    private ?string $cycle = null;

    private ?string $activeDate = null;

    private int $id;

    private ?string $currencies = null;

    private ?string $refund_rates = null;

    private ?int $claim_duration = null;

    private ?string $start_date = null;

    private ?string $end_date = null;

    private ?int $status = null;

    private ?int $min_threshold;

    private ?float $daily_reward;

    private ?int $consecutive_day;

    private ?float $consecutive_reward;

    private ?int $min_vip_level;

    public function index(array $params): array
    {
        $this->params($params);
        $query = Activity::query();
        if ($this->activeDate) {
            $query->where('start_date', '<=', $this->activeDate);
            $query->where('end_date', '>=', $this->activeDate);
        }
        if ($this->name) {
            $query->where('name', 'like', '%' . $this->name . '%');
        }
        if ($this->type) {
            $query->where('type', $this->type);
        }
        if ($this->cycle) {
            $query->where('cycle', $this->cycle);
        }
        $query->where('deleted_at', null);

        $countQuery = clone $query;
        $total = $countQuery->count();
        $list = $query->offset($this->offset)->limit($this->limit)->orderBy('id', 'desc')->get();

        return [
            'start' => $this->start,
            'limit' => $this->limit,
            'total' => $total,
            'list' => $list,
        ];
    }

    public function create(array $params): bool
    {
        $this->params($params);
        try {
            $record = $this->prepareRecord();
            $record['version'] = 1;

            $record['rates'] = [
                'wallet' => $record['wallet'],
                'reward' => $record['amount'] ?? 0,
                'daily_reward' => $record['daily_reward'],
                'consecutive_day' => $record['consecutive_day'],
                'consecutive_reward' => $record['consecutive_reward'],
                'min_vip_level' => $record['min_vip_level'],
            ];

            unset($record['wallet'], $record['amount']);

            Activity::create($record);

            return true;
        } catch (\Exception $e) {
            Log::error('create error: ' . $e->getMessage());

            return false;
        }
    }

    public function detail(int $id): array
    {
        $act = Activity::find($id);
        if (!$act) {
            Log::error('activity not found', ['id' => $id]);
            throw new \Exception('activity not found');
        }

        return $act->toArray();
    }

    public function update(int $id, mixed $params): bool
    {
        $this->params($params);
        DB::beginTransaction();
        try {
            $record = $this->prepareRecord();
            unset($record['cycle'], $record['type']);

            $activity = Activity::find($id);
            $activity->name = $record['name'];
            $activity->rates = [
                'wallet' => $record['wallet'],
                'reward' => $record['amount'],
                'daily_reward' => $record['daily_reward'],
                'consecutive_day' => $record['consecutive_day'],
                'consecutive_reward' => $record['consecutive_reward'],
                'min_vip_level' => $record['min_vip_level'],
            ];
            $activity->start_date = $record['start_date'];
            $activity->end_date = $record['end_date'];
            $activity->status = $record['status'];
            $activity->save();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            Log::error('update error: ' . $e->getMessage());
            DB::rollBack();

            return false;
        }
    }

    public function delete(int $id): bool
    {
        $adminId = app('larke-admin.auth-admin')->getId();
        $currentDate = Carbon::now()->format(DateTimeFormat::DATETIME);
        $activity = Activity::find($id);
        $activity->deleted_at = $currentDate;
        $activity->deleted_by = $adminId;
        $activity->save();

        return true;
    }

    protected function params(array $params): void
    {
        $this->setBaseFilters($params);
        $this->name = $params['name'] ?? null;
        $this->type = $params['type'] ?? null;
        $this->cycle = $params['cycle'] ?? null;
        $this->activeDate = isset($params['active_date']) ? Carbon::parse($params['active_date'])->format(DateTimeFormat::DATE) : null;
        $this->start_date = $params['start_date'] ?? null;
        $this->end_date = $params['end_date'] ?? null;
        $this->status = $params['status'] ?? null;
        $this->amount = $params['amount'] ?? 0;
        $this->wallet = $params['wallet'] ?? 0;
        $this->daily_reward = $params['daily_reward'] ?? null;
        $this->consecutive_day = $params['consecutive_day'] ?? null;
        $this->consecutive_reward = $params['consecutive_reward'] ?? null;
        $this->min_vip_level = $params['min_vip_level'] ?? null;
    }

    public function toggleStatus(int $id): bool
    {
        try {
            $activity = Activity::find($id);
            $activity->status = !$activity->status;
            $activity->save();

            return true;
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }
    }

    private function prepareRecord(): array
    {
        return [
            'name' => $this->name,
            'cycle' => $this->cycle,
            'type' => $this->type,
            'wallet' => $this->wallet,
            'amount' => $this->amount,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'status' => $this->status,
            'daily_reward' => is_null($this->daily_reward) ? null : (float) $this->daily_reward,
            'consecutive_day' => $this->consecutive_day,
            'min_vip_level' => $this->min_vip_level,
            'consecutive_reward' => (float) $this->consecutive_reward,
        ];
    }

    public function newYearActivitySummary()
    {
        return Activity::select('activities.name', 'activities.start_date', 'activities.end_date')
            ->selectRaw('SUM(activity_transactions.activity_amount) as total_saving')
            ->selectRaw('COUNT(activity_transactions.activity_id) as saving_time')
            ->selectRaw('COUNT(activity_transactions.activity_id) * SUM(activity_transactions.activity_amount) as total_reward')
            ->leftJoin('activity_transactions', 'activities.id', '=', 'activity_transactions.activity_id')
            ->where('activity_transactions.activity_type', 'saving')
            ->groupBy('activities.id', 'activities.name', 'activities.start_date', 'activities.end_date')
            ->get();
    }

    public function savingLists($params = [])
    {
        // Get the summary data using raw queries

        $summaryResult = $this->getSummary($params);
        // Main query remains the same
        $query = ActivityTransaction::select([
            'activity_transactions.*',
            DB::raw('CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM payment_activities 
            WHERE payment_activities.activity_transaction_id = activity_transactions.id
        ) THEN "direct" 
        ELSE "wallet" 
        END AS method'),
        ])
            ->where('activity_type', 'saving');

        // Add method filter
        if (!empty($params['method'])) {
            if ($params['method'] === 'direct') {
                $query->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('payment_activities')
                        ->whereRaw('payment_activities.activity_id = activity_transactions.id');
                });
            } elseif ($params['method'] === 'wallet') {
                $query->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('payment_activities')
                        ->whereRaw('payment_activities.activity_id = activity_transactions.id');
                });
            }
        }

        if (!empty($params['phone'])) {
            $query->whereHas('user', function ($query) use ($params) {
                $query->where('phone', $params['phone']);
            });
        }

        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }
        $query->with(['user']);
        // Get paginated results
        $results = $query->paginate($params['pageSize'] ?? 10);

        // Return combined data
        return [
            'summary' => [
                'direct_amount' => (float) $summaryResult->direct_amount,
                'wallet_amount' => (float) $summaryResult->wallet_amount,
                'total_amount' => (float) ($summaryResult->direct_amount + $summaryResult->wallet_amount),
            ],
            'list' => $results,
        ];
    }

    public function savingUserSummaryLists($params = [])
    {
        $summaryResult = $this->getSummary($params);
        $query = ActivityTransaction::query()
            ->select([
                'users.name',
                'users.phone',
                'activity_transactions.activity_amount',
                DB::raw('SUM(activity_transactions.activity_amount) as amount'),
                DB::raw('COUNT(*) as times'),
                DB::raw('CASE COUNT(*)
            WHEN 1 THEN SUM(activity_transactions.activity_amount) * 2
            ELSE SUM(activity_transactions.activity_amount) * COUNT(*)
            END as rewards'),
            ])
            ->join('users', 'activity_transactions.user_id', '=', 'users.id')
            ->leftJoin('transaction_logs', function ($join) {
                $join->on('activity_transactions.id', '=', 'transaction_logs.reference_id')
                    ->where('transaction_logs.type', '=', 'activity_saving');
            })
            ->leftJoin('payment_activities', 'payment_activities.activity_transaction_id', '=', 'activity_transactions.id')
            ->where('activity_transactions.activity_type', 'saving')
            ->where(function ($query) {
                $query->whereNotNull('payment_activities.id')
                    ->orWhereNotNull('transaction_logs.id');
            });
        if (!empty($params['phone'])) {
            $query->whereRelation('user', function ($query) use ($params) {
                $query->where('phone', 'like', '%' . $params['phone'] . '%');
            });
        }
        if (!empty($params['name'])) {
            $query->whereRelation('user', 'name', 'like', '%' . $params['name'] . '%');
        }
        if (!empty($params['start_date'])) {
            $query->where('activity_transactions.created_at', '>=', Carbon::parse($params['start_date'])->utc());
        }
        if (!empty($params['end_date'])) {
            $query->where('activity_transactions.created_at', '<=', Carbon::parse($params['end_date'])->utc());
        }
        $query->groupBy(['activity_transactions.user_id', 'activity_transactions.activity_amount']);

        return [
            'summary' => [
                'direct_amount' => (float) $summaryResult->direct_amount,
                'wallet_amount' => (float) $summaryResult->wallet_amount,
                'total_amount' => (float) ($summaryResult->direct_amount + $summaryResult->wallet_amount),
            ],
            'list' => $query->paginate($params['pageSize'] ?? 10),
        ];
    }

    public function getSummary($params)
    {
        $summary = ActivityTransaction::selectRaw('
            SUM(CASE 
                WHEN EXISTS (
                    SELECT 1 
                    FROM payment_activities 
                    WHERE payment_activities.activity_transaction_id = activity_transactions.id
                ) THEN activity_amount 
                ELSE 0 
            END) as direct_amount,
            SUM(CASE 
                WHEN NOT EXISTS (
                    SELECT 1 
                    FROM payment_activities 
                    WHERE payment_activities.activity_transaction_id = activity_transactions.id
                ) THEN activity_amount 
                ELSE 0 
            END) as wallet_amount
        ')
            ->where('activity_type', 'saving');

        // Apply the same filters as the main query
        if (!empty($params['phone'])) {
            $summary->whereRelation('user', function ($query) use ($params) {
                $query->where('phone', 'like', '%' . $params['phone'] . '%');
            });
        }
        if (!empty($params['name'])) {
            $summary->whereRelation('user', 'name', 'like', '%' . $params['name'] . '%');
        }

        if (!empty($params['start_date'])) {
            $summary->where('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $summary->where('created_at', '<=', $params['end_date']);
        }

        // Get the summary result
        return $summary->first();
    }
}
