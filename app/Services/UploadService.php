<?php

namespace App\Services;

use App\Entities\UploadedFileEntity;
use App\Entities\UploadFileExits;
use App\Services\LarkeOverWrite\Upload;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Larke\Admin\Model\Attachment as AttachmentModel;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class UploadService
{
    /**
     * remake based on Lark upload
     */
    public function uploadFileToModel(UploadedFile $requestFile, $attachModel, $allowDuplicate = false): bool|array
    {
        try {
            $uploadService = Upload::create();
        } catch (\Exception $e) {
            return false;
        }
        $uploadedFile = $this->uploadFile($requestFile, $allowDuplicate);
        if (!$uploadedFile) {
            return false;
        }
        if ($uploadedFile instanceof UploadFileExits) {
            return [
                'id' => $uploadedFile->id,
                'url' => $uploadedFile->url,
            ];
        }
        if (!method_exists($attachModel, 'attachments')) {
            return false;
        }

        $attachmentModel = $attachModel->attachments();
        $attachment = $attachmentModel->create([
            'name' => $uploadedFile->name,
            'path' => Storage::disk($uploadedFile->uploadDisk)->url($uploadedFile->path),
            'mime' => $uploadedFile->mimeType,
            'extension' => $uploadedFile->extension,
            'size' => $uploadedFile->size,
            'md5' => $uploadedFile->md5,
            'sha1' => $uploadedFile->sha1,
            'driver' => $uploadedFile->driver,
            'status' => 1,
        ]);
        if ($attachment === false) {
            // 入库信息失败删除已上传文件
            $uploadService->destroy($uploadedFile->path);

            return __('larke-admin::attachment.upload_file_fail');
        }

        $res = [
            'id' => $attachment->id,
        ];
        if (in_array($uploadedFile->filetype, ['image', 'video', 'audio'])) {
            $url = $uploadService->objectUrl($uploadedFile->path);

            $res['url'] = $url;
        }

        return $res;
    }

    public function deleteFile($path): void
    {
        try {
            $uploadService = Upload::create();
            $uploadService->destroy($path);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }

    public function uploadFile(UploadedFile $requestFile, $allowDuplicate = false)
    {
        $pathname = $requestFile->getPathname();

        // 原始名称
        $name = $requestFile->getClientOriginalName();

        // mimeType
        $mimeType = $requestFile->getClientMimeType();

        // 扩展名
        $extension = $requestFile->getExtension();

        // 大小
        $size = $requestFile->getSize();

        $md5 = hash_file('md5', $pathname);

        $sha1 = hash_file('sha1', $pathname);

        try {
            $uploadService = Upload::create();
        } catch (\Exception $e) {
            return false;
        }

        $uploadDisk = config('larkeadmin.upload.disk');

        $driver = $uploadDisk ?: 'local';

        $mimeType = $uploadService->getMimeType($requestFile);
        $filetype = $uploadService->getFileType($requestFile);
        $fileInfo = AttachmentModel::byMd5($md5)->first();
        if (!empty($fileInfo) && !$allowDuplicate) {
            @unlink($pathname);

            $fileInfo->update([
                'update_time' => time(),
                'update_ip' => request()->ip(),
            ]);
            $res = UploadFileExits::make($fileInfo['id']);
            if (in_array($filetype, ['image', 'video', 'audio'])) {
                $res->url = $fileInfo['url'];
            }

            return $res;
        }

        if ($filetype == 'image') {
            $uploadDir = config('larkeadmin.upload.directory.image');
        } elseif ($filetype == 'video' || $filetype == 'audio') {
            $uploadDir = config('larkeadmin.upload.directory.media');
        } else {
            $uploadDir = config('larkeadmin.upload.directory.file');
        }

        try {
            $path = $uploadService->dir($uploadDir)
                ->uniqueName()
                ->upload($requestFile);
        } catch (\Exception $e) {
            return false;
        }

        return UploadedFileEntity::make($name, $mimeType, $uploadDisk, $path, $extension, $size, $md5, $sha1, $driver);
    }
}
