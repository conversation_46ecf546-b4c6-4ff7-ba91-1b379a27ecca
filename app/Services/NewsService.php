<?php

namespace App\Services;

use App\Models\News;
use App\Repositories\NewsRepository;
use Illuminate\Http\Request;

class NewsService
{
    public function __construct(
        private NewsRepository $newsRepository
    ) {
    }

    /**
     * Get paginated news list
     */
    public function getList(Request $request, ?string $locale = null): array
    {
        $pageSize = (int) data_get($request, 'pageSize', 10);
        $paginatedNews = $this->newsRepository->paginate($pageSize, $locale);

        // Convert to the expected format for backward compatibility
        return [
            'current_page' => $paginatedNews->currentPage(),
            'start' => ($paginatedNews->currentPage() - 1) * $paginatedNews->perPage(),
            'limit' => $paginatedNews->perPage(),
            'total' => $paginatedNews->total(),
            'list' => $paginatedNews->items(),
        ];
    }

    /**
     * Create new news
     */
    public function create(array $data): News
    {
        $data['active'] = (int) (bool) ($data['active'] ?? false);

        return $this->newsRepository->create($data);
    }

    /**
     * Update existing news
     */
    public function update(array $data, News $news): News
    {
        $data['active'] = (int) (bool) ($data['active'] ?? $news->active);

        return $this->newsRepository->update($news, $data);
    }

    /**
     * Update news active status
     */
    public function updateActive(News $news, bool $active): News
    {
        return $this->newsRepository->updateActive($news, $active);
    }

    /**
     * Get single news item
     */
    public function getNews(string $newsId, ?string $locale = null): ?News
    {
        return $this->newsRepository->findById((int) $newsId);
    }

    /**
     * Delete news
     */
    public function delete(News $news): bool
    {
        return $this->newsRepository->delete($news);
    }
}
