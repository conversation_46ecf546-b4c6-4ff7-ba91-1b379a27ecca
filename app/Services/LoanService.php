<?php

namespace App\Services;

use App\Constants\LoanRequestStatus;
use App\Constants\TransactionType;
use App\Constants\WalletType;
use App\Models\LoanRequestModel;
use App\Utils\Math;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class LoanService
{
    public function __construct(protected WalletService $service)
    {
    }

    public function processLoan(int $loanId, LoanRequestStatus $status): bool
    {
        $loanRequest = LoanRequestModel::find($loanId);
        /* @var \App\Models\LoanRequestModel $loanRequest */
        if ($loanRequest->status != LoanRequestStatus::Pending->value) {
            throw new BadRequestHttpException(__('messages.invalid_loan_request', locale: 'zh_CN'), code: 400);
        }
        if ($status == LoanRequestStatus::Approved) {
            return $this->approveLoan($loanRequest);
        }

        return $this->rejectLoan($loanRequest);
    }

    protected function approveLoan(LoanRequestModel $loanRequest)
    {
        if (!$user) {
            throw new BadRequestHttpException('invalid_loan_request', code: 400);
        }
        $wallet = WalletService::make($user, WalletType::Deposit);
        DB::beginTransaction();
        try {
            $loanRequest->status = LoanRequestStatus::Success;
            $loanRequest->save();
            $oldBalance = $wallet->getProfit();
            $wallet->fluctuateProfit(
                $loanRequest->amount,
            );
            $wallet->saveTransactionLogs(
                amount: $loanRequest->amount,
                balance: $wallet->getProfit(),
                oldBalance: $oldBalance,
                type: TransactionType::Loan,
                remark: __('messages.remark_approved', ['amount' => Math::formatNumber($loanRequest->amount, true)], 'zh_CN'),
                transactionCode: Str::uuid(),
                reference: LoanRequestModel::class,
                referenceId: $loanRequest->id
            );
            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
        }

        return false;
    }

    protected function rejectLoan(LoanRequestModel $loanRequestModel)
    {
        $loanRequestModel->status = LoanRequestStatus::Failed;
        $loanRequestModel->save();

        return true;
    }
}
