<?php

namespace App\Services\Senhuo;

use App\Models\Withdraw;
use App\Utils\Math;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use InvalidArgumentException;
use RuntimeException;

class SenhuoService
{
    private const API_DOMAIN = 'http://a.senhuo88.com';

    private string $apiKey;

    private string $username;

    public function __construct()
    {
        $this->apiKey = config('services.senhuo.key');
        $this->username = config('services.senhuo.merchant');
    }

    /**
     * Process a recharge request
     *
     * @throws RuntimeException If the API request fails
     */
    public function processWithdraw($params)
    {
        return $this->makeApiRequest('/api/personnelfiles/order/pay', $params);
    }

    public function prepareWithdraw(Withdraw $withdraw)
    {
        $this->validateCredentials($this->apiKey, $this->username);
        // $this->validateAmount($withdraw->amount);
        $this->validateAmount($withdraw->actual_amount);
        $params = [
            'userName' => $this->username,
            'version' => '2.0',
            'cardName' => $withdraw->userBank->account_name,
            'cardNum' => $withdraw->userBank->account,
            'openBank' => $withdraw->userBank->name,
            'amount' => Math::formatNumber($withdraw->amount, true),
            'outOrderId' => Str::uuid()->toString(),
            'returnUrl' => config('services.senhuo.withdraw_callback_url'),
        ];

        $params['sign'] = $this->generateSignature($params);

        return $params;
    }

    /**
     * Query the status of an order
     *
     * @param  string  $orderId  Order ID to query
     * @return array Order status details
     *
     * @throws RuntimeException If the API request fails
     */
    public function getOrderStatus(string $orderId, $type = 'withdraw'): array
    {
        if (empty($orderId)) {
            throw new InvalidArgumentException('Order ID cannot be empty');
        }

        $params = [
            'userName' => $this->username,
            'outOrderId' => $orderId,
        ];

        $params['sign'] = $this->generateSignature($params);
        if ($type == 'withdraw') {
            return $this->makeApiRequest('/api/personnelfiles/order/querydetail', $params);
        }

        return $this->makeApiRequest('/api/personnelfiles/preorder/querydetail', $params);
    }

    /**
     * Generate signature for API request parameters
     *
     * @param  array  $params  Parameters to sign
     * @return string Generated signature
     */
    private function generateSignature(array $params): string
    {
        // Build the query string from the sorted parameters
        $queryString = '';
        foreach ($params as $key => $value) {
            $queryString .= $key . '=' . $value . '&';
        }

        // Remove the last "&" from the query string
        $queryString = rtrim($queryString, '&');

        // Append the access token (merchant key) at the end of the query string
        $queryString .= '&access_token=' . $this->apiKey;

        // Generate the MD5 hash of the final query string
        return strtoupper(md5($queryString));
    }

    /**
     * Generate a unique order ID
     *
     * @return string Unique order identifier
     */
    private function generateOrderId(): string
    {
        return str_replace('-', '', uniqid((string) mt_rand(), true));
    }

    private function makeApiRequest(string $endpoint, array $params): array
    {
        $message = Str::uuid()->toString() . ' ' . self::API_DOMAIN . $endpoint;
        try {
            $client = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->withOptions([
                'verify' => false, // Disable SSL verification
            ]);

            // Set proxy for development mode
            if (config('services.senhuo.development')) {
                $client = $client->withOptions([
                    'proxy' => [
                        'http' => config('services.senhuo.proxy'),
                    ],
                ]);
            }

            $response = $client->post(self::API_DOMAIN . $endpoint, $params);

            Log::channel('senhuo_daily')->info($message, ['context' => [
                'endpoint' => $endpoint,
                'params' => $params,
                'status' => $response->status(),
                'response' => $response->json(),
            ]]);

            // Check if the request was successful
            if ($response->successful()) {
                return [
                    'state' => true,
                    'data' => $response->json(),
                ];
            }

            // Handle HTTP errors
            return [
                'state' => false,
                'msg' => 'HTTP请求失败,状态码: ' . $response->status(),
            ];
        } catch (ConnectionException $e) {
            Log::channel('senhuo_daily')->error($message, ['context' => [
                'endpoint' => $endpoint,
                'params' => $params,
                'error' => $e->getMessage(),
            ]]);

            // Handle connection errors
            return [
                'state' => false,
                'msg' => 'CURL错误: ' . $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::channel('senhuo_daily')->error($message, ['context' => [
                'endpoint' => $endpoint,
                'params' => $params,
                'error' => $e->getMessage(),
            ]]);

            // Handle JSON parsing or other errors
            return [
                'state' => false,
                'msg' => 'JSON解析失败: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Validate credentials
     *
     *
     * @throws InvalidArgumentException
     */
    private function validateCredentials(string $apiKey, string $username): void
    {
        if (empty($apiKey)) {
            throw new InvalidArgumentException('API key cannot be empty');
        }

        if (empty($username)) {
            throw new InvalidArgumentException('Username cannot be empty');
        }
    }

    /**
     * Validate recharge amount
     *
     *
     * @throws InvalidArgumentException
     */
    private function validateAmount(float $amount): void
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be greater than zero');
        }
    }
}
