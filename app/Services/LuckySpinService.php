<?php

namespace App\Services;

use App\Http\Requests\LuckySpinResultListRequest;
use App\Models\LuckySpinAssignment;
use App\Models\LuckySpinEvent;
use App\Models\LuckySpinResult;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class LuckySpinService
{
    public function getLuckySpinResultList(LuckySpinResultListRequest $request)
    {
        $query = LuckySpinResult::query();

        // Phone number filtering
        if ($request->filled('user_phone')) {
            $query->where('user_phone', trim($request->user_phone));
        }

        // Verification code filtering
        if ($request->filled('verification_code')) {
            $query->where('verification_code', trim($request->verification_code));
        }

        // Date range filtering
        if ($request->filled('start_date')) {
            try {
                $startDate = Carbon::parse($request->start_date)->startOfDay();
                $query->where('created_at', '>=', $startDate);
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid start date format',
                ], 422);
            }
        }

        if ($request->filled('end_date')) {
            try {
                $endDate = Carbon::parse($request->end_date)->endOfDay();
                $query->where('created_at', '<=', $endDate);
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid end date format',
                ], 422);
            }
        }

        // Reward title filtering with eager loading
        if ($request->filled('reward_title')) {
            $query->whereHas('luckySpin', function ($query) use ($request) {
                $query->where('reward_title', 'LIKE', '%' . trim($request->reward_title) . '%');
            });
        }
        $query->whereNotNull('lucky_spin_id');
        $query->orderBy('created_at', 'desc');

        // Eager load the luckySpin relationship to avoid N+1 problem
        $query->with('luckySpin')->with('user');

        // Validate and apply pagination
        $perPage = $request->get('pageSize', 20);

        return $query->latest()
            ->paginate($perPage)
            ->withQueryString();
    }

    public function getLuckySpinList(Request $request)
    {
        $query = LuckySpinEvent::query()->with(['rewards']);
        if ($request->input('pagination') == 'false') {
            $query->where('is_enabled', true);
            $query->where('starts_at', '<=', now());
            $query->where('ends_at', '>=', now());

            return $query->get();
        }

        return $query->paginate($request->get('pageSize', 20));
    }

    public function assignLuckySpinToUser(array $data)
    {
        switch ($data['assignment_type']) {
            case 'individual':
            case 'group':
                $data['guaranteed_rewards'] = $data['guaranteed_rewards'] ?? [];
                LuckySpinAssignment::create($data);
                break;
            case 'multiple':
                foreach ($data['target_ids'] as $userId) {
                    $data['target_id'] = $userId;
                    $data['assignment_type'] = 'individual';
                    $data['guaranteed_rewards'] = $data['guaranteed_rewards'] ? $data['guaranteed_rewards'] : [];
                    $data['created_at'] = $data['updated_at'] = now();
                    LuckySpinAssignment::create($data);
                }
                break;
        }
    }
}
