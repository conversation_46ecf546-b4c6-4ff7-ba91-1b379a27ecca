<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserHierarchyService
{
    /**
     * Synchronize user hierarchy to closure table
     * Handles both immediate and background processing based on hierarchy depth
     * Thread-safe implementation with row-level locking and retry mechanism
     */
    public function syncUserHierarchy(User $user): void
    {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                $this->syncUserHierarchyWithLocking($user);

                return; // Success, exit retry loop
            } catch (\Exception $e) {
                $retryCount++;

                // Check if it's a deadlock or lock timeout error
                if ($this->isLockingError($e) && $retryCount < $maxRetries) {
                    $waitTime = pow(2, $retryCount) * 100000; // Exponential backoff in microseconds
                    usleep($waitTime);

                    Log::warning('Retrying hierarchy sync due to locking error', [
                        'user_id' => $user->id,
                        'retry_count' => $retryCount,
                        'wait_time_ms' => $waitTime / 1000,
                        'error' => $e->getMessage(),
                    ]);

                    continue;
                }

                Log::error('Failed to sync user hierarchy', [
                    'user_id' => $user->id,
                    'retry_count' => $retryCount,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                throw $e;
            }
        }
    }

    /**
     * Core hierarchy sync with proper locking
     */
    private function syncUserHierarchyWithLocking(User $user): void
    {
        $hierarchyInserts = null;
        $recordsInserted = 0;

        // Pre-condition validation
        $this->validateUserForSync($user);

        DB::transaction(function () use ($user, &$hierarchyInserts, &$recordsInserted) {
            // 1. Lock the user record to prevent concurrent modifications
            $lockedUser = DB::table('users')
                ->where('id', $user->id)
                ->lockForUpdate()
                ->first();

            if (!$lockedUser) {
                throw new \Exception("User {$user->id} not found during sync");
            }

            // Re-validate after acquiring lock (user state might have changed)
            $freshUser = User::find($user->id);
            $this->validateUserForSync($freshUser);

            // 2. Pre-calculate hierarchy data outside the critical section timing
            $hierarchyInserts = $this->buildHierarchyInserts($freshUser);

            // 3. Use database-level locking for hierarchy table operations
            $this->acquireHierarchyLock($user->id);

            try {
                // 4. Use upsert pattern instead of delete+insert
                $recordsInserted = $this->upsertHierarchyRecords($user->id, $hierarchyInserts);

                // 5. Post-condition validation
                $this->validateHierarchyAfterSync($freshUser, $hierarchyInserts);
            } finally {
                $this->releaseHierarchyLock($user->id);
            }
        });

        Log::info('User hierarchy synced successfully', [
            'user_id' => $user->id,
            'hierarchy_depth' => $user->getHierarchyDepth(),
            'records_inserted' => $recordsInserted,
        ]);
    }

    /**
     * Validate user state before sync operation
     */
    private function validateUserForSync(User $user): void
    {
        if (!$user) {
            throw new \Exception('User not found for hierarchy sync');
        }

        // Check for circular reference in parent chain
        if ($user->parent_id && $this->hasCircularReference($user)) {
            throw new \Exception("Circular reference detected in user hierarchy for user {$user->id}");
        }

        // Validate parent exists if specified
        if ($user->parent_id && !User::where('id', $user->parent_id)->exists()) {
            throw new \Exception("Parent user {$user->parent_id} not found for user {$user->id}");
        }
    }

    /**
     * Validate hierarchy data integrity after sync
     */
    private function validateHierarchyAfterSync(User $user, array $expectedInserts): void
    {
        $actualRecords = DB::table('user_hierarchies')
            ->where('descendant_id', $user->id)
            ->orderBy('depth')
            ->get();

        // Validate self-reference exists
        $selfRef = $actualRecords->where('depth', 0)->where('ancestor_id', $user->id)->first();
        if (!$selfRef) {
            throw new \Exception("Self-reference missing after hierarchy sync for user {$user->id}");
        }

        // Validate expected record count
        if (count($actualRecords) !== count($expectedInserts)) {
            Log::warning('Hierarchy record count mismatch after sync', [
                'user_id' => $user->id,
                'expected_count' => count($expectedInserts),
                'actual_count' => count($actualRecords),
            ]);
        }
    }

    /**
     * Check for circular references in user hierarchy
     */
    private function hasCircularReference(User $user, array $visited = []): bool
    {
        if (in_array($user->id, $visited)) {
            return true;
        }

        if (!$user->parent_id) {
            return false;
        }

        $visited[] = $user->id;
        $parent = User::find($user->parent_id);

        if (!$parent) {
            return false;
        }

        return $this->hasCircularReference($parent, $visited);
    }

    /**
     * Recovery mechanism for failed sync operations
     */
    public function recoverUserHierarchy(User $user): void
    {
        Log::info('Starting hierarchy recovery for user', ['user_id' => $user->id]);

        try {
            // Clear corrupted data
            DB::table('user_hierarchies')
                ->where('descendant_id', $user->id)
                ->delete();

            // Attempt fresh sync
            $this->syncUserHierarchy($user);

            Log::info('Hierarchy recovery completed successfully', ['user_id' => $user->id]);
        } catch (\Exception $e) {
            Log::error('Hierarchy recovery failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Remove existing hierarchy records for a user
     */
    private function removeExistingHierarchy(int $userId): void
    {
        DB::table('user_hierarchies')
            ->where('descendant_id', $userId)
            ->delete();
    }

    /**
     * Build hierarchy insert data using dynamic hierarchy calculation
     */
    private function buildHierarchyInserts(User $user): array
    {
        $inserts = [];
        $now = now();

        // Self-reference (depth 0)
        $inserts[] = [
            'ancestor_id' => $user->id,
            'descendant_id' => $user->id,
            'depth' => 0,
            'created_at' => $now,
        ];

        // Use dynamic hierarchy calculation instead of stored field
        try {
            $hierarchyString = $this->buildHierarchyForUser($user);

            if ($hierarchyString) {
                $ancestors = $this->parseHierarchy($hierarchyString);

                foreach ($ancestors as $depth => $ancestorId) {
                    // Verify ancestor exists before creating relationship
                    if (User::where('id', $ancestorId)->exists()) {
                        $inserts[] = [
                            'ancestor_id' => (int) $ancestorId,
                            'descendant_id' => $user->id,
                            'depth' => $depth + 1,
                            'created_at' => $now,
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            // Fallback to stored hierarchy if dynamic calculation fails
            Log::warning('Failed to build dynamic hierarchy, falling back to stored hierarchy', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            if ($user->hierarchy) {
                $ancestors = $this->parseHierarchy($user->hierarchy);

                foreach ($ancestors as $depth => $ancestorId) {
                    if (User::where('id', $ancestorId)->exists()) {
                        $inserts[] = [
                            'ancestor_id' => (int) $ancestorId,
                            'descendant_id' => $user->id,
                            'depth' => $depth + 1,
                            'created_at' => $now,
                        ];
                    }
                }
            }
        }

        return $inserts;
    }

    /**
     * Parse pipe-delimited hierarchy string into ancestor array
     */
    private function parseHierarchy(string $hierarchy): array
    {
        $trimmed = trim($hierarchy, '|');

        return $trimmed ? array_filter(explode('|', $trimmed)) : [];
    }

    /**
     * Migrate existing users to closure table
     * Used for initial data population
     */
    public function migrateExistingUsers(int $batchSize = 1000): void
    {
        Log::info('Starting migration of existing users to hierarchy table');

        $totalUsers = User::count();
        $processedUsers = 0;
        $insertedRecords = 0;

        User::chunk($batchSize, function ($users) use (&$processedUsers, &$insertedRecords) {
            $hierarchyData = [];

            foreach ($users as $user) {
                $userInserts = $this->buildHierarchyInserts($user);
                $hierarchyData = array_merge($hierarchyData, $userInserts);
                $insertedRecords += count($userInserts);
            }

            // Batch insert for performance
            if (!empty($hierarchyData)) {
                DB::table('user_hierarchies')->insert($hierarchyData);
            }

            $processedUsers += count($users);
            Log::info('Migration progress', [
                'processed_users' => $processedUsers,
                'inserted_records' => $insertedRecords,
            ]);
        });

        Log::info('Migration completed', [
            'total_users' => $totalUsers,
            'processed_users' => $processedUsers,
            'inserted_records' => $insertedRecords,
        ]);
    }

    /**
     * Validate hierarchy data integrity
     * Compare closure table with hierarchy field data
     */
    public function validateHierarchyIntegrity(): array
    {
        $issues = [];

        // Check for missing self-references
        $usersWithoutSelfRef = DB::select('
            SELECT u.id
            FROM users u
            LEFT JOIN user_hierarchies uh ON u.id = uh.ancestor_id AND u.id = uh.descendant_id AND uh.depth = 0
            WHERE uh.ancestor_id IS NULL
        ');

        if (!empty($usersWithoutSelfRef)) {
            $issues['missing_self_references'] = array_column($usersWithoutSelfRef, 'id');
        }

        // Check for hierarchy mismatches using dynamic hierarchy calculation
        $users = User::whereNotNull('parent_id')->get();
        foreach ($users as $user) {
            try {
                // Use dynamic hierarchy calculation for expected ancestors
                $expectedHierarchyString = $this->buildHierarchyForUser($user);
                $expectedAncestors = $expectedHierarchyString ? $this->parseHierarchy($expectedHierarchyString) : [];

                $actualAncestors = DB::table('user_hierarchies')
                    ->where('descendant_id', $user->id)
                    ->where('depth', '>', 0)
                    ->orderBy('depth')
                    ->pluck('ancestor_id')
                    ->toArray();

                if ($expectedAncestors !== array_map('strval', $actualAncestors)) {
                    $issues['hierarchy_mismatches'][] = [
                        'user_id' => $user->id,
                        'expected' => $expectedAncestors,
                        'actual' => $actualAncestors,
                        'stored_hierarchy' => $user->hierarchy, // Keep for comparison
                    ];
                }
            } catch (\Exception $e) {
                // Fallback to stored hierarchy for validation
                Log::warning('Failed to validate hierarchy with dynamic calculation, using stored data', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);

                if ($user->hierarchy) {
                    $expectedAncestors = $this->parseHierarchy($user->hierarchy);
                    $actualAncestors = DB::table('user_hierarchies')
                        ->where('descendant_id', $user->id)
                        ->where('depth', '>', 0)
                        ->orderBy('depth')
                        ->pluck('ancestor_id')
                        ->toArray();

                    if ($expectedAncestors !== array_map('strval', $actualAncestors)) {
                        $issues['hierarchy_mismatches'][] = [
                            'user_id' => $user->id,
                            'expected' => $expectedAncestors,
                            'actual' => $actualAncestors,
                            'validation_method' => 'stored_fallback',
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * Clean up orphaned hierarchy records
     */
    public function cleanupOrphanedRecords(): int
    {
        $deleted = DB::table('user_hierarchies')
            ->leftJoin('users as ancestor', 'user_hierarchies.ancestor_id', '=', 'ancestor.id')
            ->leftJoin('users as descendant', 'user_hierarchies.descendant_id', '=', 'descendant.id')
            ->whereNull('ancestor.id')
            ->orWhereNull('descendant.id')
            ->delete();

        if ($deleted > 0) {
            Log::info('Cleaned up orphaned hierarchy records', ['deleted_count' => $deleted]);
        }

        return $deleted;
    }

    /**
     * Find users that are missing hierarchy data
     */
    public function findUsersMissingHierarchyData(?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = User::whereNotExists(function ($subQuery) {
            $subQuery->select(DB::raw(1))
                ->from('user_hierarchies')
                ->whereColumn('user_hierarchies.descendant_id', 'users.id')
                ->where('user_hierarchies.depth', 0); // Missing self-reference
        });

        if ($limit) {
            $query->limit($limit);
        }

        return $query->orderBy('id')->get();
    }

    /**
     * Find users with incomplete hierarchy data
     */
    public function findUsersWithIncompleteHierarchyData(?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        // Find users who have parent relationships but may have incomplete closure table data
        $users = User::whereNotNull('parent_id')->get();

        $incompleteUsers = collect();

        foreach ($users as $user) {
            if (!$this->hasCompleteHierarchyData($user)) {
                $incompleteUsers->push($user);

                if ($limit && $incompleteUsers->count() >= $limit) {
                    break;
                }
            }
        }

        return new \Illuminate\Database\Eloquent\Collection($incompleteUsers->toArray());
    }

    /**
     * Check if user has complete hierarchy data in closure table
     */
    public function hasCompleteHierarchyData(User $user): bool
    {
        // Check if user has self-reference
        $hasSelfReference = DB::table('user_hierarchies')
            ->where('ancestor_id', $user->id)
            ->where('descendant_id', $user->id)
            ->where('depth', 0)
            ->exists();

        if (!$hasSelfReference) {
            return false;
        }

        // Use dynamic hierarchy calculation to determine expected ancestors
        try {
            $expectedHierarchyString = $this->buildHierarchyForUser($user);

            if ($expectedHierarchyString) {
                $expectedAncestors = $this->parseHierarchy($expectedHierarchyString);
                $expectedDepth = count($expectedAncestors);

                if ($expectedDepth > 0) {
                    $actualRecords = DB::table('user_hierarchies')
                        ->where('descendant_id', $user->id)
                        ->where('depth', '>', 0)
                        ->count();

                    return $actualRecords >= $expectedDepth;
                }
            }
        } catch (\Exception $e) {
            // Fallback to stored hierarchy for validation
            Log::warning('Failed to check hierarchy completeness with dynamic calculation, using stored data', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            if ($user->hierarchy) {
                $expectedAncestors = $this->parseHierarchy($user->hierarchy);
                $expectedDepth = count($expectedAncestors);

                if ($expectedDepth > 0) {
                    $actualRecords = DB::table('user_hierarchies')
                        ->where('descendant_id', $user->id)
                        ->where('depth', '>', 0)
                        ->count();

                    return $actualRecords >= $expectedDepth;
                }
            }
        }

        return true;
    }

    /**
     * Get hierarchy coverage statistics
     */
    public function getHierarchyCoverageStats(): array
    {
        $totalUsers = User::count();
        $usersWithHierarchy = DB::table('user_hierarchies')
            ->distinct('descendant_id')
            ->count('descendant_id');

        $missingCount = $totalUsers - $usersWithHierarchy;
        $coverage = $totalUsers > 0 ? round(($usersWithHierarchy / $totalUsers) * 100, 2) : 0;

        return [
            'total_users' => $totalUsers,
            'users_with_hierarchy' => $usersWithHierarchy,
            'users_missing_hierarchy' => $missingCount,
            'coverage_percentage' => $coverage,
        ];
    }

    /**
     * Get detailed hierarchy statistics
     */
    public function getDetailedHierarchyStats(): array
    {
        $stats = DB::table('user_hierarchies')
            ->selectRaw('
                COUNT(*) as total_records,
                COUNT(DISTINCT descendant_id) as unique_users,
                MAX(depth) as max_depth,
                AVG(depth) as avg_depth,
                COUNT(CASE WHEN depth = 0 THEN 1 END) as self_references,
                COUNT(CASE WHEN depth > 0 THEN 1 END) as ancestor_records
            ')
            ->first();

        $depthDistribution = DB::table('user_hierarchies')
            ->where('depth', '>', 0)
            ->groupBy('depth')
            ->selectRaw('depth, COUNT(*) as count')
            ->orderBy('depth')
            ->pluck('count', 'depth')
            ->toArray();

        return [
            'total_records' => $stats->total_records,
            'unique_users' => $stats->unique_users,
            'max_depth' => $stats->max_depth,
            'average_depth' => $stats->avg_depth !== null ? round($stats->avg_depth, 2) : 0,
            'self_references' => $stats->self_references,
            'ancestor_records' => $stats->ancestor_records,
            'depth_distribution' => $depthDistribution,
        ];
    }

    /**
     * Check if the exception is related to database locking
     */
    private function isLockingError(\Exception $e): bool
    {
        $message = strtolower($e->getMessage());

        return str_contains($message, 'deadlock') ||
            str_contains($message, 'lock wait timeout') ||
            str_contains($message, 'lock timeout') ||
            str_contains($message, 'serialization failure') ||
            $e->getCode() === 1213 || // MySQL deadlock
            $e->getCode() === 1205;   // MySQL lock timeout
    }

    /**
     * Acquire database-level lock for hierarchy operations
     */
    private function acquireHierarchyLock(int $userId): void
    {
        // Use MySQL named locks for hierarchy synchronization
        $lockName = "hierarchy_sync_{$userId}";
        $timeout = 10; // 10 seconds timeout

        $result = DB::selectOne('SELECT GET_LOCK(?, ?) as lock_result', [$lockName, $timeout]);

        if (!$result || $result->lock_result !== 1) {
            throw new \Exception("Failed to acquire hierarchy lock for user {$userId}");
        }
    }

    /**
     * Release database-level lock for hierarchy operations
     */
    private function releaseHierarchyLock(int $userId): void
    {
        $lockName = "hierarchy_sync_{$userId}";
        DB::selectOne('SELECT RELEASE_LOCK(?) as release_result', [$lockName]);
    }

    /**
     * Upsert hierarchy records using atomic operations
     */
    private function upsertHierarchyRecords(int $userId, array $hierarchyInserts): int
    {
        if (empty($hierarchyInserts)) {
            return 0;
        }

        // First, remove existing records that are not in the new set
        $newPairs = [];
        foreach ($hierarchyInserts as $insert) {
            $newPairs[] = $insert['ancestor_id'] . '_' . $insert['depth'];
        }

        // Delete records that should no longer exist
        DB::table('user_hierarchies')
            ->where('descendant_id', $userId)
            ->whereNotIn(
                DB::raw("CONCAT(ancestor_id, '_', depth)"),
                $newPairs
            )
            ->delete();

        $recordsInserted = 0;

        // Insert records using MySQL's INSERT IGNORE for idempotency
        // Since table has composite primary key (ancestor_id, descendant_id), duplicates will be ignored
        foreach ($hierarchyInserts as $insert) {
            $result = DB::statement('
                INSERT IGNORE INTO user_hierarchies (ancestor_id, descendant_id, depth, created_at)
                VALUES (?, ?, ?, ?)
            ', [
                $insert['ancestor_id'],
                $insert['descendant_id'],
                $insert['depth'],
                $insert['created_at'],
            ]);

            if ($result) {
                $recordsInserted++;
            }
        }

        return $recordsInserted;
    }

    /**
     * Build hierarchy string for a user based on their parent
     *
     * Constructs the hierarchy string by traversing up the parent chain.
     * Used when creating or updating user parent relationships.
     *
     * @param User $user The user to build hierarchy for
     * @return string The hierarchy string
     */
    public function buildHierarchyForUser(User $user): string
    {
        if (!$user->parent_id) {
            return '';
        }

        try {
            $maxDepth = 20; // Prevent infinite loops
            $visitedIds = []; // Track visited IDs to detect circular references

            // Use a recursive CTE to fetch all ancestors in correct order
            $ancestors = DB::select(
                <<<'SQL'
                WITH RECURSIVE ancestors AS (
                    SELECT id, parent_id, 1 as depth
                    FROM users
                    WHERE id = ?
                    UNION ALL
                    SELECT u.id, u.parent_id, a.depth + 1
                    FROM users u
                    INNER JOIN ancestors a ON u.id = a.parent_id
                    WHERE a.depth < ? AND u.parent_id IS NOT NULL
                )
                SELECT id FROM ancestors WHERE parent_id IS NOT NULL
                SQL,
                [$user->parent_id, $maxDepth]
            );

            // Build the hierarchy array from the result
            $hierarchy = [];
            foreach ($ancestors as $row) {
                $ancestorId = $row->id;

                // Check for circular reference
                if (in_array($ancestorId, $visitedIds)) {
                    Log::warning('Circular reference detected in user hierarchy', [
                        'user_id' => $user->id,
                        'circular_ancestor' => $ancestorId,
                        'visited_ids' => $visitedIds,
                    ]);
                    break;
                }

                $visitedIds[] = $ancestorId;
                $hierarchy[] = $ancestorId;
            }

            // Format as pipe-delimited string with leading and trailing pipes
            return empty($hierarchy) ? '' : '|' . implode('|', $hierarchy) . '|';
        } catch (\Exception $e) {
            Log::error('Failed to build hierarchy for user', [
                'user_id' => $user->id,
                'parent_id' => $user->parent_id,
                'error' => $e->getMessage(),
            ]);

            return '';
        }
    }
}
