<?php

namespace App\Services;

use App\Constants\CommissionType;
use App\Constants\OrderStatus;
use App\Constants\TransactionType;
use App\Constants\WalletType;
use App\Constants\WithdrawStatus;
use App\Http\Requests\HourlyWithdrawRequest;
use App\Models\Commission;
use App\Models\Deposit;
use App\Models\DepositRequests;
use App\Models\Order;
use App\Models\TeamStatistic;
use App\Models\TransactionLog;
use App\Models\User;
use App\Models\UserLog;
use App\Models\Wallet;
use App\Models\Withdraw;
use App\Utils\Gmt8;
use App\Utils\Math;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class StatisticsService
{
    public function getStatistics(array $params): array
    {
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        // Get members register count with date filter if provided
        $membersRegisterQuery = User::query();
        if ($startDate && $endDate) {
            $membersRegisterQuery->whereBetween('created_at', [$startDate, $endDate]);
        }
        $memberRegisterCount = $membersRegisterQuery->count();

        $totalMembers = User::query()->count();

        // Get transaction statistics
        $transactionStats = TransactionLog::query()
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->select([
                // DB::raw('COUNT(DISTINCT CASE WHEN type = "deposit" THEN user_id END) as deposit_user'),
                // DB::raw('COUNT(CASE WHEN type = "deposit" THEN 1 END) as deposit_number'),
                // DB::raw('SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) as deposit_amount'),

                DB::raw('COUNT(DISTINCT CASE WHEN type = "withdraw" THEN user_id END) as withdraw_user'),
                DB::raw('COUNT(CASE WHEN type = "withdraw" THEN 1 END) as withdraw_number'),
                DB::raw('SUM(CASE WHEN type = "withdraw" THEN amount ELSE 0 END) as withdraw_amount'),

                DB::raw('COUNT(DISTINCT CASE WHEN type = "order" THEN user_id END) as investment_user'),
                DB::raw('COUNT(CASE WHEN type = "order" THEN 1 END) as investment_number'),
                DB::raw('SUM(CASE WHEN type = "order" THEN amount ELSE 0 END) as investment_amount'),
            ])
            ->whereIn('type', ['deposit', 'withdraw', 'order'])
            ->first();
        $withdrawalQuery = Withdraw::where('status', WithdrawStatus::Approved->value)
            ->groupBy('user_id')
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->select(['user_id', DB::raw('sum(amount) as withdraw_amount'), DB::raw('count(id) as withdraw_number')]);
        $withdrawStats = DB::query()
            ->fromSub($withdrawalQuery, 'withdrawal')
            ->select([DB::raw('count(user_id) as withdraw_user'), DB::raw('sum(withdraw_amount) as withdraw_amount'), DB::raw('sum(withdraw_number) as withdraw_number')])
            ->first();
        // Get commission statistics
        $commissionStats = TeamStatistic::query()
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->select([
                DB::raw('SUM(investment_commission) as investment_commission_amount'),
                DB::raw('SUM(rebate_commission) as rebate_commission_amount'),
            ])
            ->first();

        $claimedCommission = Commission::whereType('investment')
            // ->whereCategoryId(2)
            ->where('created_at', '>=', '2024-12-11 16::00:00')
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->whereNotNull('processed_at')
            ->groupBy('user_id')
            ->select(['user_id', DB::raw('sum(amount) as amount'), DB::raw('count(id) as claimed_times')]);
        $commissionClaimStats = DB::query()->fromSub($claimedCommission, 'commission')
            ->select([DB::raw('count(user_id) as claimed_users'), DB::raw('sum(amount) as claimed_amount'), DB::raw('sum(claimed_times) as claimed_times')])
            ->first();
        $userStats = $this->getUserGlobalStatistic();
        $depositStats = $this->depositStats();
        $depositSummary = $this->getDepositSummary($startDate, $endDate);

        // Combine all statistics
        return [
            'members' => $totalMembers,
            'new_members_register' => $memberRegisterCount,
            'deposit_user' => $depositSummary->deposit_user ?? 0,
            'deposit_number' => $depositSummary->deposit_number ?? 0,
            'deposit_amount' => $depositSummary->deposit_amount ?? 0,
            'withdraw_user' => $withdrawStats->withdraw_user ?? 0,
            'withdraw_number' => $withdrawStats->withdraw_number ?? 0,
            'withdraw_amount' => $withdrawStats->withdraw_amount ?? 0,
            'investment_user' => $transactionStats->investment_user ?? 0,
            'investment_number' => $transactionStats->investment_number ?? 0,
            'investment_amount' => $transactionStats->investment_amount ?? 0,
            'investment_commission_amount' => $commissionStats->investment_commission_amount ?? 0,
            'rebate_commission_amount' => $commissionStats->rebate_commission_amount ?? 0,
            'register_stats' => $userStats,
            'deposit_stats' => $depositStats,
            'claimed_commission_stats' => $commissionClaimStats,
        ];
    }

    public function getHourlyOrder(HourlyWithdrawRequest $request): array
    {
        $date = $request->date ?? Gmt8::today();
        $todayRange = Gmt8::toUtcTodayRange($date);

        // Base query
        $query = Order::query()
            ->select([
                DB::raw('EXTRACT(HOUR FROM orders.created_at) as hour'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('SUM(orders.total_amount) as total_amount'),
                DB::raw('COUNT(DISTINCT orders.user_id) as unique_users'),
            ])
            ->whereBetween('orders.created_at', $todayRange);

        // Apply status filter if provided
        $query->where('orders.status', OrderStatus::Processed->value);
        $query->whereIn('orders.payment_type', ['direct', 'wallet']);
        $query->leftJoin('wallets', function (JoinClause $join) {
            $join->on('wallets.id', '=', 'orders.wallet_id')
                ->where('wallets.type', '=', WalletType::General->value);
        });
        $query->where(function (Builder $query) {
            $query->where('wallets.type', WalletType::General->value)
                ->orWhereNull('wallets.type');
        });
        $query->join('products', 'products.id', '=', 'orders.product_id');

        // Group by hour and get results
        $results = $query->groupBy(DB::raw('EXTRACT(HOUR FROM orders.created_at)'))
            ->get()
            ->each(function ($item) {
                $item->hour = Carbon::now()->setHour($item->hour)->tz('Asia/Taipei')->hour;
            })
            ->keyBy('hour');

        // Initialize array with all 24 hours
        $hourlyData = collect(range(0, 23))->map(function ($hour) use ($results) {
            $hourData = $results->get($hour);

            return [
                'hour' => $hour,
                'total_amount' => $hourData ? (float) $hourData->total_amount : 0,
                'transaction_count' => $hourData ? $hourData->transaction_count : 0,
                'unique_users' => $hourData ? $hourData->unique_users : 0,
            ];
        })->values();

        // Calculate totals
        $totals = $hourlyData->reduce(function ($carry, $item) {
            return [
                'total_amount' => $carry['total_amount'] + $item['total_amount'],
                'total_transactions' => $carry['total_transactions'] + $item['transaction_count'],
            ];
        }, ['total_amount' => 0, 'total_transactions' => 0]);

        return [
            'date' => $date,
            'hourly_data' => $hourlyData,
            'summary' => [
                'total_amount' => $totals['total_amount'],
                'total_transactions' => $totals['total_transactions'],
            ],
        ];
    }

    public function getUserStatistic(HourlyWithdrawRequest $request): array
    {
        $date = $request->date ?? Gmt8::today();
        $todayRange = Gmt8::toUtcTodayRange($date);

        $totalMembers = User::query()->whereBetween('created_at', $todayRange)->count();
        $totalMembersAllTime = User::query()->count();
        $totalUserActiveToday = UserLog::query()
            ->whereBetween('time', $todayRange)
            ->distinct('user_id')
            ->count('user_id');

        return [
            'total_members' => $totalMembers,
            'total_member_all_time' => $totalMembersAllTime,
            'total_user_active_today' => $totalUserActiveToday,
        ];
    }

    public function getRegisterUserStatistic(HourlyWithdrawRequest $request): array
    {
        $date = $request->date ?? Gmt8::today();
        $todayRange = Gmt8::toUtcTodayRange($date);
        $query = User::query()
            ->select([
                DB::raw('EXTRACT(HOUR FROM created_at) as hour'),
                DB::raw('COUNT(*) as member_count'),
            ])
            ->whereBetween('created_at', $todayRange);
        $results = $query->groupBy(DB::raw('EXTRACT(HOUR FROM created_at)'))
            ->get()
            ->each(function ($item) {
                $item->hour = Carbon::now()->setHour($item->hour)->tz('Asia/Taipei')->hour;
            })
            ->keyBy('hour');
        $hourlyData = collect(range(0, 23))->map(function ($hour) use ($results) {
            $hourData = $results->get($hour);

            return [
                'hour' => $hour,
                'member_count' => $hourData ? $hourData->member_count : 0,
            ];
        })->values();

        return [
            'date' => $date,
            'hourly_data' => $hourlyData,
        ];
    }

    public function getWalletSummary($filters)
    {
        $query = DB::query();
        $remainProfit = Wallet::groupBy('type')
            ->select(['type', DB::raw('sum(profit) as remain_profit')]);
        $withdraw = Withdraw::join('wallets', 'wallets.id', '=', 'withdraws.wallet_id')
            ->where('withdraws.status', 2)
            ->groupBy('wallets.type')
            ->select(['wallets.type as type', DB::raw('sum(withdraws.amount) as total_approved_withdraw')]);
        $pendingWithdraw = Withdraw::join('wallets', 'wallets.id', '=', 'withdraws.wallet_id')
            ->where('withdraws.status', 0)
            ->groupBy('wallets.type')
            ->select(['wallets.type as type', DB::raw('sum(withdraws.amount) as total_pending_withdraw')]);
        $totalProfit = TransactionLog::from('transaction_logs as t')
            ->join('wallets as w', 'w.id', '=', 't.wallet_id')
            ->where('t.type', TransactionType::OperationServiceProfit->value)
            ->groupBy('w.type')
            ->select(['w.type as type', DB::raw('sum(t.amount) as total_profit')]);
        $totalCommission = TransactionLog::from('transaction_logs as t')
            ->join('wallets as w', 'w.id', '=', 't.wallet_id')
            ->where('t.type', TransactionType::Rebate->value)
            ->groupBy('w.type')
            ->select(['w.type as type', DB::raw('sum(t.amount) as total_commission')]);

        $summaryResult = $query->fromSub($remainProfit, 'remain_profit')
            ->leftJoinSub($withdraw, 'withdraw', 'remain_profit.type', '=', 'withdraw.type')
            ->leftJoinSub($pendingWithdraw, 'pending_withdraw', 'remain_profit.type', '=', 'pending_withdraw.type')
            ->leftJoinSub($totalProfit, 'total_profit', 'remain_profit.type', '=', 'total_profit.type')
            ->leftJoinSub($totalCommission, 'total_commission', 'remain_profit.type', '=', 'total_commission.type')
            ->select([
                'remain_profit.type',
                'remain_profit.remain_profit',
                'withdraw.total_approved_withdraw',
                'pending_withdraw.total_pending_withdraw',
                'total_profit.total_profit',
                'total_commission.total_commission',
            ])
            ->get();
        $summaryResult = $summaryResult->each(function ($item) {
            switch ($item->type) {
                case WalletType::Deposit->value:
                case WalletType::Reward->value:
                case WalletType::Promotion->value:
                case WalletType::Profit->value:
                case WalletType::Partnership->value:
                    unset($item->total_commission);
                    $item->remain_profit = Math::formatNumber($item->remain_profit ?? 0, true);
                    $item->total_approved_withdraw = Math::formatNumber($item->total_approved_withdraw ?? 0, true);
                    $item->total_pending_withdraw = Math::formatNumber($item->total_pending_withdraw ?? 0, true);
                    $item->total_profit = Math::formatNumber($item->total_profit ?? 0, true);
                    break;
                case WalletType::Commission->value:
                    $item->remain_profit = Math::formatNumber($item->remain_profit ?? 0, true);
                    $item->total_approved_withdraw = Math::formatNumber($item->total_approved_withdraw ?? 0, true);
                    $item->total_pending_withdraw = Math::formatNumber($item->total_pending_withdraw ?? 0, true);
                    $item->total_profit = Math::formatNumber($item->total_commission ?? 0, true);
                    unset($item->total_commission);
            }
        });
        // $saudiSummary = $this->saudiSummary($filters['start_date'], $filters['end_date']);
        // $generalSummary = $this->generalSummary($filters['start_date'], $filters['end_date']);
        // $commissionSummary = $this->commissionSummary($filters['start_date'], $filters['end_date']);

        return [
            'profit_summary' => $summaryResult,
            // 'saudi_summary' => $saudiSummary,
            // 'general_summary' => $generalSummary,
            // 'commission_summary' => $commissionSummary,
        ];
    }

    public function getUserGlobalStatistic()
    {
        $lastHour = Gmt8::toUtcHourRange(Gmt8::nowGmt8Instance()->subHour()->toDateTimeString());
        $current = Gmt8::toUtcHourRange(Gmt8::nowGmt8Instance()->toDateTimeString());
        $yesterday = Gmt8::toUtcTodayRange(Gmt8::nowGmt8Instance()->subDay()->toDateTimeString());
        $today = Gmt8::toUtcTodayRange(Gmt8::nowGmt8Instance()->toDateTimeString());
        $dayStats = User::select(DB::raw('COUNT(*) AS total'))
            ->selectSub(function ($query) use ($yesterday, $today) {
                $query->selectRaw("
            CASE
                WHEN created_at BETWEEN ? AND ? THEN 'today'
                WHEN created_at BETWEEN ? AND ? THEN 'yesterday'
            END
        ", [
                    $today[0], $today[1], // today
                    $yesterday[0], $yesterday[1], // yesterday
                ]);
            }, 'date_range')
            ->groupBy('date_range')
            ->whereBetween('created_at', [$yesterday[0], $today[1]]);
        $hours = User::select(DB::raw('COUNT(*) AS total'))
            ->selectSub(function ($query) use ($current, $lastHour) {
                $query->selectRaw("
            CASE
                WHEN created_at BETWEEN ? AND ? THEN 'last_hour'
                WHEN created_at BETWEEN ? AND ? THEN 'current'
            END
        ", [
                    $lastHour[0], $lastHour[1], // last_hour
                    $current[0], $current[1], // today
                ]);
            }, 'date_range')
            ->groupBy('date_range')
            ->whereBetween('created_at', [$lastHour[0], $current[1]])
            ->union($dayStats);
        $results = $hours->get();
        $userStats = [
            'today' => 0,
            'yesterday' => 0,
            'last_hour' => 0,
            'current' => 0,
        ];
        foreach ($results as $result) {
            match ($result->date_range) {
                'current' => $userStats['current'] = $result->total,
                'last_hour' => $userStats['last_hour'] = $result->total,
                'yesterday' => $userStats['yesterday'] = $result->total,
                'today' => $userStats['today'] = $result->total
            };
        }

        return $userStats;
    }

    public function depositStats(): array
    {
        $lastHour = Gmt8::toUtcHourRange(Gmt8::nowGmt8Instance()->subHour()->toDateTimeString());
        $current = Gmt8::toUtcHourRange(Gmt8::nowGmt8Instance()->toDateTimeString());
        $yesterday = Gmt8::toUtcTodayRange(Gmt8::nowGmt8Instance()->subDay()->toDateTimeString());
        $today = Gmt8::toUtcTodayRange(Gmt8::nowGmt8Instance()->toDateTimeString());

        return [
            'current' => Math::formatNumber(DepositRequests::whereBetween('created_at', $current)->where('status', 'success')
                ->sum('amount'), true),
            'last_hour' => Math::formatNumber(DepositRequests::whereBetween('created_at', $lastHour)->where('status', 'success')
                ->sum('amount'), true),
            'today' => Math::formatNumber(DepositRequests::whereBetween('created_at', $today)->where('status', 'success')
                ->sum('amount'), true),
            'yesterday' => Math::formatNumber(DepositRequests::whereBetween('created_at', $yesterday)->where('status', 'success')
                ->sum('amount'), true),
        ];
    }

    public function getDepositSummary($startDate = null, $endDate = null)
    {
        $depositQuery = DepositRequests::query();
        $depositQuery->select([
            DB::raw('COUNT(DISTINCT user_id) as deposit_user'),
            DB::raw('COUNT(*) as deposit_number'),
            DB::raw('SUM(amount) as deposit_amount'),
        ]);

        if ($startDate and $endDate) {
            $depositQuery->whereBetween('created_at', [$startDate, $endDate]);
        }
        $depositQuery->where('status', 'success');

        return $depositQuery->first();
    }

    protected function saudiSummary($startDate = null, $endDate = null)
    {
        $claimedSaudiQuery = Commission::where('category_id', 2)
            ->where('type', CommissionType::Investment->value)
            ->whereNotNull('processed_at')
            ->whereNotNull('expired_at')
            ->where('status', 2);
        $claimedSaudiDailyQuery = $claimedSaudiQuery->clone()
            ->groupBy(['user_id', 'claimed_date'])
            ->whereBetween('claimed_at', [$startDate, $endDate])
            ->select([
                'user_id',
                DB::raw("date(CONVERT_TZ (claimed_at, '+00:00','+08:00')) as claimed_date"),
                DB::raw('sum(amount) as claimed_amount'),
            ]);
        $claimedSaudiQuery->groupBy('user_id')
            ->select(['user_id', DB::raw('sum(amount) as total_claimed')]);
        $claimedSaudiDaily = DB::query()->fromSub($claimedSaudiDailyQuery, 'saudi_claimed_daily')
            ->groupBy('claimed_date')
            ->select(['claimed_date', DB::raw('count(user_id) as claimed_users'), DB::raw('sum(claimed_amount) as claimed_amount')])
            ->get();
        $claimedSaudiDaily = collect(CarbonPeriod::create(Gmt8::toGmt8($startDate), Gmt8::toGmt8($endDate)))
            ->map(function (Carbon $date) use ($claimedSaudiDaily) {
                $claimedData = $claimedSaudiDaily->where('claimed_date', $date->toDateString())->first();

                return [
                    'date' => $date->toDateString(),
                    'users' => $claimedData ? $claimedData->claimed_users : 0,
                    'amount' => $claimedData ? $claimedData->claimed_amount : '0.00',
                ];
            });
        $claimedSaudi = DB::query()->fromSub($claimedSaudiQuery, 'saudi_claimed')
            ->select([
                DB::raw("'saudi' as type"),
                DB::raw('count(user_id) as claimed_users'),
                DB::raw('sum(total_claimed) as total_claimed'),
            ]);

        $withdrawUserSub = Wallet::from('wallets as w')
            ->join('withdraws as wd', 'w.id', '=', 'wd.wallet_id')
            ->where('w.type', WalletType::Saudi->value)
            ->where('wd.status', 2)
            ->groupBy('wd.user_id')
            ->select([
                'wd.user_id',
                DB::raw('sum(wd.amount) as withdraw_amount'),
            ]);
        $withdrawUserQuery = DB::query()
            ->fromSub($withdrawUserSub, 'withdraw')
            ->select([
                DB::raw('count(user_id) as withdraw_users'),
                DB::raw('coalesce(sum(withdraw_amount)) as withdraw_amount'),
                DB::raw("'saudi' as type"),
            ]);
        $summary = DB::query()
            ->fromSub($claimedSaudi, 'claimed')
            ->joinSub($withdrawUserQuery, 'withdraw', 'claimed.type', '=', 'withdraw.type')
            ->select([
                'claimed_users',
                'total_claimed',
                'withdraw_users',
                'withdraw_amount',
            ])
            ->first();
        $summary->remain_profit = $summary->total_claimed - $summary->withdraw_amount;
        $summary->un_withdraw_users = $summary->claimed_users - $summary->withdraw_users;

        return [
            'summary' => $summary,
            'daily' => $claimedSaudiDaily,
        ];
    }

    public function generalSummary($startDate = null, $endDate = null)
    {
        $releaseProfit = Commission::query()
            ->where('type', CommissionType::Investment->value)
            ->where('status', 2);
        $releaseProfitDailyQuery = $releaseProfit->clone()
            ->groupBy(['user_id', 'release_date'])
            ->whereBetween('processed_at', [$startDate, $endDate])
            ->select([
                'user_id',
                DB::raw("date(CONVERT_TZ (processed_at, '+00:00','+08:00')) as release_date"),
                DB::raw('sum(amount) as release_amount'),
            ]);
        $releaseProfit->groupBy('user_id')
            ->select(['user_id', DB::raw('sum(amount) as total_release')]);
        $releaseProfitDaily = DB::query()->fromSub($releaseProfitDailyQuery, 'general_claimed_daily')
            ->groupBy('release_date')
            ->select(['release_date', DB::raw('count(user_id) as purchased_users'), DB::raw('sum(release_amount) as release_amount')])
            ->get();
        $releaseProfitDaily = collect(CarbonPeriod::create(Gmt8::toGmt8($startDate), Gmt8::toGmt8($endDate)))
            ->map(function (Carbon $date) use ($releaseProfitDaily) {
                $releaseData = $releaseProfitDaily->where('release_date', $date->toDateString())->first();

                return [
                    'date' => $date->toDateString(),
                    'users' => $releaseData ? $releaseData->purchased_users : 0,
                    'amount' => $releaseData ? $releaseData->release_amount : '0.00',
                ];
            });

        $releaseProfit = DB::query()->fromSub($releaseProfit, 'general_claimed')
            ->select([
                DB::raw("'general' as type"),
                DB::raw('count(user_id) as purchased_users'),
                DB::raw('sum(total_release) as release_amount'),
            ]);

        $withdrawUserSub = Wallet::from('wallets as w')
            ->join('withdraws as wd', 'w.id', '=', 'wd.wallet_id')
            // ->where('w.type', WalletType::General->value)
            ->where('wd.status', 2)
            ->groupBy('wd.user_id')
            ->select([
                'wd.user_id',
                DB::raw('sum(wd.amount) as withdraw_amount'),
            ]);
        $withdrawUserQuery = DB::query()
            ->fromSub($withdrawUserSub, 'withdraw')
            ->select([
                DB::raw('count(user_id) as withdraw_users'),
                DB::raw('coalesce(sum(withdraw_amount)) as withdraw_amount'),
                DB::raw("'general' as type"),
            ]);

        $summary = DB::query()
            ->fromSub($releaseProfit, 'claimed')
            ->joinSub($withdrawUserQuery, 'withdraw', 'claimed.type', '=', 'withdraw.type')
            ->select([
                'purchased_users',
                'release_amount',
                'withdraw_users',
                'withdraw_amount',
            ])
            ->first();
        $summary->remain_profit = $summary->release_amount - $summary->withdraw_amount;
        $summary->un_withdraw_users = $summary->purchased_users - $summary->withdraw_users;

        return [
            'summary' => $summary,
            'daily' => $releaseProfitDaily,
        ];
    }

    protected function commissionSummary($startDate = null, $endDate = null)
    {
        $releaseProfit = Commission::query()
            ->where('type', CommissionType::Rebate->value)
            ->where('status', 2);
        $releaseProfitDailyQuery = $releaseProfit->clone()
            ->groupBy(['user_id', 'release_date'])
            ->whereBetween('processed_at', [$startDate, $endDate])
            ->select([
                'user_id',
                DB::raw("date(CONVERT_TZ (processed_at, '+00:00','+08:00')) as release_date"),
                DB::raw('sum(amount) as release_amount'),
            ]);
        $releaseProfit->groupBy('user_id')
            ->select(['user_id', DB::raw('sum(amount) as total_rebate')]);
        $releaseProfitDaily = DB::query()->fromSub($releaseProfitDailyQuery, 'general_claimed_daily')
            ->groupBy('release_date')
            ->select(['release_date', DB::raw('count(user_id) as purchased_users'), DB::raw('sum(release_amount) as release_amount')])
            ->get();
        $releaseProfitDaily = collect(CarbonPeriod::create(Gmt8::toGmt8($startDate), Gmt8::toGmt8($endDate)))
            ->map(function (Carbon $date) use ($releaseProfitDaily) {
                $releaseData = $releaseProfitDaily->where('release_date', $date->toDateString())->first();

                return [
                    'date' => $date->toDateString(),
                    'users' => $releaseData ? $releaseData->purchased_users : 0,
                    'amount' => $releaseData ? $releaseData->release_amount : '0.00',
                ];
            });

        $releaseProfit = DB::query()->fromSub($releaseProfit, 'general_claimed')
            ->select([
                DB::raw("'general' as type"),
                DB::raw('count(user_id) as rebate_users'),
                DB::raw('sum(total_rebate) as rebate_amount'),
            ]);

        $withdrawUserSub = Wallet::from('wallets as w')
            ->join('withdraws as wd', 'w.id', '=', 'wd.wallet_id')
            ->where('w.type', WalletType::Commission->value)
            ->where('wd.status', 2)
            ->groupBy('wd.user_id')
            ->select([
                'wd.user_id',
                DB::raw('sum(wd.amount) as withdraw_amount'),
            ]);
        $withdrawUserQuery = DB::query()
            ->fromSub($withdrawUserSub, 'withdraw')
            ->select([
                DB::raw('count(user_id) as withdraw_users'),
                DB::raw('coalesce(sum(withdraw_amount)) as withdraw_amount'),
                DB::raw("'general' as type"),
            ]);

        $summary = DB::query()
            ->fromSub($releaseProfit, 'claimed')
            ->joinSub($withdrawUserQuery, 'withdraw', 'claimed.type', '=', 'withdraw.type')
            ->select([
                'rebate_users',
                'rebate_amount',
                'withdraw_users',
                'withdraw_amount',
            ])
            ->first();
        $summary->remain_profit = $summary->rebate_amount - $summary->withdraw_amount;
        $summary->un_withdraw_users = $summary->rebate_users - $summary->withdraw_users;

        return [
            'summary' => $summary,
            'daily' => $releaseProfitDaily,
        ];
    }
}
