<?php

namespace App\Services;

use App\Models\Otp;
use Illuminate\Pagination\LengthAwarePaginator;

class OtpService
{
    public function getList(array $filters = []): LengthAwarePaginator
    {
        $limit = $filters['limit'] ?? 10;
        $query = Otp::query()->orderBy('created_at', 'DESC');

        if (!empty($filters['phone'])) {
            $query->where('phone', $filters['phone']);
        }

        return $query->paginate($limit);
    }
}
