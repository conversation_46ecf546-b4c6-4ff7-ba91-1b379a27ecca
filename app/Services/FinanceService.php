<?php

namespace App\Services;

use App\Constants\TransactionType;
use App\Models\Commission;
use App\Models\TransactionLog;

class FinanceService
{
    public function getTransactions(array $filter, $pageSize = 10)
    {
        $query = TransactionLog::with(['user', 'wallet']);
        if (isset($filter['start_date']) && isset($filter['end_date'])) {
            $query->whereBetween('created_at', [$filter['start_date'], $filter['end_date']]);
        }
        if (isset($filter['phone'])) {
            $query->whereRelation('user', 'phone', 'like', "%{$filter['phone']}%");
        }

        if (isset($filter['name'])) {
            $query->whereRelation('user', 'name', 'like', "%{$filter['name']}%");
        }
        if (isset($filter['transaction_type'])) {
            $query->where('type', $filter['transaction_type']);
        }

        if (isset($filter['wallet_type'])) {
            $query->whereHas('wallet', function ($q) use ($filter) {
                $q->where('type', $filter['wallet_type']);
            });
        }

        return $query->orderByDesc('created_at')->paginate($pageSize);
    }

    public function getCommissions(array $filter, $pageSize = 10)
    {
        $query = Commission::with('user');
        if (isset($filter['start_date']) && isset($filter['end_date'])) {
            $query->whereBetween('created_at', [$filter['start_date'], $filter['end_date']]);
        }

        if (isset($filter['name'])) {
            $query->whereRelation('user', 'name', 'like', "%{$filter['name']}%");
        }

        if (isset($filter['phone'])) {
            $query->whereRelation('user', 'phone', 'like', "%{$filter['phone']}%");
        }

        return $query->orderByDesc('created_at')->paginate($pageSize);
    }

    public function getRebate(array $filter, $pageSize = 10)
    {
        $filter['transaction_type'] = TransactionType::Rebate->value;

        return $this->getTransactions($filter, $pageSize);
    }
}
