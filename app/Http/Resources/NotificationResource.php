<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /* @var LengthAwarePaginator $data */
        $data = $this->resource;
        $responseData = $data->getCollection()->map(function ($item) {
            if ($item->attachments) {
                $images = $item->attachments->map(function ($attachment) {
                    /* @var \App\Models\Attachment $attachment */
                    return [
                        'id' => $attachment->id,
                        'name' => $attachment->name,
                        'url' => $attachment->path,
                    ];
                });
            } else {
                $images = [];
            }

            return [
                'id' => $item->id,
                'title' => $item->title,
                'message' => $item->message,
                'sort' => $item->sort,
                'status' => $item->status,
                'images' => $images,
                'for_official_community' => $item->for_official_community,
            ];
        });
        $data->setCollection($responseData);

        return $data->toArray();
    }
}
