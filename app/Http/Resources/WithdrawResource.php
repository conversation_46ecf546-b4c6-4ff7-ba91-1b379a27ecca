<?php

namespace App\Http\Resources;

use App\Utils\Math;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;

class WithdrawResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return LengthAwarePaginator
     */
    public function toArray(Request $request)
    {
        /* @var LengthAwarePaginator $data */
        $data = $this->resource;

        $data->getCollection()->map(function ($item) {
            $item->amount = Math::formatNumber($item->amount, 2);
            $item->accumulated_withdraw_amount = Math::formatNumber($item->accumulated_withdraw_amount, 2);
            $item->total_withdraw = Math::formatNumber($item->total_withdraw, 2);
            $item->total_deposits = Math::formatNumber($item->total_deposits, 2);
            $item->general_wallet_profit = Math::formatNumber($item->general_wallet_profit, 2);
            $item->commission_wallet_profit = Math::formatNumber($item->commission_wallet_profit, 2);
            $item->saudi_wallet_profit = Math::formatNumber($item->saudi_wallet_profit, 2);
            $item->subordinate_deposit_amount = Math::formatNumber($item->subordinate_deposit_amount, 2);
        });

        return $data;
    }
}
