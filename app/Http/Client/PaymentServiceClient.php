<?php

namespace App\Http\Client;

use App\Models\PaymentChannel;
use App\Utils\CommonUtil;
use Exception;
use Illuminate\Support\Facades\Http;

class PaymentServiceClient
{
    protected string $domain;

    public function __construct()
    {
        $this->domain = CommonUtil::parseDomain(config('services.payment_service.domain'));
    }

    public function post(string $uri, array $payload = [], ?PaymentChannel $paymentChannel = null): array
    {
        if ($paymentChannel) {
            $payload['merchantInfo'] = [
                'merchantNo' => $paymentChannel->merchant_name, // Merchant number
                'subMerchantNo' => $paymentChannel->sub_merchant_name,
                'merchantPrivateKey' => $paymentChannel->merchant_private_key,
                'merchantPublicKey' => $paymentChannel->merchant_public_key,
                'channelPrivateKey' => $paymentChannel->platform_private_key,
                'channelPublicKey' => $paymentChannel->platform_public_key,
                'merchantExpansionA' => $paymentChannel->extend_prop_1,
                'merchantExpansionB' => $paymentChannel->extend_prop_2,
                'merchantExpansionC' => $paymentChannel->extend_prop_3,
                'merchantExpansionD' => $paymentChannel->extend_prop_4,
            ];
        }
        $payload = CommonUtil::removeNullValues($payload);

        // Send the POST request
        try {
            $response = Http::timeout(10)->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Api-Key' => config('services.payment_service.api_key'),
            ])->post($this->domain . $uri, $payload);
        } catch (Exception $exception) {
            abort($exception->getCode(), $exception->getMessage());
        }

        // Check for errors and handle the response
        $data = $response->json();
        if ($response->failed() || ($response->status() !== 200)) {
            abort($response->status(), $data['message'] ?? __('messages.unknown_error'));
        }

        if (empty($data['status'])) {
            abort(400, $data['message'] ?? __('messages.unknown_error'));
        }

        return $data;
    }

    public function get(string $uri, array $queries = [])
    {
        try {
            // Send the GET request
            $response = Http::timeout(10)->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Api-Key' => config('services.payment_service.api_key'),
            ])->get($this->domain . $uri, $queries);

            // Check for errors and handle the response
            $data = $response->json();
            if ($response->failed() || ($response->status() !== 200)) {
                throw new Exception($data['message'] ?? __('messages.unknown_error'), $response->status());
            }

            return $data;
        } catch (Exception $exception) {
            if ($exception->getCode() === 504) {
                abort(504, __('messages.service_timeout'));
            }
            $message = $exception->getCode() >= 500 ? __('messages.integration_service_error') : $exception->getMessage();
            abort($exception->getCode(), $message);
        }
    }
}
