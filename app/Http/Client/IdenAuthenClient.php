<?php

namespace App\Http\Client;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class IdenAuthenClient
{
    public function sendRequest($body)
    {
        $url = config('services.ali_cloud_api.iden_auth_url');
        $appCode = config('services.ali_cloud_api.app_code');

        // Define headers as per the API requirements
        $headers = [
            'Authorization' => 'APPCODE ' . $appCode,
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        ];

        try {
            // Send the POST request with Laravel's Http client
            $response = Http::withHeaders($headers)
                ->asForm() // Ensures form-urlencoded content type
                ->post($url, $body);

            // Handle the response
            // Return the decoded JSON response data
            return $response->json();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return [
                'status' => false,
                'respCode' => '0010',
            ];
        }
    }
}
