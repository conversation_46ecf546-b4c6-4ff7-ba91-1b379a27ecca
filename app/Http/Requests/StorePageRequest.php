<?php

namespace App\Http\Requests;

use App\Models\Page;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class StorePageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $defaultLocale = config('locales.locales.default'); // Gets 'en'

        return [
            // Basic page fields
            'slug' => [
                'required',
                'string',
                'max:255',
                function (string $attribute, mixed $value, Closure $fail) {
                    $slugified = Str::slug($value, language: mb_strtolower(App::getLocale()));
                    $exists = Page::where('slug', $slugified)->exists();
                    if ($exists) {
                        $fail("The {$attribute} has already been taken.");
                    }
                },
            ],
            'active' => 'required|boolean',
            'thumbnail' => 'nullable|string', // File path only, not file upload

            // Translatable fields - only default language required
            'title' => 'required|array',
            "title.{$defaultLocale}" => 'required|string|max:255', // Dynamic: title.en
            'title.*' => 'nullable|string|max:255', // Other languages optional
            'description' => 'nullable|array',
            "description.{$defaultLocale}" => 'nullable|string|max:500', // Dynamic: description.en
            'description.*' => 'nullable|string|max:500',
            'content' => 'required|array',
            "content.{$defaultLocale}" => 'required|string', // Dynamic: content.en
            'content.*' => 'nullable|string', // Other languages optional
        ];
    }

    /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Page title is required.',
            'title.array' => 'Page title must be provided for multiple languages.',
            'title.en.required' => 'Title is required for the default language (English).',
            'title.*.max' => 'Title may not be greater than 255 characters.',

            'content.required' => 'Page content is required.',
            'content.array' => 'Page content must be provided for multiple languages.',
            'content.en.required' => 'Content is required for the default language (English).',

            'description.array' => 'Page description must be provided for multiple languages.',
            'description.*.max' => 'Description may not be greater than 500 characters.',

            'slug.required' => 'Page slug is required.',
            'slug.max' => 'Page slug may not be greater than 255 characters.',

            'active.required' => 'Active status is required.',
            'active.boolean' => 'Active status must be true or false.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'slug' => 'page slug',
            'active' => 'active status',
            'title' => 'page title',
            'description' => 'page description',
            'content' => 'page content',
        ];
    }
}
