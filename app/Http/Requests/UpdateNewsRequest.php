<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateNewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Basic news fields - all optional for updates
            'slug' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('news', 'slug')->ignore($this->news?->id),
            ],
            'active' => 'sometimes|boolean',
            'thumbnail' => 'sometimes|string|nullable', // File path only, not file upload
            'author' => 'sometimes|string|nullable|max:255',

            // Translatable fields - optional for updates (allows partial updates)
            'title' => 'sometimes|array',
            'title.*' => 'nullable|string|max:255',
            'description' => 'sometimes|array',
            'description.*' => 'nullable|string|max:500',
            'content' => 'sometimes|array',
            'content.*' => 'nullable|string',
        ];
    }

    /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'title.array' => 'News title must be provided for multiple languages.',
            'title.*.max' => 'Title may not be greater than 255 characters.',

            'content.array' => 'News content must be provided for multiple languages.',

            'description.array' => 'News description must be provided for multiple languages.',
            'description.*.max' => 'Description may not be greater than 500 characters.',

            'slug.max' => 'News slug may not be greater than 255 characters.',
            'slug.unique' => 'This news slug has already been taken.',

            'active.boolean' => 'Active status must be true or false.',

            'author.max' => 'Author name may not be greater than 255 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'slug' => 'news slug',
            'active' => 'active status',
            'title' => 'news title',
            'description' => 'news description',
            'content' => 'news content',
            'author' => 'author name',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $defaultLocale = config('locales.locales.default'); // Gets 'en'

            // Custom validation: If title is provided, ensure default language has content
            if ($this->has('title') && is_array($this->title)) {
                if (empty(trim($this->title[$defaultLocale] ?? ''))) {
                    $defaultLabel = config("locales.locale_metadata.{$defaultLocale}.label", ucfirst($defaultLocale));
                    $validator->errors()->add(
                        "title.{$defaultLocale}",
                        "Title is required for the default language ({$defaultLabel})."
                    );
                }
            }

            // Custom validation: If content is provided, ensure default language has content
            if ($this->has('content') && is_array($this->content)) {
                if (empty(trim($this->content[$defaultLocale] ?? ''))) {
                    $defaultLabel = config("locales.locale_metadata.{$defaultLocale}.label", ucfirst($defaultLocale));
                    $validator->errors()->add(
                        "content.{$defaultLocale}",
                        "Content is required for the default language ({$defaultLabel})."
                    );
                }
            }
        });
    }
}
