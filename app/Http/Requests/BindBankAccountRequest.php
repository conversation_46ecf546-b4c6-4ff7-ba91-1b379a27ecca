<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BindBankAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank_account_name' => ['required', 'string'],
            'phone' => ['required', 'string'],
            'bank_name' => ['required', 'string'],
            'bank_account' => ['required', 'string'],
        ];
    }
}
