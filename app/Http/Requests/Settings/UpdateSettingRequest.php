<?php

namespace App\Http\Requests\Settings;

use App\Constants\WithdrawFeeChargeType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'max_sub_level' => 'numeric',
            'rebate_commission_lv_1' => 'numeric',
            'rebate_commission_lv_2' => 'numeric',
            'rebate_commission_lv_3' => 'numeric',
            'rebate_commission_lv_4' => 'numeric',
            'rebate_commission_lv_5' => 'numeric',
            'rebate_commission_lv_6' => 'numeric',
            'first_login_message' => 'string',
            'app_title' => 'string',
            'app_stage' => 'string',
            'crm_link' => 'string',
            'referral_url' => 'string',
            'app_logo' => 'file|image|max:1024',
            'community_url' => 'string',
            'community_qr' => 'string',
            'app_banner' => 'file|image|max:2048',
            'apk_download_link' => 'string',
            'ios_download_link' => 'string',
            'brochure_file' => 'string',
            'wallet_general_rule' => 'string',
            'wallet_commission_rule' => 'string',
            'invite_subordinate_id_verification_reward' => 'numeric',
            'force_bypass_id_verification' => 'boolean',
            'face_recognition_reminder_message' => 'string',
            'withdraw_noticed' => 'string',
            'lucky_spin_rule' => 'string',
            'request_invitation_code' => 'bool',
            'not_allowed_phone_prefix' => 'string',
            'stock_value' => 'numeric',
            'max_subordinates_to_show' => 'numeric',
            'profile_ads_banner' => 'file|image|max:1024',
            'profile_ads_url' => 'nullable|string',
            'float_icon' => 'file|image|max:1024',
            'float_icon_link' => 'nullable|string',
            'reward_value' => 'numeric',
            'profit_rules' => 'string',
            'cs_working_hours' => 'string',
            'cs_img' => 'file|image|max:1024',
            'cs_btn_url' => 'string',
            'cs_website_url' => 'string',
            'max_operation_request_per_day' => 'numeric',
            'auto_release_delivery_request' => 'bool',
            'withdrawal_fee' => 'numeric',
            'withdrawal_min_amount' => 'numeric',
            'withdrawal_max_amount' => 'numeric',
            'reward_allow_to_withdraw' => 'bool',
            'commission_allow_to_withdraw' => 'bool',
            'partnership_allow_to_withdraw' => 'bool',
            'require_purchase_before_withdraw' => 'numeric',
            'min_withdraw_err_msg' => 'string',
            'product_require_err_msg' => 'string',
            'is_maintenance_mode' => 'bool',
            'maintenance_reason' => 'string',
            'maintenance_from_date' => 'date_format:Y-m-d H:i:s',
            'maintenance_to_date' => 'date_format:Y-m-d H:i:s',
            'allow_transfer_to_deposit_wallet' => 'bool',
            'auto_release_operation_request' => 'bool',
            'enable_operation_service_start_time' => 'nullable|date_format:Y-m-d H:i:s',
            'enable_operation_service_end_time' => 'nullable|date_format:Y-m-d H:i:s',
            'operation_order_profit' => 'numeric|min:1',
            'enable_operation_service_for_purchased_user' => 'bool',
            'operation_service_promotion_profit' => 'numeric|min:1',
            'operation_service_maximum_profit' => 'numeric|min:1',
            'promotion_allow_to_withdraw' => 'bool',
            'profit_allow_to_withdraw' => 'bool',
            'dedicated_link' => 'string',
            'share_product_price' => 'numeric|min:1',
            'share_product_interest_rate' => 'numeric|min:1',
            'share_product_weight' => 'numeric|min:1',
            'share_product_banner' => 'nullable|image|max:1024',
            'wallet_reward_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'wallet_promotion_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'wallet_commission_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'wallet_deposit_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'wallet_profit_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'wallet_partnership_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'loan_monthly_release_day' => 'numeric|max:31',
            'forbidden_deposit_time' => 'string',
            'reserved_fund_rate' => 'numeric|min:0.01|max:1',
            'operation_request_service_extra_profit' => 'numeric|min:1',
            'partnership_commission_rates' => 'string',
            'partner_brochure_link' => 'nullable|string|url',
            'elementary_partner_banner' => 'nullable|image|max:2048',
            'senior_partner_banner' => 'nullable|image|max:2048',
            'premium_partner_banner' => 'nullable|image|max:2048',
            'operation_request_profit' => 'numeric|min:1',
            'free_store_product_description' => 'string',
            'free_store_profit_rules' => 'string',
            'withdraw_stage_remind_message' => 'string|nullable',
            'contribute_customize_amount' => 'bool',
            'allow_contribute_all_balance' => 'bool',
            'allow_contribute_downgraded_vip_level' => 'bool',
            'general_allow_to_withdraw' => 'bool',

            'wallet_general_withdraw_day' => 'numeric|in:0,1,2,3,4,5,6,7',
            'maximum_withdraw_per_day' => 'numeric',
            'maximum_withdraw_times_per_day' => 'numeric',
            'withdraw_fee_charge_type' => ['string', Rule::in(WithdrawFeeChargeType::values())],
            'pending_withdrawal_after_change_passcode' => 'bool',
            'min_valid_subordinate_deposit' => 'numeric',
            'pending_withdrawal_period' => 'numeric|min:0',
        ];
    }
}
