<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreNewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $defaultLocale = config('locales.locales.default'); // Gets 'en' from config

        return [
            'active' => 'required|boolean',
            'thumbnail' => 'nullable|string', // File path only, not file upload
            'author' => 'nullable|string|max:255',

            // Translatable fields - only default language required
            'title' => 'required|array',
            "title.{$defaultLocale}" => 'required|string|max:255', // Dynamic: title.en
            'title.*' => 'nullable|string|max:255', // Other languages optional
            'description' => 'nullable|array',
            "description.{$defaultLocale}" => 'nullable|string|max:500', // Dynamic: description.en
            'description.*' => 'nullable|string|max:500',
            'content' => 'required|array',
            "content.{$defaultLocale}" => 'required|string', // Dynamic: content.en
            'content.*' => 'nullable|string', // Other languages optional
        ];
    }

    /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        $defaultLocale = config('locales.locales.default');
        $defaultLabel = config("locales.locale_metadata.{$defaultLocale}.label", ucfirst($defaultLocale));

        return [
            'title.required' => 'News title is required.',
            'title.array' => 'News title must be provided for multiple languages.',
            "title.{$defaultLocale}.required" => "Title is required for the default language ({$defaultLabel}).",
            'title.*.max' => 'Title may not be greater than 255 characters.',

            'content.required' => 'News content is required.',
            'content.array' => 'News content must be provided for multiple languages.',
            "content.{$defaultLocale}.required" => "Content is required for the default language ({$defaultLabel}).",

            'description.array' => 'News description must be provided for multiple languages.',
            'description.*.max' => 'Description may not be greater than 500 characters.',

            'slug.required' => 'News slug is required.',
            'slug.max' => 'News slug may not be greater than 255 characters.',

            'active.required' => 'Active status is required.',
            'active.boolean' => 'Active status must be true or false.',

            'author.max' => 'Author name may not be greater than 255 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'slug' => 'news slug',
            'active' => 'active status',
            'title' => 'news title',
            'description' => 'news description',
            'content' => 'news content',
            'author' => 'author name',
        ];
    }
}
