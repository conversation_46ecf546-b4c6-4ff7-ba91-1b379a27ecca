<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRewardSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'required_points' => 'required|numeric',
            'reward_amount' => 'required|numeric',
            'reward_type' => 'required|string',
            'reward_source' => 'required|string',
            'type' => 'required|string',
            'name' => 'required|string',
            'description' => 'required|string',
            'is_active' => 'required|boolean',
            'auto_release' => 'required|boolean',
            'wallet' => 'required|string',
        ];
    }
}
