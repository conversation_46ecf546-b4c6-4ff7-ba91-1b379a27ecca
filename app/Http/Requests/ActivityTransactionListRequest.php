<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ActivityTransactionListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'nullable|numeric',
            'limit' => 'nullable|numeric',
            'offset' => 'nullable|numeric',
            'filters' => 'nullable|array',
            'filters.phone' => 'nullable|string',
            'filters.name' => 'nullable|string',
            'filters.activity_type' => 'nullable|string',
            'filters.activity_cycle' => 'nullable|string',
            'filters.created_from' => 'nullable|date',
            'filters.created_to' => 'nullable|date',
        ];
    }
}
