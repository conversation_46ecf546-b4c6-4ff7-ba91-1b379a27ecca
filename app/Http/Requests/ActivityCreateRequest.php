<?php

namespace App\Http\Requests;

use App\Constants\Activities\ActivityTypes;
use App\Constants\WalletType;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ActivityCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'cycle' => 'required|string|max:255|in:daily,one_time',
            'type' => 'required|string|max:255|in:' . implode(',', ActivityTypes::values()),
            'amount' => 'required|integer|min:1',
            'wallet' => 'required|string|in:' . implode(',', WalletType::values()),
            'start_date' => 'required|date',
            'end_date' => 'nullable|date',
            'status' => 'required|integer|in:0,1',
            'daily_reward' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'numeric'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:0.10'),
            ],
            'consecutive_day' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'integer'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:2'),
            ],
            'consecutive_reward' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'numeric'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:0.01'),
            ],
            'min_vip_level' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'exists:vip_levels,id'),
            ],
        ];
    }

    protected function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            $startDate = $this->input('start_date');
            $endDate = $this->input('end_date');

            if ($startDate && $endDate && $startDate > $endDate) {
                $validator->errors()->add('start_date', 'The start date must be a date before or equal to the end date.');
            }
        });
    }
}
