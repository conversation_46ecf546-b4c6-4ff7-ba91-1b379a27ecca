<?php

namespace App\Http\Requests;

use App\Constants\WithdrawalFeeType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GroupUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Group fields
            'name' => 'required|string|max:255',

            // WithdrawSetting fields
            'withdrawal_week_days' => 'nullable|array',
            'withdrawal_week_days.*' => 'nullable|integer|between:0,6',
            'withdrawal_customize_amount' => 'nullable|array',
            'withdrawal_customize_amount.*' => 'numeric',
            'withdrawal_min_amount' => 'required|numeric|min:0',
            'withdrawal_max_amount' => 'required|numeric|min:0|gte:withdrawal_min_amount',
            'withdrawal_max_amount_per_day' => 'required|numeric|min:0',
            'withdrawal_fee' => 'required|numeric|min:0',
            'withdrawal_fee_charge_type' => ['required', Rule::enum(WithdrawalFeeType::class)],
            'free_withdrawal_times' => 'required|integer|min:0',
            'withdrawal_extra_fee_limit' => 'required|numeric|min:0',
            'withdrawal_extra_fee_percentage' => 'required|numeric|min:0|max:100',
            'daily_withdrawal_times' => 'required|integer|min:1',
            'enabled' => 'required|boolean',
            'withdrawal_start_time' => 'nullable|string|date_format:H:i:s',
            'withdrawal_end_time' => 'nullable|string|date_format:H:i:s|after:withdrawal_start_time',
            'tax_rate' => 'required|numeric|min:0',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Group name is required.',
            'withdrawal_week_days.required' => 'Withdrawal week days are required.',
            'withdrawal_week_days.*.integer' => 'Week days must be integers between 0 and 6.',
            'withdrawal_week_days.*.between' => 'Week days must be between 0 (Sunday) and 6 (Saturday).',
            'withdrawal_min_amount.required' => 'Minimum withdrawal amount is required.',
            'withdrawal_max_amount.required' => 'Maximum withdrawal amount is required.',
            'withdrawal_max_amount.gte' => 'Maximum withdrawal amount must be greater than or equal to minimum amount.',
            'withdrawal_end_time.after' => 'Withdrawal end time must be after start time.',
        ];
    }

    /**
     * Get custom attribute names for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'withdrawal_week_days' => 'withdrawal week days',
            'withdrawal_customize_amount' => 'custom withdrawal amounts',
            'withdrawal_min_amount' => 'minimum withdrawal amount',
            'withdrawal_max_amount' => 'maximum withdrawal amount',
            'withdrawal_max_amount_per_day' => 'daily maximum withdrawal amount',
            'withdrawal_fee' => 'withdrawal fee',
            'withdrawal_fee_charge_type' => 'withdrawal fee type',
            'free_withdrawal_times' => 'free withdrawal times',
            'withdrawal_extra_fee_limit' => 'extra fee limit',
            'withdrawal_extra_fee_percentage' => 'extra fee percentage',
            'daily_withdrawal_times' => 'daily withdrawal times',
            'withdrawal_start_time' => 'withdrawal start time',
            'withdrawal_end_time' => 'withdrawal end time',
        ];
    }
}
