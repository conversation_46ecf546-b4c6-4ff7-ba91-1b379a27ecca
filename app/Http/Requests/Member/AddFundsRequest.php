<?php

namespace App\Http\Requests\Member;

use App\Constants\WalletType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AddFundsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|min:0|numeric',
            'phones' => 'required|string',
            'wallet_type' => [
                Rule::in(WalletType::cases()),
                'required',
                'string',
            ],
            'type' => 'string|in:add,reduce,reward',
            'remark' => 'nullable',
        ];
    }
}
