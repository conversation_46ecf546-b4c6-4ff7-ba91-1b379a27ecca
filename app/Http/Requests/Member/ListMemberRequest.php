<?php

namespace App\Http\Requests\Member;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'member_type' => 'nullable|string|in:agent,individual',
            'member_level' => 'nullable|string|in:star,gold,silver,general',
            'partner_type' => 'nullable|string|in:agent,elementary,senior,premium',
            'agent_number' => 'nullable|string',
            'phone' => 'nullable|string',
            'email' => 'nullable|string|email',
            'sortBy' => 'nullable|string',
            'sort' => 'nullable|string|in:asc,desc',
            'vip_level_id' => 'nullable|integer',
        ];
    }
}
