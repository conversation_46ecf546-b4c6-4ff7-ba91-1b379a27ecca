<?php

namespace App\Http\Requests\Member;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'password' => 'string|min:8|confirmed',
            'passcode' => 'digits:6|confirmed',
            'active' => 'bool',
            'locked_withdraw' => 'bool',
            'locked_deposit' => 'bool',
            'id_name' => 'string|required_if:id_name,true',
            'id_card' => 'string|regex:/[0-9]{17}[0-9X]{1}/i|unique:users,id_card',
            'sex' => 'numeric',
            'agent_type' => 'string|in:regular,regional_super_agent,silver_super_agent,gold_super_agent',
            'partner_type' => 'string|in:agent,elementary,senior,premium',
        ];
    }

    public function messages(): array
    {
        return [
            'id_card' => 'member.id_card_taken',
        ];
    }
}
