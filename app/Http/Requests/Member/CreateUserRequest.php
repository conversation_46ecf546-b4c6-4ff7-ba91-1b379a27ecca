<?php

namespace App\Http\Requests\Member;

use App\Constants\AccountType;
use Illuminate\Foundation\Http\FormRequest;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $inviteCodeValidation = 'required|numeric|digits:8|exists:users,invitation_code';

        return [
            'email' => 'nullable|email|unique:users,email',
            'phone' => [
                'required',
                'unique:users,phone', // Ensure the phone is not already registered in the users table
            ],
            'password' => 'required|string|min:8|confirmed',
            'invitation_code' => $inviteCodeValidation,
            'transaction_passcode' => 'required|numeric|digits:6|confirmed',
            'account_type' => 'required|in:' . implode(',', array_column(AccountType::cases(), 'value')),
        ];
    }

    public function messages(): array
    {
        return [
            // Email validation messages
            'email.email' => __('messages.validation.email_invalid'),
            'email.unique' => __('messages.validation.email_already_registered'),

            // Phone validation messages
            'phone.required' => __('messages.validation.phone_required'),
            'phone.phone' => __('messages.validation.phone_invalid'),
            'phone.unique' => __('messages.phone_already_registered'),

            // Password validation messages
            'password.required' => __('messages.validation.password_required'),
            'password.string' => __('messages.validation.password_string'),
            'password.min' => __('messages.validation.password_min'),
            'password.confirmed' => __('messages.validation.password_confirmed'),

            // Invitation code validation messages
            'invitation_code.required' => __('messages.validation.invitation_code_required'),
            'invitation_code.numeric' => __('messages.validation.invitation_code_numeric'),
            'invitation_code.digits' => __('messages.validation.invitation_code_digits'),
            'invitation_code.exists' => __('messages.invalid_invitation_code'),

            // Transaction passcode validation messages
            'transaction_passcode.required' => __('messages.validation.transaction_passcode_required'),
            'transaction_passcode.numeric' => __('messages.validation.transaction_passcode_numeric'),
            'transaction_passcode.digits' => __('messages.validation.transaction_passcode_digits'),
            'transaction_passcode.confirmed' => __('messages.validation.transaction_passcode_confirmed'),

            // Rate limiting messages
            'phone.max_registrations_per_ip' => __('messages.too_many_registrations_from_ip'),
        ];
    }
}
