<?php

namespace App\Http\Requests\Coupon;

use Illuminate\Foundation\Http\FormRequest;

class StoreCouponRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'max:100', 'unique:coupons,code'],
            'discount_amount' => ['required', 'numeric', 'min:0', 'max:999999.99'],
            'discount_type' => ['required', 'string', 'in:percentage,fixed'],
            'qualification_amount' => ['nullable', 'numeric', 'min:0'],
            'available_at' => ['required', 'date'],
            'expired_at' => ['nullable', 'date'],
            'usage_limit' => ['nullable', 'numeric', 'min:0'],
            'active' => ['required', 'boolean'],
        ];
    }
}
