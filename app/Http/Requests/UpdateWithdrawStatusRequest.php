<?php

namespace App\Http\Requests;

use App\Constants\WithdrawStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateWithdrawStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => [
                'nullable',
                'in:0,2,3',
            ],
            'notes' => [
                'nullable',
                'string',
            ],
            'payment_channel_id' => [
                'nullable',
                'integer',
                Rule::requiredIf(function () {
                    return $this->request->get('status') == WithdrawStatus::Approved->value;
                }),
            ],
        ];
    }
}
