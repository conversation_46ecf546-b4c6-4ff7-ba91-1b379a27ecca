<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class PaymentChannelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if (is_string($this->request->get('logo'))) {
            $logoRule = 'required|string';
        } else {
            $logoRule = 'required|image|mimes:jpeg,png,jpg|max:2048';
        }

        return [
            'name' => 'required|string',
            'provider' => 'required|string',
            'active' => 'boolean',
            'for_deposit' => 'boolean',
            'for_withdraw' => 'boolean',
            'logo' => $logoRule,
            'minimum_amount' => 'required|numeric',
            'maximum_amount' => 'required|numeric',
            'sort' => 'required|numeric',
            'merchant_name' => 'nullable|string',
            'merchant_private_key' => 'nullable|string',
            'merchant_public_key' => 'nullable|string',
            'platform_private_key' => 'nullable|string',
            'platform_public_key' => 'nullable|string',
            'sub_merchant_name' => 'nullable|string',
            'extend_prop_1' => 'nullable|string',
            'extend_prop_2' => 'nullable|string',
            'extend_prop_3' => 'nullable|string',
            'extend_prop_4' => 'nullable|string',
            'min_user_vip_level' => 'nullable|numeric',
            'payment_organization_id' => 'numeric|exists:payment_organizations,id',
        ];
    }
}
