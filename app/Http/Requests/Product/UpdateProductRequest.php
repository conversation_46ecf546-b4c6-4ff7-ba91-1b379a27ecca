<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'rules' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'promotion_price' => 'nullable|numeric|min:0',
            'active' => 'required|boolean',
            'image' => 'image|max:2048',
            'daily_profit' => 'required|numeric|min:0',
            'reward' => 'required|numeric|min:0',
            'limited' => 'required|boolean',
            'type' => 'required|string|in:investment',
            'category_id' => 'required|exists:categories,id',
            'metadata' => 'nullable|array',
            'sort' => 'numeric|min:0',
        ];
    }
}
