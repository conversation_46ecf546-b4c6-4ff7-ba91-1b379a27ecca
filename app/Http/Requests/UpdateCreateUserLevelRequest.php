<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCreateUserLevelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|numeric',
            'name' => 'required|string',
            'min_points' => 'required|integer',
            'max_points' => 'required|integer',
            'level_order' => 'required|integer',
            // 'discount' => 'nullable|numeric|max:0.99|min:0.01',
            'withdraw_fee' => 'required|numeric|max:0.99|min:0.01',
            'min_withdraw_amount' => 'required|numeric|min:0',
        ];
    }
}
