<?php

namespace App\Http\Requests\VipLevelReward;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListVipLevelRewardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'nullable|integer|exists:users,id',
            'vip_level_id' => 'nullable|integer|exists:vip_levels,id',
            'user_name' => 'nullable|string|max:255',
            'email' => 'nullable|string|max:255',
            'vip_level_name' => 'nullable|string|max:255',
            'transaction_code' => 'nullable|string|max:255',
            'date_from' => 'nullable|date|before_or_equal:date_to',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'pageSize' => 'nullable|integer|min:1|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.integer' => 'User ID must be a valid integer.',
            'user_id.exists' => 'The selected user does not exist.',
            'vip_level_id.integer' => 'VIP Level ID must be a valid integer.',
            'vip_level_id.exists' => 'The selected VIP level does not exist.',
            'user_name.string' => 'User name must be a valid string.',
            'user_name.max' => 'User name cannot exceed 255 characters.',
            'vip_level_name.string' => 'VIP level name must be a valid string.',
            'vip_level_name.max' => 'VIP level name cannot exceed 255 characters.',
            'transaction_code.string' => 'Transaction code must be a valid string.',
            'transaction_code.max' => 'Transaction code cannot exceed 255 characters.',
            'date_from.date' => 'Date from must be a valid date.',
            'date_from.before_or_equal' => 'Date from must be before or equal to date to.',
            'date_to.date' => 'Date to must be a valid date.',
            'date_to.after_or_equal' => 'Date to must be after or equal to date from.',
            'pageSize.integer' => 'Page size must be a valid integer.',
            'pageSize.min' => 'Page size must be at least 1.',
            'pageSize.max' => 'Page size cannot exceed 100.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'user ID',
            'vip_level_id' => 'VIP level ID',
            'user_name' => 'user name',
            'vip_level_name' => 'VIP level name',
            'transaction_code' => 'transaction code',
            'date_from' => 'start date',
            'date_to' => 'end date',
            'pageSize' => 'page size',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional custom validation if needed
            if ($this->date_from && $this->date_to) {
                $dateFrom = \Carbon\Carbon::parse($this->date_from);
                $dateTo = \Carbon\Carbon::parse($this->date_to);

                // Check if date range is not too large (e.g., max 1 year)
                if ($dateFrom->diffInDays($dateTo) > 365) {
                    $validator->errors()->add('date_to', 'Date range cannot exceed 365 days.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set default pageSize if not provided
        if (!$this->has('pageSize') || $this->pageSize === null) {
            $this->merge([
                'pageSize' => 10,
            ]);
        }

        // Clean up empty string values to null for optional fields
        $this->merge([
            'user_id' => $this->user_id === '' ? null : $this->user_id,
            'vip_level_id' => $this->vip_level_id === '' ? null : $this->vip_level_id,
            'user_name' => $this->user_name === '' ? null : $this->user_name,
            'vip_level_name' => $this->vip_level_name === '' ? null : $this->vip_level_name,
            'transaction_code' => $this->transaction_code === '' ? null : $this->transaction_code,
            'date_from' => $this->date_from === '' ? null : $this->date_from,
            'date_to' => $this->date_to === '' ? null : $this->date_to,
        ]);
    }
}
