<?php

namespace App\Http\Requests\VipLevelReward;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ShowVipLevelRewardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // No additional validation needed for show method
            // Route model binding will handle the ID validation
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if the VIP level reward exists and user has permission to view it
            $vipLevelReward = $this->route('vip_level_reward') ?? $this->route('vipLevelReward');

            if (!$vipLevelReward) {
                $validator->errors()->add('id', 'VIP Level Reward not found.');
            }
        });
    }
}
