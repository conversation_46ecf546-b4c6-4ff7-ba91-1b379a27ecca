<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PaymentChannelListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'for_withdraw' => 'boolean',
            'for_deposit' => 'boolean',
            'active' => 'boolean',
            'available_merchant_name' => 'boolean',
            'available_merchant_private_key' => 'boolean',
            'user_id' => 'nullable|numeric',
            'bank_id' => 'nullable|numeric|exists:user_banks,id',
        ];
    }
}
