<?php

namespace App\Http\Requests\VipLevelCondition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateVipLevelConditionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vip_level_id' => 'sometimes|required|integer|exists:vip_levels,id',
            'remaining_balance_threshold' => 'sometimes|required|numeric|min:0',
            'total_deposit_threshold' => 'sometimes|required|numeric|min:0',
            'subordinates_balance_threshold' => 'sometimes|required|numeric|min:0',
            'subordinates_deposit_threshold' => 'sometimes|required|numeric|min:0',
            'subordinates_threshold' => 'sometimes|required|integer|min:0',
            'is_active' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'vip_level_id.required' => 'VIP level is required.',
            'vip_level_id.exists' => 'Selected VIP level does not exist.',
            'vip_level_id.unique' => 'A condition already exists for this VIP level. Only one condition per VIP level is allowed.',
            'remaining_balance_threshold.required' => 'Remaining balance threshold is required.',
            'remaining_balance_threshold.numeric' => 'Remaining balance threshold must be a number.',
            'remaining_balance_threshold.min' => 'Remaining balance threshold cannot be negative.',
            'total_deposit_threshold.required' => 'Total deposit threshold is required.',
            'total_deposit_threshold.numeric' => 'Total deposit threshold must be a number.',
            'total_deposit_threshold.min' => 'Total deposit threshold cannot be negative.',
            'subordinates_balance_threshold.required' => 'Subordinates balance threshold is required.',
            'subordinates_balance_threshold.numeric' => 'Subordinates balance threshold must be a number.',
            'subordinates_balance_threshold.min' => 'Subordinates balance threshold cannot be negative.',
            'subordinates_deposit_threshold.required' => 'Subordinates deposit threshold is required.',
            'subordinates_deposit_threshold.numeric' => 'Subordinates deposit threshold must be a number.',
            'subordinates_deposit_threshold.min' => 'Subordinates deposit threshold cannot be negative.',
            'subordinates_threshold.required' => 'Subordinates count threshold is required.',
            'subordinates_threshold.integer' => 'Subordinates count threshold must be an integer.',
            'subordinates_threshold.min' => 'Subordinates count threshold cannot be negative.',
            'is_active.boolean' => 'Active status must be true or false.',
        ];
    }
}
