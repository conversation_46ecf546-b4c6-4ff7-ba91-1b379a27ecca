<?php

namespace App\Http\Requests\VipLevel;

use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreVipLevelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $defaultLocale = config('locales.locales.default'); // Gets 'en'

        return [
            // Translatable fields - only default language required
            'name' => 'required|array',
            "name.{$defaultLocale}" => 'required|string|max:255', // Dynamic: name.en
            'name.*' => 'nullable|string|max:255', // Other languages optional
            'description' => 'nullable|array',
            "description.{$defaultLocale}" => 'nullable|string|max:1000', // Dynamic: description.en
            'description.*' => 'nullable|string|max:1000',

            // Non-translatable fields
            'level_order' => [
                'nullable',
                'integer',
                'min:0',
                function (string $attribute, mixed $value, Closure $fail) {
                    if ($value !== null) {
                        $exists = \App\Models\VipLevel::where('level_order', $value)->exists();
                        if ($exists) {
                            $fail("The {$attribute} has already been taken.");
                        }
                    }
                },
            ],
            'discount' => 'required|numeric|min:0|max:100',
            'withdraw_fee' => 'required|numeric|min:0|max:100',
            'min_withdraw_amount' => 'required|numeric|min:0',
            'contribute_amount_min' => 'nullable|numeric|min:0',
            'contribute_amount_max' => 'nullable|numeric|min:0',
            'contribute_amount_fixed' => 'nullable|numeric|min:0',
            'profit_rate_bonus' => 'required|numeric|min:0|max:100',
            'level_up_reward' => 'required|numeric|min:0',
            'withdrawal_week_days' => 'nullable|array',
            'withdrawal_week_days.*' => 'nullable|integer|between:0,6',
            'withdrawal_customize_amount' => 'nullable|array',
            'withdrawal_customize_amount.*' => 'numeric',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        $defaultLocale = config('locales.locales.default');

        return [
            // Multi-language field messages
            'name.required' => 'VIP level name is required.',
            'name.array' => 'VIP level name must be provided for multiple languages.',
            "name.{$defaultLocale}.required" => 'Name is required for the default language.',
            'name.*.max' => 'Name may not be greater than 255 characters.',

            'description.array' => 'VIP level description must be provided for multiple languages.',
            'description.*.max' => 'Description may not be greater than 1000 characters.',

            // Non-translatable field messages
            'level_order.unique' => 'Level order must be unique.',
            'discount.required' => 'Discount percentage is required.',
            'discount.max' => 'Discount cannot exceed 100%.',
            'withdraw_fee.required' => 'Withdraw fee is required.',
            'withdraw_fee.max' => 'Withdraw fee cannot exceed 100%.',
            'min_withdraw_amount.required' => 'Minimum withdraw amount is required.',
            'profit_rate_bonus.required' => 'Profit rate bonus is required.',
            'profit_rate_bonus.max' => 'Profit rate bonus cannot exceed 100%.',
            'level_up_reward.required' => 'Level up reward is required.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'VIP level name',
            'description' => 'VIP level description',
            'level_order' => 'level order',
            'discount' => 'discount percentage',
            'withdraw_fee' => 'withdraw fee',
            'min_withdraw_amount' => 'minimum withdraw amount',
            'contribute_amount_min' => 'minimum contribute amount',
            'contribute_amount_max' => 'maximum contribute amount',
            'contribute_amount_fixed' => 'fixed contribute amount',
            'profit_rate_bonus' => 'profit rate bonus',
            'level_up_reward' => 'level up reward',
        ];
    }
}
