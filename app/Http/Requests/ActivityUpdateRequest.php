<?php

namespace App\Http\Requests;

use App\Constants\Activities\ActivityTypes;
use App\Constants\WalletType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ActivityUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'amount' => 'required|integer|min:1',
            'wallet' => 'required|string|in:' . implode(',', WalletType::values()),
            'cycle' => 'required|string|max:255|in:daily,one_time',
            'type' => 'required|string|max:255|in:' . implode(',', ActivityTypes::values()),
            'start_date' => 'required|date_format:Y-m-d H:i:s',
            'end_date' => 'nullable|date_format:Y-m-d H:i:s',
            'status' => 'required|integer|in:0,1',
            'daily_reward' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'numeric'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:0.10'),
            ],
            'consecutive_day' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'integer'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:2'),
            ],
            'consecutive_reward' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'numeric'),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'min:0.01'),
            ],
            'min_vip_level' => [
                Rule::requiredIf(function () {
                    return $this->input('type') === ActivityTypes::SIGN_IN->value;
                }),
                Rule::when($this->input('type') === ActivityTypes::SIGN_IN->value, 'exists:vip_levels,id'),
            ],
        ];
    }
}
