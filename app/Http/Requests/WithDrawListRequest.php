<?php

namespace App\Http\Requests;

use App\Constants\WalletType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WithDrawListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'bank_mobile_number' => 'numeric',
            'bank_name' => 'nullable|string',
            'bank_account' => 'nullable|string',
            'amount' => 'nullable|numeric',
            'status' => 'nullable|in:pending,approved,rejected,failed,processing,pending_for_face_auth',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'pageSize' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'wallet_type' => [
                'nullable',
                Rule::in(WalletType::cases()),
            ],
        ];
    }
}
