<?php

namespace App\Http\Requests\VipLevelHistory;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ListVipLevelHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'nullable|integer|exists:users,id',
            'old_vip_level_id' => 'nullable|integer|exists:vip_levels,id',
            'new_vip_level_id' => 'nullable|integer|exists:vip_levels,id',
            'trigger_event' => 'nullable|string|in:deposit,withdrawal,manual,bonus',
            'action_type' => 'nullable|string|max:255',
            'user_name' => 'nullable|string|max:255',
            'email' => 'nullable|string|max:255',
            'old_vip_level_name' => 'nullable|string|max:255',
            'new_vip_level_name' => 'nullable|string|max:255',
            'date_from' => 'nullable|date|before_or_equal:date_to',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'pageSize' => 'nullable|integer|min:1|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.integer' => 'User ID must be a valid integer.',
            'user_id.exists' => 'The selected user does not exist.',
            'old_vip_level_id.integer' => 'Old VIP Level ID must be a valid integer.',
            'old_vip_level_id.exists' => 'The selected old VIP level does not exist.',
            'new_vip_level_id.integer' => 'New VIP Level ID must be a valid integer.',
            'new_vip_level_id.exists' => 'The selected new VIP level does not exist.',
            'trigger_event.string' => 'Trigger event must be a valid string.',
            'trigger_event.in' => 'Trigger event must be one of: deposit, withdrawal, manual, bonus.',
            'action_type.string' => 'Action type must be a valid string.',
            'action_type.max' => 'Action type cannot exceed 255 characters.',
            'user_name.string' => 'User name must be a valid string.',
            'user_name.max' => 'User name cannot exceed 255 characters.',
            'old_vip_level_name.string' => 'Old VIP level name must be a valid string.',
            'old_vip_level_name.max' => 'Old VIP level name cannot exceed 255 characters.',
            'new_vip_level_name.string' => 'New VIP level name must be a valid string.',
            'new_vip_level_name.max' => 'New VIP level name cannot exceed 255 characters.',
            'date_from.date' => 'Date from must be a valid date.',
            'date_from.before_or_equal' => 'Date from must be before or equal to date to.',
            'date_to.date' => 'Date to must be a valid date.',
            'date_to.after_or_equal' => 'Date to must be after or equal to date from.',
            'pageSize.integer' => 'Page size must be a valid integer.',
            'pageSize.min' => 'Page size must be at least 1.',
            'pageSize.max' => 'Page size cannot exceed 100.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'user ID',
            'old_vip_level_id' => 'old VIP level ID',
            'new_vip_level_id' => 'new VIP level ID',
            'trigger_event' => 'trigger event',
            'action_type' => 'action type',
            'user_name' => 'user name',
            'old_vip_level_name' => 'old VIP level name',
            'new_vip_level_name' => 'new VIP level name',
            'date_from' => 'start date',
            'date_to' => 'end date',
            'pageSize' => 'page size',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional custom validation if needed
            if ($this->date_from && $this->date_to) {
                $dateFrom = \Carbon\Carbon::parse($this->date_from);
                $dateTo = \Carbon\Carbon::parse($this->date_to);

                // Check if date range is not too large (e.g., max 1 year)
                if ($dateFrom->diffInDays($dateTo) > 365) {
                    $validator->errors()->add('date_to', 'Date range cannot exceed 365 days.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set default pageSize if not provided
        if (!$this->has('pageSize') || $this->pageSize === null) {
            $this->merge([
                'pageSize' => 10,
            ]);
        }

        // Clean up empty string values to null for optional fields
        $this->merge([
            'user_id' => $this->user_id === '' ? null : $this->user_id,
            'old_vip_level_id' => $this->old_vip_level_id === '' ? null : $this->old_vip_level_id,
            'new_vip_level_id' => $this->new_vip_level_id === '' ? null : $this->new_vip_level_id,
            'trigger_event' => $this->trigger_event === '' ? null : $this->trigger_event,
            'action_type' => $this->action_type === '' ? null : $this->action_type,
            'user_name' => $this->user_name === '' ? null : $this->user_name,
            'old_vip_level_name' => $this->old_vip_level_name === '' ? null : $this->old_vip_level_name,
            'new_vip_level_name' => $this->new_vip_level_name === '' ? null : $this->new_vip_level_name,
            'date_from' => $this->date_from === '' ? null : $this->date_from,
            'date_to' => $this->date_to === '' ? null : $this->date_to,
        ]);
    }
}
