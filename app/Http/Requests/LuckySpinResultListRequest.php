<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LuckySpinResultListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_phone' => [
                'nullable',
                'string',
            ],
            'verification_code' => [
                'nullable',
                'string',
            ],
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'pageSize' => 'nullable|integer|min:10|max:100',
            'reward_title' => 'nullable|string',
        ];
    }
}
