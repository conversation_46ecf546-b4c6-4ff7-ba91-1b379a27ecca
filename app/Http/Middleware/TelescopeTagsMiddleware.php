<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;

class TelescopeTagsMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        Telescope::tag(function (IncomingEntry $entry) use ($request) {
            if ($entry->type !== 'request') {
                return [];
            }

            $middlewares = $request->route()->computedMiddleware ?? [];

            return collect($middlewares)
                ->filter(fn ($middleware) => str_contains($middleware, 'tags:'))
                ->flatMap(function ($middleware) {
                    $tag = explode(':', $middleware)[1] ?? '';

                    return explode(',', $tag);
                })
                ->all();
        });

        return $next($request);
    }
}
