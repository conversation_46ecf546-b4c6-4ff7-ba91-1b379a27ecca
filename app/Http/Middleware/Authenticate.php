<?php

namespace App\Http\Middleware;

use App\Models\Admin as AdminModel;
use App\Models\LarkeAdminSession;
use Closure;
use Larke\Admin\Http\ResponseCode;
use Larke\Admin\Middleware\Authenticate as Base;

/**
 * jwt 验证
 *
 * @create 2020-10-19
 *
 * <AUTHOR>
 */
class Authenticate extends Base
{
    public function handle($request, Closure $next)
    {
        if (!$this->shouldPassThrough($request)) {
            if (($res = $this->jwtCheck()) !== null) {
                return $res;
            }
        }

        return $next($request);
    }

    /*
     * jwt验证
     */
    protected function jwtCheck()
    {
        $authorization = request()->header('Authorization');
        if (!$authorization) {
            return $this->error(__('larke-admin::auth.token_dont_empty'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        $authorizationArr = explode(' ', $authorization);
        if (count($authorizationArr) != 2) {
            return $this->error(__('larke-admin::auth.token_dont_empty'), ResponseCode::ACCESS_TOKEN_ERROR);
        }
        if ($authorizationArr[0] != 'Bearer') {
            return $this->error(__('larke-admin::auth.token_error'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        $accessToken = $authorizationArr[1];
        if (!$accessToken) {
            return $this->error(__('larke-admin::auth.token_dont_empty'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        if (count(explode('.', $accessToken)) != 3) {
            return $this->error(__('larke-admin::auth.token_error'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        if (app('larke-admin.cache')->has(md5($accessToken))) {
            return $this->error(__('larke-admin::auth.token_no_use'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        try {
            $decodeAccessToken = app('larke-admin.auth-token')
                ->decodeAccessToken($accessToken);
        } catch (\Exception $e) {
            return $this->error(__('larke-admin::auth.token_error'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        try {
            // 验证
            app('larke-admin.auth-token')->validate($decodeAccessToken);

            // 签名
            app('larke-admin.auth-token')->verify($decodeAccessToken);
        } catch (\Exception $e) {
            return $this->error(__('larke-admin::auth.token_timeout'), ResponseCode::ACCESS_TOKEN_TIMEOUT);
        }

        try {
            $adminid = $decodeAccessToken->getData('adminid');
            $sessionId = $decodeAccessToken->getData('session_id');
        } catch (\Exception $e) {
            return $this->error(__('larke-admin::auth.token_no_use'), ResponseCode::ACCESS_TOKEN_ERROR);
        }

        $adminInfo = AdminModel::where('id', $adminid)
            ->with(['groups'])
            ->first();
        if (empty($adminInfo)) {
            return $this->error(__('larke-admin::auth.passport_error'), ResponseCode::AUTH_ERROR);
        }
        $adminInfo = $adminInfo->toArray();
        $adminInfo['session_id'] = $sessionId;
        if (config('larkeadmin.two_fa.enabled')) {
            if ($adminInfo['two_fa_active'] != 1 && in_array(request()->path(), ['admin-api/admin/two-fa/qr', 'admin-api/admin/two-fa/verify'])) {
                app('larke-admin.auth-admin')
                    ->withAccessToken($accessToken)
                    ->withId($adminid)
                    ->withData($adminInfo);

                return null;
            }
            if (request()->path() == 'admin-api/admin/two-fa/verify') {
                app('larke-admin.auth-admin')
                    ->withAccessToken($accessToken)
                    ->withId($adminid)
                    ->withData($adminInfo);

                return null;
            }

            /** @var \App\Models\LarkeAdminSession $session */
            $session = LarkeAdminSession::where('session_id', $sessionId)
                ->where('admin_id', $adminid)
                ->first();
            if (empty($session)) {
                return $this->error(__('larke-admin::auth.passport_error'), ResponseCode::AUTH_ERROR);
            }
            if ($session->two_fa_verified != 1) {
                // return null;
                return $this->error(__('messages.two_fa.have_to_verify_2fa', locale: 'zh_CN'), 108);
            }
        }

        // 账号信息

        app('larke-admin.auth-admin')
            ->withAccessToken($accessToken)
            ->withId($adminid)
            ->withData($adminInfo);

        if (!app('larke-admin.auth-admin')->isActive()) {
            return $this->error(__('larke-admin::auth.passport_error'), ResponseCode::AUTH_ERROR);
        }

        if (!app('larke-admin.auth-admin')->isGroupActive()) {
            return $this->error(__('larke-admin::auth.group_error'), ResponseCode::AUTH_ERROR);
        }

        return null;
    }
}
