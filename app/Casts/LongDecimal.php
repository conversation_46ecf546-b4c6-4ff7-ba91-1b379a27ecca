<?php

namespace App\Casts;

use App\Utils\Math;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class LongDecimal implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param array<string, mixed> $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): string
    {
        $value = Math::formatNumber($value);

        return rtrim(rtrim($value, '0'), '.');
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): string
    {
        $value = Math::formatNumber($value);

        return rtrim(rtrim($value, '0'), '.');
    }
}
