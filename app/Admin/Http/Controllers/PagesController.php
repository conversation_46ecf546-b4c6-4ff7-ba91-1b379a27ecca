<?php

namespace App\Admin\Http\Controllers;

use App\Http\Requests\PageFileUploadRequest;
use App\Http\Requests\StorePageRequest;
use App\Http\Requests\UpdatePageRequest;
use App\Models\Page;
use App\Repositories\Contracts\PageRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Http\Controller as BaseController;

#[RouteRule(
    slug: '{prefix}pages',
    title: 'Pages Management',
    desc: 'Pages Controllers',
    order: 9910,
    auth: true
)]
class PagesController extends BaseController
{
    public function __construct(
        private PageRepositoryInterface $pageRepository
    ) {
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page List',
        desc: 'Page List',
        order: 9901,
        auth: true
    )]
    public function index(Request $request): Response
    {
        $locale = $request->query('locale');
        $list = $this->pageRepository->paginate($request->pageSize ?? 10, $locale);

        return $this->success(__('larke-admin::common.get_success'), $list);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page Show',
        desc: 'Page Show',
        order: 9902,
        auth: true
    )]
    public function show(Page $page): Response
    {
        $pageData = $page->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.get_success'), $pageData);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page Create',
        desc: 'Page Create',
        order: 9903,
        auth: true
    )]
    public function store(StorePageRequest $request): Response
    {
        $page = $this->pageRepository->create($request->validated());
        $pageData = $page->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.create_success'), $pageData);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page Update',
        desc: 'Page Update',
        order: 9904,
        auth: true
    )]
    public function update(Page $page, UpdatePageRequest $request): Response
    {
        $page = $this->pageRepository->update($page, $request->validated());
        $pageData = $page->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.update_success'), $pageData);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page Update',
        desc: 'Page Update',
        order: 9904,
        auth: true
    )]
    public function updateActive(Page $page, Request $request): Response
    {
        $validated = $request->validate([
            'active' => 'boolean',
        ]);

        $page = $this->pageRepository->updateActive($page, $validated['active']);

        return $this->success(__('larke-admin::common.update_success'), $page);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Upload Page Thumbnail',
        desc: 'Upload Page Thumbnail',
        order: 9905,
        auth: true
    )]
    public function uploadThumbnail(Page $page, PageFileUploadRequest $request): Response
    {
        $file = $request->file('thumbnail');
        $thumbnailPath = $this->pageRepository->uploadThumbnail($page, $file);

        return $this->success('Thumbnail uploaded successfully', [
            'thumbnail' => $thumbnailPath,
            'page' => $page->fresh(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Delete Page Thumbnail',
        desc: 'Delete Page Thumbnail',
        order: 9906,
        auth: true
    )]
    public function deleteThumbnail(Page $page): Response
    {
        $this->pageRepository->deleteThumbnail($page);

        return $this->success('Thumbnail deleted successfully', [
            'page' => $page->fresh(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Page Translations',
        desc: 'Get Page with All Translations',
        order: 9907,
        auth: true
    )]
    public function translations(Page $page): Response
    {
        $translations = $page->getAllTranslations();

        return $this->success(__('larke-admin::common.get_success'), [
            'page' => $page->toArray(),
            'translations' => $translations,
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Update Page Translation',
        desc: 'Update specific translation for a page',
        order: 9908,
        auth: true
    )]
    public function updateTranslation(Page $page, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,description,content',
            'locale' => 'required|string|max:10',
            'content' => 'required|string',
        ]);

        $page->setTranslation(
            $validated['field'],
            $validated['locale'],
            $validated['content']
        );

        return $this->success(__('larke-admin::common.update_success'), [
            'page' => $page->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Delete Page Translation',
        desc: 'Delete specific translation for a page',
        order: 9909,
        auth: true
    )]
    public function deleteTranslation(Page $page, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,description,content',
            'locale' => 'required|string|max:10',
        ]);

        $page->translations()
            ->where('field_key', $validated['field'])
            ->where('locale', $validated['locale'])
            ->delete();

        return $this->success(__('larke-admin::common.delete_success'), [
            'page' => $page->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.pages',
        title: 'Locale Configuration',
        desc: 'Get supported locales configuration',
        order: 9910,
        auth: true
    )]
    public function localeConfig(): Response
    {
        $config = config('locales');

        return $this->success(__('larke-admin::common.get_success'), $config);
    }
}
