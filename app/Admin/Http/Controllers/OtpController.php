<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\OtpService;
use Illuminate\Http\Request;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    title: 'OTP',
    desc: 'OTP',
    order: 155,
    auth: true,
    slug: '{prefix}otp'
)]
class OtpController extends Controller
{
    #[RouteRule(
        title: 'OTP',
        desc: 'OTP',
        order: 1,
        auth: true
    )]
    public function index(Request $request)
    {
        $service = app(OtpService::class);
        $list = $service->getList($request->all());

        return $this->success(__('messages.success'), $list);
    }
}
