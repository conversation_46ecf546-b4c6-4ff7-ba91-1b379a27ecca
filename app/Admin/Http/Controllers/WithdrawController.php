<?php

namespace App\Admin\Http\Controllers;

use App\Constants\NotificationType;
use App\Constants\PaymentServiceProvider;
use App\Constants\SenhuoWithdrawStatus;
use App\Constants\TransactionType;
use App\Constants\WalletType;
use App\Constants\WithdrawStatus;
use App\Http\Requests\UpdateWithdrawStatusRequest;
use App\Http\Requests\WithDrawListRequest;
use App\Http\Resources\WithdrawResource;
use App\Models\Notification;
use App\Models\PaymentChannel;
use App\Models\SenhuoWithdraw;
use App\Models\Withdraw;
use App\Models\WithdrawRequest;
use App\Services\Payment\PaymentService;
use App\Services\Senhuo\SenhuoService;
use App\Services\WalletService;
use App\Services\WithdrawService;
use App\Utils\Gmt8;
use App\Utils\Math;
use Faker\Provider\Payment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Http\Controller as BaseController;

#[RouteRule(
    slug: 'larke-admin.withdraw',
    title: 'Withdrawal Manager',
    desc: 'Withdrawal Manager',
    order: 9910,
    auth: true
)]
class WithdrawController extends BaseController
{
    private WithdrawService $service;

    private SenhuoService $senhuoService;

    private PaymentService $paymentService;

    public function __construct(
        WithdrawService $service,
        SenhuoService $senhuoService,
        PaymentService $paymentService,
    ) {
        $this->service = $service;
        $this->senhuoService = $senhuoService;
        $this->paymentService = $paymentService;
    }

    #[RouteRule(
        parent: 'larke-admin.withdraw',
        title: 'Withdraw List',
        desc: 'Withdraw List',
        order: 9901,
        auth: true
    )]
    public function index(WithDrawListRequest $request)
    {
        $filters = $request->validated();
        if (!empty($filters['start_date'])) {
            $filters['start_date'] = Gmt8::toUtc($filters['start_date']);
        }
        if (!empty($filters['end_date'])) {
            $filters['end_date'] = Gmt8::toUtc($filters['end_date'], true);
        }

        $list = $this->service->getList($filters);

        return $this->success(__('larke-admin::common.get_success'), WithdrawResource::make($list));
    }

    #[RouteRule(
        title: 'Update Withdraw Status',
        desc: 'Update Withdraw Status',
        order: 9921,
        auth: true
    )]
    public function update(Withdraw $withdraw, UpdateWithdrawStatusRequest $request)
    {
        return Cache::lock('withdraw-processing-' . $withdraw->id, 5)->get(function () use ($withdraw, $request) {
            $withdrawStatus = WithdrawStatus::tryFrom($request->get('status'));
            if ($withdraw->status !== WithdrawStatus::Pending->value) {
                return $this->error(__('messages.reload'), 5, ['reload' => true]);
            }

            DB::beginTransaction();
            try {
                $withdraw->notes = $request->notes;
                // process payment, should queue
                if ($withdrawStatus == WithdrawStatus::Approved) {
                    if ($request->payment_channel_id === 0) {
                        $withdraw->payment_channel_id = $request->payment_channel_id;
                        $withdraw->status = WithdrawStatus::Approved;
                    } else {
                        $withdraw->status = WithdrawStatus::Processing->value;
                        $withdraw->payment_channel_id = $request->payment_channel_id;

                        $usingPaymentService = config('services.payment_service.enabled');

                        /** @var PaymentChannel $paymentChannel */
                        $paymentChannel = PaymentChannel::withTrashed()->find($withdraw->payment_channel_id);
                        if ($paymentChannel->service_provider === PaymentServiceProvider::Senhuo->value) {
                            if (!SenhuoWithdraw::query()->where('withdraw_id', $withdraw->id)->exists()) {
                                $params = $this->senhuoService->prepareWithdraw($withdraw);
                                $this->saveWithdrawLog($withdraw, $params);
                            }
                        } elseif ($usingPaymentService && !WithdrawRequest::query()->where('withdraw_id', $withdraw->id)->exists()) {
                            $this->paymentService->prepareWithdrawRequest($withdraw);
                        }
                    }
                } elseif ($request->status == WithdrawStatus::Rejected->value) {
                    // Only process if the withdraw status is not already rejected
                    $walletType = WalletType::getType($withdraw->wallet->type);
                    $walletService = WalletService::make($withdraw->user, $walletType);

                    $oldBalance = $walletService->getBalance();
                    $walletService->fluctuateBalance($withdraw->amount);

                    // update old new balance of withdraw record
                    $withdraw->status = WithdrawStatus::Rejected->value;
                    $withdraw->old_balance = $oldBalance;
                    $withdraw->new_balance = $walletService->getBalance();
                    // may need that format on next step
                    //  $notes = ' - 提现 审核失败 原因：' . $request->notes;
                    // Save transaction logs
                    $walletService->saveTransactionLogs(
                        amount: $withdraw->amount,
                        balance: $walletService->getBalance(),
                        oldBalance: $oldBalance,
                        type: TransactionType::Withdraw_rollback,
                        remark: __('messages.rollback_withdraw', ['amount' => Math::formatNumber($withdraw->amount, true)]),
                        transactionCode: $withdraw->code . '-' . 'ROLLBACK',
                        reference: Withdraw::class,
                        referenceId: $withdraw->id,
                    );
                    if (!empty($request->notes)) {
                        $withdraw->remark;
                    }
                }
                $withdraw->save();
                if ($withdrawStatus == WithdrawStatus::Rejected) {
                    $this->addWithdrawNotification($withdraw, $withdrawStatus);
                }

                DB::commit();
            } catch (\Exception $exception) {
                DB::rollBack();
                Log::error($exception->getMessage());

                return $this->error(__('messages.please_refresh_page_and_try_again'));
            }

            return $this->success(__('messages.success'));
        });
    }

    private function saveWithdrawLog(Withdraw $withdraw, $params)
    {
        // Save the transaction to the withdraw log
        $withdrawLog = new SenhuoWithdraw();
        $withdrawLog->wallet_id = $withdraw->wallet_id;
        $withdrawLog->wallet_type = $withdraw->wallet->type;
        $withdrawLog->user_id = $withdraw->user_id;
        $withdrawLog->withdraw_id = $withdraw->id;
        $withdrawLog->status = SenhuoWithdrawStatus::Requesting->value;
        $withdrawLog->card_name = $params['cardName'];
        $withdrawLog->card_number = $params['cardNum'];
        $withdrawLog->open_bank = $withdraw->userBank->name;
        $withdrawLog->outOrderId = $params['outOrderId'];
        $withdrawLog->returnUrl = '';
        $withdrawLog->amount = $withdraw->amount;
        $withdrawLog->fee = $params['fee'] ?? 0;
        $withdrawLog->save();
    }

    public function addWithdrawNotification(Withdraw $withdraw, WithdrawStatus $withdrawStatus)
    {
        $withdraw->load(['wallet', 'user']);
        $message = __(
            'messages.withdrawal_result_notification',
            [
                'wallet' => __('messages.wallet_types.' . $withdraw->wallet->type, locale: 'zh_CN'),
                'amount' => Math::formatNumber($withdraw->amount, true),
                'status' => __('messages.withdraw_status.' . $withdrawStatus->name, locale: 'zh_CN'),
            ],
            'zh_CN'
        );

        Notification::create([
            'type' => NotificationType::Withdrawal->value,
            'user_id' => $withdraw->user_id,
            'title' => __('messages.withdrawal_request', locale: 'zh_CN'),
            'message' => $message,
        ]);
    }
}
