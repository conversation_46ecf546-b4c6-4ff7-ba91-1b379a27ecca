<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\GroupUpdateRequest;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}groups',
    title: 'Manage Reward',
    desc: 'Manage Reward',
    order: 115,
    auth: true
)]
class GroupController extends Controller
{
    private string $apiUrl;

    public function __construct()
    {
        $this->apiUrl = config('services.api_service.url') . '/settings/clear-cache';
    }

    #[RouteRule(
        parent: '{prefix}groups',
        title: 'List Groups with Withdrawal Settings',
        desc: 'List Groups with Withdrawal Settings',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $query = Group::with(['withdrawSettings'])->withCount(['users as members']);

        return $this->success(__('messages.success'), $query->paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}groups',
        title: 'Update Groups Settings',
        desc: 'Update Groups Settings',
        order: 9921,
        auth: true
    )]
    public function update(GroupUpdateRequest $request, Group $group)
    {
        if ($group->id !== 1) {
            $group->name = $request->name;
            $group->save();
        }
        $updateSetting = $request->validated();
        unset($updateSetting['name']);
        $group->withdrawSettings->update($updateSetting);
        Http::put($this->apiUrl, [
            'group_id' => $group->id,
        ]);

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}groups',
        title: 'Create New group',
        desc: 'Create New group',
        order: 9921,
        auth: true
    )]
    public function store(GroupUpdateRequest $request)
    {
        $group = Group::create([
            'name' => $request->name,
        ]);
        $updateSetting = $request->validated();
        unset($updateSetting['name']);
        $updateSetting['type'] = 'group';
        $group->withdrawSettings()->create($updateSetting);

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}groups',
        title: 'Delete group',
        desc: 'Delete group',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request, Group $group)
    {
        $request->validate([
            'new_group_id' => 'nullable|exists:groups,id',
        ]);
        if ($request->new_group_id) {
            $group->users()->update(['group_id' => $request->new_group_id]);
        } else {
            $group->users()->update(['group_id' => 1]);
        }
        Http::put($this->apiUrl, [
            'group_id' => $group->id,
        ]);
        $group->delete();

        return $this->success(__('messages.success'));
    }
}
