<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\BankAccountListRequest;
use App\Http\Requests\BindBankAccountRequest;
use App\Models\User;
use App\Models\UserBank;
use App\Services\FinanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}bank_account',
    title: 'Manage Bank Account',
    desc: 'Manage Bank Account',
    order: 115,
    auth: true
)]
class BankAccountController extends Controller
{
    public function __construct(protected FinanceService $service)
    {
        //
    }

    #[RouteRule(
        parent: '{prefix}bank_account',
        title: 'List Bank Account',
        desc: 'Get List Bank Account',
        order: 9921,
        auth: true
    )]
    public function getList(BankAccountListRequest $request)
    {
        $params = $request->validated();
        $query = UserBank::query();
        if (!empty($params['bank_name'])) {
            $query->where('name', $params['bank_name']);
        }

        if (!empty($params['bank_account'])) {
            $query->where('account', $params['bank_account']);
        }

        if (!empty($params['name'])) {
            $query->whereRelation('user', 'name', $params['name']);
        }

        if (!empty($params['phone'])) {
            $query->whereRelation('user', 'phone', $params['phone']);
        }
        $query->with('user');
        $query->orderBy('created_at', 'desc');

        $result = $query->paginate($request->get('pageSize', 20));

        return $this->success(__('messages.success'), $result);
    }

    public function bind(BindBankAccountRequest $request)
    {
        $params = $request->validated();

        //        if (!$this->checkBankInfoExternally($params['bank_account'])) {
        //            return $this->error(__('messages.bank_account_invalid'));
        //        }

        $user = User::query()->where('phone', $params['phone'])->first();
        if (!$user) {
            return $this->error(__('messages.user_not_found'));
        }
        $bank = UserBank::query()
            ->where('account', $params['bank_account'])
            ->where('name', $params['bank_name'])
            ->where('account_name', $params['bank_account_name'])
            ->where('active', 1)
            ->where('user_id', $user->id)
            ->first();
        if ($bank) {
            return $this->error(__('messages.this_bank_account_already_bound_to_this_user'));
        }
        // todo: allow admin to bind same bank account to other users -> popup to confirm

        DB::beginTransaction();
        try {
            $bank = UserBank::create([
                'user_id' => $user->id,
                'name' => $params['bank_name'],
                'account' => $params['bank_account'],
                'account_name' => $params['bank_account_name'],
                'mobile_number' => $params['phone'],
                'active' => true,
            ]);

            // deactivate Other Banks
            DB::table('user_banks')
                ->whereNot('id', $bank->id)
                ->where('user_id', $bank->user_id)
                ->where('active', true)
                ->update(['active' => false]);

            DB::commit();

            return $this->success(__('messages.success'));
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception->getMessage());

            return $this->error(__('messages.bind_bank_account_failed'));
        }
    }

    public function checkBankInfoExternally($cardNo)
    {
        // External API endpoint
        $url = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo={$cardNo}&cardBinCheck=true";

        try {
            // Use Laravel's HTTP client to make a GET request to the external API
            $response = Http::get($url);

            // Check if the response is successful
            if ($response->successful()) {
                $bankInfo = $response->json();

                // Check if the card is validated and the information is valid
                if (isset($bankInfo['validated']) && $bankInfo['validated']) {
                    return true;  // Return true if valid card info from external API
                }
            }
        } catch (\Exception $e) {
            // Log the error if there's an issue with the external API request
            Log::error('Bank Card API Error: ' . $e->getMessage());
        }

        return false;  // Return false if card info is not valid or API fails
    }

    public function checkBankBinded(BindBankAccountRequest $request)
    {
        $params = $request->validated();
        $user = User::query()->where('phone', $params['phone'])->first();
        if (!$user) {
            return $this->error(__('messages.user_not_found'));
        }
        $bank = UserBank::query()
            ->where('account', $params['bank_account'])
            ->where('name', $params['bank_name'])
            ->where('account_name', $params['bank_account_name'])
            ->where('active', 1)
            ->where('user_id', '!=', $user->id)
            ->first();
        if ($bank) {
            return $this->success(__('messages.success'), [
                'is_used_by_other' => true,
            ]);
        }

        return $this->success(__('messages.success'), [
            'is_used_by_other' => false,
        ]);
    }
}
