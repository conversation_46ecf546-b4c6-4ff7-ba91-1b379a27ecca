<?php

namespace App\Admin\Http\Controllers;

use App\Constants\UserBankType;
use App\Http\Controllers\Controller;
use App\Http\Requests\PaymentChannelListRequest;
use App\Http\Requests\PaymentChannelRequest;
use App\Http\Requests\PaymentChannelStatisticRequest;
use App\Models\PaymentChannel;
use App\Models\User;
use App\Models\UserBank;
use App\Utils\Gmt8;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}payment_channels',
    title: 'Manage Payment Channel',
    desc: 'Manage Payment Channel',
    order: 115,
    auth: true
)]
class PaymentChannelController extends Controller
{
    #[RouteRule(
        parent: '{prefix}payment_channels',
        title: 'List Payment Channel',
        desc: 'Get List Payment Channel',
        order: 9921,
        auth: true
    )]
    public function index(PaymentChannelListRequest $request)
    {
        $params = $request->validated();
        $query = PaymentChannel::query();
        if ($request->bank_id) {
            $bank = UserBank::find($request->bank_id);
            if ($bank->type = UserBankType::Crypto->value) {
                $supportProviders = Http::get(config('services.api_service.url') . '/payment/providers')->json();
                if ($supportProviders['status'] == true) {
                    $network = $bank->name;
                    $currency = $bank->account_name;
                    $supportProviders = collect($supportProviders['data']);
                    $providerNames = $supportProviders->map(function ($item) use ($currency, $network) {
                        if (in_array($currency, $item['supported_currencies']) && in_array($network, $item['supported_networks'][$currency])) {
                            return $item['name'];
                        }
                    });
                    $query->whereIn('service_provider', $providerNames->toArray());
                }
            }
        }
        if (!empty($params['for_withdraw'])) {
            $query->where('for_withdraw', $params['for_withdraw']);
        }
        if (!empty($params['for_deposit'])) {
            $query->where('for_deposit', $params['for_deposit']);
        }
        if (!empty($params['active'])) {
            $query->where('active', $params['active']);
        }
        if (!empty($params['available_merchant_name'])) {
            $query->whereNotNull('merchant_name');
        }
        if (!empty($params['available_merchant_private_key'])) {
            $query->whereNotNull('merchant_private_key');
        }
        if (!empty($params['user_id'])) {
            $user = User::find($params['user_id']);
            if ($user) {
                $vipLevel = $user->getVipLevelAttribute();
                $query->where(function ($query) use ($vipLevel) {
                    $query->where('min_user_vip_level', '<=', $vipLevel->level_order)
                        ->orWhereNull('min_user_vip_level');
                });
            }
        }
        if ($request->pagination == 'false') {
            $result = $query->get();
        } else {
            $result = $query->paginate($request->get('pageSize', 10));
        }

        return $this->success(__('messages.success'), $result);
    }

    #[RouteRule(
        parent: '{prefix}payment_channels',
        title: 'Update Payment Channel',
        desc: 'Update Payment Channel Info',
        order: 9921,
        auth: true
    )]
    public function update(PaymentChannel $paymentChannel, PaymentChannelRequest $request)
    {
        $updateData = $request->validated();
        if ($request->has('logo') && !is_string($request->logo)) {
            $updateData['logo'] = Storage::disk('public')
                ->putFile('images', $request->file('logo'));
            $updateData['logo'] = Storage::disk('public')->url($updateData['logo']);
        }
        $updateData['service_provider'] = $request->provider;
        if (!empty($updateData['min_user_vip_level']) && $updateData['min_user_vip_level'] == -1) {
            $updateData['min_user_vip_level'] = null;
        }
        $paymentChannel->update($updateData);

        return $this->success(__('messages.success'), $paymentChannel->refresh());
    }

    #[RouteRule(
        parent: '{prefix}payment_channels',
        title: 'Update Payment Channel',
        desc: 'Update Payment Channel Info',
        order: 9921,
        auth: true
    )]
    public function store(PaymentChannelRequest $request)
    {
        $createData = $request->validated();
        if ($request->has('logo')) {
            $createData['logo'] = Storage::disk('public')
                ->putFile('images', $request->file('logo'));
            $createData['logo'] = Storage::disk('public')->url($createData['logo']);
        }
        $createData['service_provider'] = $request->provider;
        if (!empty($createData['min_user_vip_level']) && $createData['min_user_vip_level'] == -1) {
            $createData['min_user_vip_level'] = null;
        }

        return $this->success(__('messages.success'), PaymentChannel::create($createData));
    }

    #[RouteRule(
        parent: '{prefix}payment_channels',
        title: 'Delete Payment Channel',
        desc: 'Delete Payment Channel',
        order: 9921,
        auth: true
    )]
    public function destroy(PaymentChannel $paymentChannel, Request $request)
    {
        if ($paymentChannel->delete()) {
            return $this->success(__('larke-admin::common.delete_success'));
        }

        return $this->success(__('larke-admin::common.delete_fail'));
    }

    #[RouteRule(
        parent: '{prefix}payment_channels',
        title: 'Payment Channel report',
        desc: 'Payment Channel report',
        order: 9921,
        auth: true
    )]
    public function paymentChannelStatistic(PaymentChannelStatisticRequest $request)
    {
        $today = Gmt8::today();
        $yesterday = Gmt8::yestedday();
        $todayTimeRange = Gmt8::toUtcTodayRange($today);
        $yesterdayTimeRange = Gmt8::toUtcTodayRange($yesterday);

        // Query for deposits
        $depositsQuery = DB::table('view_deposit_requests')
            ->select(
                'payment_channels.id as channel_id',
                'payment_channels.name as channel_name',
                'payment_channels.service_provider as service_provider',
                DB::raw('SUM(CASE WHEN view_deposit_requests.created_at BETWEEN ? AND ? THEN view_deposit_requests.amount ELSE 0 END) as today_deposit'),
                DB::raw('SUM(CASE WHEN view_deposit_requests.created_at BETWEEN ? AND ? THEN view_deposit_requests.amount ELSE 0 END) as yesterday_deposit'),
                DB::raw('SUM(view_deposit_requests.amount) as total_deposit')
            )
            ->leftJoin('payment_channels', 'view_deposit_requests.payment_channel_id', '=', 'payment_channels.id')
            ->where('view_deposit_requests.status', 'success')
            ->groupBy('payment_channels.id', 'payment_channels.name')
            ->addBinding([$todayTimeRange[0], $todayTimeRange[1], $yesterdayTimeRange[0], $yesterdayTimeRange[1]], 'select')
            ->get();

        return $this->success(__('larke-admin::common.get_success'), $depositsQuery);
    }

    /**
     * @throws ConnectionException
     */
    #[RouteRule(
        parent: '{prefix}payment_providers',
        title: 'Payment Service Providers',
        desc: 'Payment Service Providers',
        order: 9922,
        auth: true
    )]
    public function getProviders()
    {
        $result = Http::get(config('services.api_service.url') . '/payment/providers')->json();
        if (!$result || !$result['status']) {
            return $this->error(__('messages.integration_service_error'));
        }
        $newProviders = $result['data'];
        foreach ($newProviders as $provider) {
            $providers[$provider['name']] = [
                'code' => $provider['name'],
                'name' => Str::upper($provider['name']),
                'active' => 1,
                'deposit' => 1,
                'withdraw' => 1,
            ];
        }

        return $this->success(__('messages.success'), array_values($providers));
    }
}
