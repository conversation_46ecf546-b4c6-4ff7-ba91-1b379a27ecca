<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateRewardSettingRequest;
use App\Models\RewardMilestone;
use Illuminate\Http\Request;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}settings.rewards',
    title: 'Manage Reward',
    desc: 'Manage Reward',
    order: 115,
    auth: true
)]
class RewardController extends Controller
{
    #[RouteRule(
        parent: '{prefix}settings.rewards',
        title: 'List  Reward Settings',
        desc: 'List  Reward Settings',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        return $this->success(__('messages.success'), RewardMilestone::paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}settings.rewards',
        title: 'Create Rewards Settings',
        desc: 'Create New Rewards Settings',
        order: 9921,
        auth: true
    )]
    public function store(UpdateRewardSettingRequest $request)
    {
        RewardMilestone::create($request->validated());

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}settings.rewards',
        title: 'Update Rewards Settings',
        desc: 'Update Rewards Settings',
        order: 9921,
        auth: true
    )]
    public function update(UpdateRewardSettingRequest $request)
    {
        $rewardMilestone = RewardMilestone::findOrFail($request->id);
        $rewardMilestone->update($request->validated());

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}settings.rewards',
        title: 'Delete Rewards Settings',
        desc: 'Delete Rewards Settings',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }
}
