<?php

namespace App\Admin\Http\Controllers;

use App\Http\Requests\StoreNewsRequest;
use App\Http\Requests\UpdateNewsRequest;
use App\Models\News;
use App\Repositories\NewsRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Http\Controller as BaseController;

#[RouteRule(
    slug: 'larke-admin.news',
    title: 'News Management',
    desc: 'News Controllers',
    order: 9910,
    auth: true
)]
class NewsController extends BaseController
{
    public function __construct(
        private NewsRepository $newsRepository
    ) {
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News List',
        desc: 'News List',
        order: 9901,
        auth: true
    )]
    public function index(Request $request): Response
    {
        $locale = $request->query('locale');
        $list = $this->newsRepository->paginate($request->pageSize ?? 10, $locale);

        return $this->success(__('larke-admin::common.get_success'), $list);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News Show',
        desc: 'News Show',
        order: 9902,
        auth: true
    )]
    public function show(News $news): Response
    {
        $newsData = $news->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.get_success'), $newsData);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News Create',
        desc: 'News Create',
        order: 9903,
        auth: true
    )]
    public function store(StoreNewsRequest $request): Response
    {
        $news = $this->newsRepository->create($request->validated());
        $newsData = $news->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.create_success'), $newsData);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News Update',
        desc: 'News Update',
        order: 9904,
        auth: true
    )]
    public function update(News $news, UpdateNewsRequest $request): Response
    {
        $news = $this->newsRepository->update($news, $request->validated());
        $newsData = $news->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.update_success'), $newsData);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News Update Active',
        desc: 'News Update Active',
        order: 9905,
        auth: true
    )]
    public function updateActive(News $news, Request $request): Response
    {
        $validated = $request->validate([
            'active' => 'boolean',
        ]);

        $news = $this->newsRepository->updateActive($news, $validated['active']);

        return $this->success(__('larke-admin::common.update_success'), $news);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'Upload News Thumbnail',
        desc: 'Upload News Thumbnail',
        order: 9905,
        auth: true
    )]
    public function uploadThumbnail(News $news, Request $request): Response
    {
        $request->validate([
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $file = $request->file('thumbnail');
        $thumbnailPath = $this->newsRepository->uploadThumbnail($news, $file);

        return $this->success('Thumbnail uploaded successfully', [
            'thumbnail' => $thumbnailPath,
            'news' => $news->fresh(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'Delete News Thumbnail',
        desc: 'Delete News Thumbnail',
        order: 9906,
        auth: true
    )]
    public function deleteThumbnail(News $news): Response
    {
        $this->newsRepository->deleteThumbnail($news);

        return $this->success('Thumbnail deleted successfully', [
            'news' => $news->fresh(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'News Translations',
        desc: 'Get News with All Translations',
        order: 9907,
        auth: true
    )]
    public function translations(News $news): Response
    {
        $translations = $news->getAllTranslations();

        return $this->success(__('larke-admin::common.get_success'), [
            'news' => $news->toArray(),
            'translations' => $translations,
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'Update News Translation',
        desc: 'Update specific translation for a news',
        order: 9908,
        auth: true
    )]
    public function updateTranslation(News $news, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,description,content',
            'locale' => 'required|string|max:10',
            'content' => 'required|string',
        ]);

        $news->setTranslation(
            $validated['field'],
            $validated['locale'],
            $validated['content']
        );

        return $this->success(__('larke-admin::common.update_success'), [
            'news' => $news->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'Delete News Translation',
        desc: 'Delete specific translation for a news',
        order: 9909,
        auth: true
    )]
    public function deleteTranslation(News $news, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,description,content',
            'locale' => 'required|string|max:10',
        ]);

        $news->translations()
            ->where('field_key', $validated['field'])
            ->where('locale', $validated['locale'])
            ->delete();

        return $this->success(__('larke-admin::common.delete_success'), [
            'news' => $news->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: 'larke-admin.news',
        title: 'Locale Configuration',
        desc: 'Get supported locales configuration',
        order: 9910,
        auth: true
    )]
    public function localeConfig(): Response
    {
        $config = config('locales');

        return $this->success(__('larke-admin::common.get_success'), $config);
    }
}
