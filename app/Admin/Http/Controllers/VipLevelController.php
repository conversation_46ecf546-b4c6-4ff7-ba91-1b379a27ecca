<?php

namespace App\Admin\Http\Controllers;

use App\Http\Requests\VipLevel\StoreVipLevelRequest;
use App\Http\Requests\VipLevel\UpdateVipLevelRequest;
use App\Models\VipLevel;
use App\Repositories\VipLevelRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Http\Controller as BaseController;

#[RouteRule(
    slug: '{prefix}vip-levels',
    title: 'Manage VIP Levels',
    desc: 'Manage VIP Levels',
    order: 120,
    auth: true
)]
class VipLevelController extends BaseController
{
    protected VipLevelRepository $repository;

    public function __construct(VipLevelRepository $repository)
    {
        $this->repository = $repository;
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'List VIP Levels',
        desc: 'Get List VIP Levels',
        order: 9921,
        auth: true
    )]
    public function index(Request $request): Response
    {
        $locale = $request->query('locale');
        $list = $this->repository->paginate($request->pageSize ?? 10, $locale);
        if ($request->has('fullList')) {
            return $this->success(__('messages.success'), $this->repository->getAll());
        }

        return $this->success(__('larke-admin::common.get_success'), $list);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'VIP Level detail',
        desc: 'GET VIP Level Detail',
        order: 9922,
        auth: true
    )]
    public function show(VipLevel $vipLevel): Response
    {
        $vipLevelData = $vipLevel->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.get_success'), $vipLevelData);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Create VIP Level',
        desc: 'Create New VIP Level',
        order: 9923,
        auth: true
    )]
    public function store(StoreVipLevelRequest $request): Response
    {
        $validated = $request->validated();
        $withdrawSettings = [
            'withdrawal_week_days' => $validated['withdrawal_week_days'] ?? null,
            'withdrawal_customize_amount' => $validated['withdrawal_customize_amount'] ?? null,
        ];
        $vipLevel = $this->repository->create($validated);

        // Create withdrawal settings if provided
        if (!empty(array_filter($withdrawSettings))) {
            $vipLevel->withdrawSettings()->create($withdrawSettings);
        }

        $vipLevelData = $vipLevel->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.create_success'), $vipLevelData);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Update VIP Level',
        desc: 'Update VIP Level Info',
        order: 9924,
        auth: true
    )]
    public function update(VipLevel $vipLevel, UpdateVipLevelRequest $request): Response
    {
        $validated = $request->validated();
        $vipLevel = $this->repository->update($vipLevel, $validated);
        $vipLevelData = $vipLevel->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.update_success'), $vipLevelData);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Delete VIP Level',
        desc: 'Delete VIP Level',
        order: 9925,
        auth: true
    )]
    public function destroy(Request $request, VipLevel $vipLevel)
    {
        try {
            // Check if VIP level is being used by any users
            // You might want to add this check based on your business logic
            // if ($vipLevel->users()->exists()) {
            //     return $this->error(__('Cannot delete VIP level that is assigned to users.'), 422);
            // }

            if ($this->repository->delete($vipLevel)) {
                return $this->success(__('messages.success'));
            }

            return $this->error(__('messages.failed'));
        } catch (\Exception $e) {
            return $this->error(__('messages.failed'), 500);
        }
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Update VIP Level Withdrawal Settings',
        desc: 'Update Withdrawal Settings for VIP Level',
        order: 9925,
        auth: true
    )]
    public function updateWithdrawalSetting(Request $request, VipLevel $vipLevel)
    {
        $validated = $request->validate([
            'withdrawal_week_days' => 'nullable|array',
            'withdrawal_week_days.*' => 'nullable|integer|between:0,6',
            'withdrawal_customize_amount' => 'nullable|array',
            'withdrawal_customize_amount.*' => 'numeric',
        ]);

        $vipLevel->withdrawSettings->update($validated);
        $apiUrl = config('services.api_service.url') . '/settings/clear-cache';
        Http::put($apiUrl, [
            'vip_id' => $vipLevel->id,
        ]);

        return $this->success(__('larke-admin::common.update_success'));
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'VIP Level Translations',
        desc: 'Get VIP Level with All Translations',
        order: 9926,
        auth: true
    )]
    public function translations(VipLevel $vipLevel): Response
    {
        $translations = $vipLevel->getAllTranslations();

        return $this->success(__('larke-admin::common.get_success'), [
            'vip_level' => $vipLevel->toArray(),
            'translations' => $translations,
        ]);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Update VIP Level Translation',
        desc: 'Update specific translation for a VIP level',
        order: 9927,
        auth: true
    )]
    public function updateTranslation(VipLevel $vipLevel, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:name,description',
            'locale' => 'required|string|max:10',
            'content' => 'required|string',
        ]);

        $vipLevel->setTranslation(
            $validated['field'],
            $validated['locale'],
            $validated['content']
        );

        return $this->success(__('larke-admin::common.update_success'), [
            'vip_level' => $vipLevel->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Delete VIP Level Translation',
        desc: 'Delete specific translation for a VIP level',
        order: 9928,
        auth: true
    )]
    public function deleteTranslation(VipLevel $vipLevel, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:name,description',
            'locale' => 'required|string|max:10',
        ]);

        $vipLevel->translations()
            ->where('field_key', $validated['field'])
            ->where('locale', $validated['locale'])
            ->delete();

        return $this->success(__('larke-admin::common.delete_success'), [
            'vip_level' => $vipLevel->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}vip-levels',
        title: 'Locale Configuration',
        desc: 'Get supported locales configuration',
        order: 9929,
        auth: true
    )]
    public function localeConfig(): Response
    {
        $config = config('locales');

        return $this->success(__('larke-admin::common.get_success'), $config);
    }
}
