<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\DepositListRequest;
use App\Models\DepositRequests;
use App\Models\PaymentChannel;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}deposits',
    title: 'Manage Deposit',
    desc: 'Manage Deposit',
    order: 115,
    auth: true
)]
class DepositRequestController extends Controller
{
    #[RouteRule(
        parent: '{prefix}deposits_request',
        title: 'List Deposit request',
        desc: 'Get List Deposit request',
        order: 9921,
        auth: true
    )]
    public function index(DepositListRequest $request)
    {
        $depositQuery = DepositRequests::with(['wallet', 'user', 'paymentChannel' => function ($query) {
            $query->withTrashed();
        }]);
        if (!empty($request->start_date) && !empty($request->end_date)) {
            $depositQuery->whereBetween('created_at', [
                Gmt8::toUtc($request->start_date),
                Gmt8::toUtc($request->end_date, true),
            ]);
        }
        if (!empty($request->phone)) {
            $depositQuery->whereRelation('user', 'phone', 'like', '%' . $request->phone . '%');
        }
        if (!empty($request->name)) {
            $depositQuery->whereRelation('user', 'name', 'like', '%' . $request->name . '%');
        }

        if (!empty($request->outOrderId)) {
            $depositQuery->where('outOrderId', $request->outOrderId);
        }
        if (!empty($request->status)) {
            $depositQuery->where('status', $request->status);
        }
        $depositQuery->orderBy('created_at', 'desc');

        return $this->success(__('messages.success'), $depositQuery->paginate($request->get('pageSize', 10)));
    }

    public function checkStatus(Request $request)
    {
        /** @var PaymentChannel $paymentChannel */
        $paymentChannel = PaymentChannel::withTrashed()->findOrFail($request->paymentChannelId);
        $depositRequest = DepositRequests::query()->where('outOrderId', $request->outOrderId)->firstOrFail();

        // cache the request with that id and return the response
        $cacheKey = 'deposit_request_status_' . $depositRequest->outOrderId;
        if (cache()->has($cacheKey)) {
            return $this->success(__('messages.success'));
        }
        cache()->put($cacheKey, $request->all(), now()->addMinutes(5));

        $statusServiceUrl = config('services.api.deposit_status_url');
        $params = [
            'outOrderId' => $depositRequest->outOrderId,
            'paymentChannelId' => $paymentChannel->id,
        ];

        // send post status request to $statusServiceUrl
        $client = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->withOptions([
            'verify' => false, // Disable SSL verification
        ]);
        $response = $client->post($statusServiceUrl, $params);
        if ($response->failed()) {
            return $this->error(__('messages.failed_to_verify'));
        }

        return $this->success(__('messages.success'));
    }
}
