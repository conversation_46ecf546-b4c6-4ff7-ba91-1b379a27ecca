<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Coupon\CouponListRequest;
use App\Http\Requests\Coupon\StoreCouponRequest;
use App\Http\Requests\Coupon\UpdateCouponRequest;
use App\Models\Coupon;
use App\Utils\Gmt8;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}coupon',
    title: 'Manage Coupon',
    desc: 'Manage Coupon',
    order: 115,
    auth: true
)]
class CouponController extends Controller
{
    #[RouteRule(
        parent: '{prefix}coupon',
        title: 'List Coupon',
        desc: 'Get List Coupon',
        order: 9931,
        auth: true
    )]
    public function index(CouponListRequest $request)
    {
        $params = $request->validated();
        $query = Coupon::query();
        if (isset($params['active'])) {
            $query->where('active', $request->active);
        }

        if (isset($params['include_expired']) && $params['include_expired'] == 0) {// dont show expired coupon
            $query->where('expired_at', '>', now());
        }

        if ($request->input('disable_pagination')) {
            $coupons = $query->get();

            return $this->success(__('messages.success'), ['data' => $coupons]);
        }

        $coupons = $query->paginate($request->input('per_page', 10));

        return $this->success(__('messages.success'), $coupons);
    }

    #[RouteRule(
        parent: '{prefix}coupon',
        title: 'Update Coupon',
        desc: 'Update Coupon Info',
        order: 9921,
        auth: true
    )]
    public function update(Coupon $coupon, UpdateCouponRequest $request)
    {
        $updateData = $request->validated();
        $updateData['available_at'] = Gmt8::toUtc($updateData['available_at'] . ' 00:00:00');
        if (!empty($updateData['expired_at'])) {
            $updateData['expired_at'] = Gmt8::toUtc($updateData['expired_at'] . ' 23:59:59');
        }
        $coupon->update($updateData);

        return $this->success(__('messages.success'), $coupon->refresh());
    }

    #[RouteRule(
        parent: '{prefix}coupon',
        title: 'Update Coupon',
        desc: 'Update Coupon Info',
        order: 9921,
        auth: true
    )]
    public function store(StoreCouponRequest $request)
    {
        $createData = $request->validated();
        $createData['code'] = strtoupper($createData['code']);
        $createData['available_at'] = Gmt8::toUtc($createData['available_at'] . ' 00:00:00');
        if (!empty($updateData['expired_at'])) {
            $createData['expired_at'] = Gmt8::toUtc($updateData['expired_at'] . ' 23:59:59');
        }

        return $this->success(__('messages.success'), Coupon::create($createData));
    }
}
