<?php

namespace App\Admin\Http\Controllers;

use App\Constants\PaymentType;
use App\Constants\ProductCategory;
use App\Constants\ReturnType;
use App\Entities\Product\InvestmentProductMetadataEntity;
use App\Http\Controllers\Controller;
use App\Http\Requests\FilterProductRequest;
use App\Http\Requests\Product\CreateProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}products',
    title: 'Manage Product',
    desc: 'Manage Product',
    order: 115,
    auth: true
)]
class ProductController extends Controller
{
    #[RouteRule(
        parent: '{prefix}products',
        title: 'List Product',
        desc: 'Get List Product',
        order: 9921,
        auth: true
    )]
    public function index(FilterProductRequest $request)
    {
        $validated = $request->validated();
        $query = Product::query();

        if (!empty($validated['name'])) {
            $query->where('name', 'like', '%' . $validated['name'] . '%');
        }
        if (!empty($validated['active'])) {
            $query->where('active', $validated['active']);
        }

        if (!empty($validated['sortBy']) && !empty($validated['sort'])) {
            $query->orderBy($validated['sortBy'], $validated['sort']);
        } else {
            $query->orderBy('sort');
        }
        $products = $query->paginate($request->get('pageSize', 10));

        return $this->success(
            __('messages.success'),
            $products
        );
    }

    #[RouteRule(
        parent: '{prefix}products',
        title: 'Create Product',
        desc: 'Create New Product',
        order: 9921,
        auth: true
    )]
    public function store(CreateProductRequest $request)
    {
        $validated = $request->validated();
        // validate metadata based on category
        if ($validated['category_id'] == ProductCategory::Investment->value) {
            $validator = Validator::make($validated, [
                'metadata' => 'array|required',
                'metadata.fund_company_name' => 'required|string',
                'metadata.invest_period' => 'required|integer',
                'metadata.return_type' => 'required|numeric|in:' . implode(',', ReturnType::values()),
                'metadata.minimum_vip_level' => 'required|integer',
                'metadata.min_invest_amount' => 'required|numeric',
                'metadata.max_invest_amount' => 'required|numeric',
                'metadata.limit_purchase_items' => 'required|integer',
                'metadata.total_stocks' => 'required|integer',
                'metadata.start_date_time' => 'required|date_format:Y-m-d H:i:s',
                'metadata.end_date_time' => 'required|date_format:Y-m-d H:i:s',
                'metadata.auto_extend' => 'required|boolean',
            ]);
            if ($validator->fails()) {
                return $this->error('validation error', 422, $validator->errors());
            }

            $validated['metadata'] = InvestmentProductMetadataEntity::make(
                fund_company_name: $validated['metadata']['fund_company_name'],
                invest_period: $validated['metadata']['invest_period'],
                interest_return_type: $validated['metadata']['return_type'],
                minimum_vip_level: $validated['metadata']['minimum_vip_level'],
                min_invest_amount: $validated['metadata']['min_invest_amount'],
                max_invest_amount: $validated['metadata']['max_invest_amount'],
                limit_purchase_items: $validated['metadata']['limit_purchase_items'],
                total_stocks: $validated['metadata']['total_stocks'],
                start_date_time: $validated['metadata']['start_date_time'],
                end_date_time: $validated['metadata']['end_date_time'],
                auto_extend: $validated['metadata']['auto_extend'],
                remaining_stocks: $validated['metadata']['remaining_stocks'] ?? 0,
                payment_type: $validated['metadata']['payment_type'] ?? PaymentType::Wallet->value,
            )->toArray();
        } else {
            if (isset($validated['metadata'])) {
                foreach ($validated['metadata'] as $key => $value) {
                    if (in_array($key, ['profit_rate', 'share_quantity', 'available_loan'])) {
                        $validated['metadata'][$key] = (int) $value;
                    }
                }
            }
        }

        if ($request->has('image')) {
            $validated['image'] = Storage::disk('public')
                ->putFile('images', $request->file('image'));
            $validated['image'] = Storage::disk('public')->url($validated['image']);
        }

        // Handle sorting logic
        $maxSort = Product::max('sort');
        $validated['sort'] = $maxSort + 1;
        if ($product = Product::create($validated)) {
            return $this->success(__('messages.success'), $product);
        }

        return $this->error(__('messages.error'));
    }

    #[RouteRule(
        parent: '{prefix}products',
        title: 'Update Product',
        desc: 'Update Product Info',
        order: 9921,
        auth: true
    )]
    public function update(UpdateProductRequest $request, Product $product)
    {
        $validated = $request->validated();
        if ($validated['category_id'] == ProductCategory::Investment->value) {
            $validator = Validator::make($validated, [
                'metadata' => 'array|required',
                'metadata.fund_company_name' => 'required|string',
                'metadata.invest_period' => 'required|integer',
                'metadata.return_type' => 'required|numeric|in:' . implode(',', ReturnType::values()),
                'metadata.minimum_vip_level' => 'required|integer',
                'metadata.min_invest_amount' => 'required|numeric',
                'metadata.max_invest_amount' => 'required|numeric',
                'metadata.limit_purchase_items' => 'required|integer',
                'metadata.total_stocks' => 'required|integer',
                'metadata.start_date_time' => 'required|date_format:Y-m-d H:i:s',
                'metadata.end_date_time' => 'required|date_format:Y-m-d H:i:s',
                'metadata.auto_extend' => 'required|boolean',
                'metadata.remaining_stocks' => 'required|numeric|min:0|max:100',
                'metadata.payment_type' => 'required|string|in:' . implode(',', PaymentType::values()),
            ]);
            if ($validator->fails()) {
                return $this->error('validation error', 422, $validator->errors());
            }

            $validated['metadata'] = InvestmentProductMetadataEntity::make(
                fund_company_name: $validated['metadata']['fund_company_name'],
                invest_period: $validated['metadata']['invest_period'],
                interest_return_type: $validated['metadata']['return_type'],
                minimum_vip_level: $validated['metadata']['minimum_vip_level'],
                min_invest_amount: $validated['metadata']['min_invest_amount'],
                max_invest_amount: $validated['metadata']['max_invest_amount'],
                limit_purchase_items: $validated['metadata']['limit_purchase_items'],
                total_stocks: $validated['metadata']['total_stocks'],
                start_date_time: $validated['metadata']['start_date_time'],
                end_date_time: $validated['metadata']['end_date_time'],
                auto_extend: $validated['metadata']['auto_extend'],
                remaining_stocks: $validated['metadata']['remaining_stocks'],
                payment_type: $validated['metadata']['payment_type'],
            )->toArray();
        } else {
            if (isset($validated['metadata'])) {
                foreach ($validated['metadata'] as $key => $value) {
                    if (in_array($key, ['profit_rate', 'share_quantity', 'available_loan'])) {
                        $validated['metadata'][$key] = (int) $value;
                    }
                }
            }
        }

        if ($request->has('image')) {
            $validated['image'] = Storage::disk('public')
                ->putFile('images', $request->file('image'));
            $validated['image'] = Storage::disk('public')->url($validated['image']);
        }
        if (isset($validated['metadata'])) {
            foreach ($validated['metadata'] as $key => $value) {
                if (in_array($key, ['profit_rate', 'share_quantity', 'available_loan'])) {
                    $validated['metadata'][$key] = (int) $value;
                }
            }
        }
        // Handle sorting logic
        $maxSort = Product::max('sort');
        if (!empty($validated['sort'])) {
            $existingProduct = Product::where('sort', $validated['sort'])
                ->where('id', '!=', $product->id)
                ->first();
            if ($existingProduct) {
                $currentSort = $existingProduct->sort;
                $existingProduct->update(['sort' => $product->sort]);
                $validated['sort'] = $currentSort;
            }
        }
        if (isset($validated['sort']) && $validated['sort'] == 0) {
            $validated['sort'] = $maxSort + 1;
        }

        if ($product->update($validated)) {
            return $this->success(__('messages.success'));
        }

        return $this->error(__('messages.error'));
    }

    #[RouteRule(
        parent: '{prefix}products',
        title: 'Delete Product',
        desc: 'Delete Product',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }

    public function sort($id, $request)
    {
        $validated = $request->validate([
            'sort' => 'required|integer',
        ]);
        $product = Product::find($id);
        if (!empty($validated['sort'])) {
            $existingProduct = Product::where('sort', $validated['sort'])
                ->where('id', '!=', $product->id)
                ->first();
            if ($existingProduct) {
                $currentSort = $existingProduct->sort;
                $existingProduct->update(['sort' => $product->sort]);
                $validated['sort'] = $currentSort;
            }
        }

        if ($product->update($validated)) {
            return $this->success(__('messages.success'));
        }

        return $this->error(__('messages.error'));
    }
}
