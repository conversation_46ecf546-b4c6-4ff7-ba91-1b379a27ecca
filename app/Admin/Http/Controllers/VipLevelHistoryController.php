<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\VipLevelHistory\ListVipLevelHistoryRequest;
use App\Models\VipLevelHistory;
use App\Repositories\VipLevelHistoryRepository;
use Illuminate\Http\Request;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}vip-level-history',
    title: 'Manage VIP Level History',
    desc: 'Manage VIP Level History',
    order: 122,
    auth: true
)]
class VipLevelHistoryController extends Controller
{
    protected VipLevelHistoryRepository $repository;

    public function __construct(VipLevelHistoryRepository $repository)
    {
        $this->repository = $repository;
    }

    #[RouteRule(
        parent: '{prefix}vip-level-history',
        title: 'List VIP Level History',
        desc: 'Get List VIP Level History',
        order: 9921,
        auth: true
    )]
    public function index(ListVipLevelHistoryRequest $request)
    {
        $pageSize = $request->validated()['pageSize'];
        $filters = $request->validated();

        $vipLevelHistory = $this->repository->paginateWithFilters($pageSize, $filters);

        return $this->success(__('messages.success'), $vipLevelHistory);
    }

    #[RouteRule(
        parent: '{prefix}vip-level-history',
        title: 'VIP Level History detail',
        desc: 'GET VIP Level History Detail',
        order: 9922,
        auth: true
    )]
    public function show(Request $request, VipLevelHistory $vipLevelHistory)
    {
        // Load relationships using repository
        $vipLevelHistoryWithRelations = $this->repository->findWithRelations($vipLevelHistory->id);

        return $this->success(__('messages.success'), $vipLevelHistoryWithRelations->toArray());
    }
}
