<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Requests\AnnouncementFormRequest;
use App\Http\Controllers\Controller;
use App\Models\Announcement;
use App\Services\AnnouncementService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}announcements',
    title: 'Manage Announcements',
    desc: 'Manage Announcements',
    order: 315,
    auth: true
)]
class AnnouncementController extends Controller
{
    protected AnnouncementService $service;

    public function __construct(AnnouncementService $service)
    {
        $this->service = $service;
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'List Announcements',
        desc: 'Get List Announcements',
        order: 316,
        auth: true
    )]
    public function index(Request $request): Response
    {
        $locale = $request->query('locale');
        $list = $this->service->getList($request, $locale);

        return $this->success(__('larke-admin::common.get_success'), $list);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Announcements detail',
        desc: 'GET Announcements Detail',
        order: 317,
        auth: true
    )]
    public function show(string $newsId): Response
    {
        $announcement = $this->service->getAnnouncement($newsId);

        if (!$announcement) {
            return $this->error(__('larke-admin::common.id_dont_empty'));
        }

        $announcementData = $announcement->toArrayWithTranslations();

        return $this->success(__('larke-admin::common.get_success'), $announcementData);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Announcements Create',
        desc: 'Announcements Create',
        order: 9903,
        auth: true
    )]
    public function store(AnnouncementFormRequest $request): Response
    {
        $news = $this->service->create($request->validated());

        return $this->success(__('larke-admin::common.get_success'), $news);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Update Announcements',
        desc: 'Update Announcements Info',
        order: 318,
        auth: true
    )]
    public function update(string $id, AnnouncementFormRequest $request)
    {
        $news = $this->service->getAnnouncement($id);

        if (empty($news)) {
            return $this->error(__('larke-admin::common.id_dont_empty'));
        }

        $this->service->update($request->validated(), $news);

        return $this->success(__('larke-admin::common.get_success'), $news);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Cancel Announcements',
        desc: 'Cancel Announcements',
        order: 320,
        auth: true
    )]
    public function cancel(Announcement $announcement, Request $request)
    {
        // cancel the announcement instead of delete it
        $announcement->status = 2;
        $announcement->save();

        return $this->success(__('larke-admin::common.get_success'), $announcement);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Delete Announcements',
        desc: 'Delete Announcements',
        order: 319,
        auth: true
    )]
    public function destroy(Announcement $announcement, Request $request)
    {
        $announcement->notifications()->delete();
        // cancel the announcement instead of delete it
        $announcement->delete();

        return $this->success(__('larke-admin::common.get_success'), $announcement);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Announcement Translations',
        desc: 'Get Announcement with All Translations',
        order: 321,
        auth: true
    )]
    public function translations(Announcement $announcement): Response
    {
        $translations = $announcement->getAllTranslations();

        return $this->success(__('larke-admin::common.get_success'), [
            'announcement' => $announcement->toArray(),
            'translations' => $translations,
        ]);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Update Announcement Translation',
        desc: 'Update specific translation for an announcement',
        order: 322,
        auth: true
    )]
    public function updateTranslation(Announcement $announcement, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,message,button_text',
            'locale' => 'required|string|max:10',
            'content' => 'required|string',
        ]);

        $announcement->setTranslation(
            $validated['field'],
            $validated['locale'],
            $validated['content']
        );

        return $this->success(__('larke-admin::common.update_success'), [
            'announcement' => $announcement->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Delete Announcement Translation',
        desc: 'Delete specific translation for an announcement',
        order: 323,
        auth: true
    )]
    public function deleteTranslation(Announcement $announcement, Request $request): Response
    {
        $validated = $request->validate([
            'field' => 'required|string|in:title,message,button_text',
            'locale' => 'required|string|max:10',
        ]);

        $announcement->translations()
            ->where('field_key', $validated['field'])
            ->where('locale', $validated['locale'])
            ->delete();

        return $this->success(__('larke-admin::common.delete_success'), [
            'announcement' => $announcement->toArrayWithTranslations(),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}announcements',
        title: 'Locale Configuration',
        desc: 'Get supported locales configuration',
        order: 324,
        auth: true
    )]
    public function localeConfig(): Response
    {
        $config = config('locales');

        return $this->success(__('larke-admin::common.get_success'), $config);
    }
}
