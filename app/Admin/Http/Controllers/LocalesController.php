<?php

namespace App\Admin\Http\Controllers;

use Illuminate\Http\Response;
use <PERSON>rke\Admin\Annotation\RouteRule;
use <PERSON>rke\Admin\Http\Controller as BaseController;

#[RouteRule(
    slug: '{prefix}locales',
    title: 'Locales Management',
    desc: 'Locales Controllers',
    order: 9950,
    auth: true
)]
class LocalesController extends BaseController
{
    #[RouteRule(
        parent: 'larke-admin.locales',
        title: 'Supported Locales',
        desc: 'Get supported locales configuration',
        order: 9951,
        auth: true
    )]
    public function index(): Response
    {
        $supportedLocales = config('translatable.locales');

        // Map locale codes to human-readable information
        $localeInfo = [
            'en' => ['label' => 'English', 'flag' => '🇺🇸', 'required' => true],
            'zh_CN' => ['label' => '中文', 'flag' => '🇨🇳', 'required' => true],
            'fr' => ['label' => 'Français', 'flag' => '🇫🇷', 'required' => false],
            'de' => ['label' => 'Deutsch', 'flag' => '🇩🇪', 'required' => false],
            'es' => ['label' => 'Español', 'flag' => '🇪🇸', 'required' => false],
            'it' => ['label' => 'Italiano', 'flag' => '🇮🇹', 'required' => false],
            'ja' => ['label' => '日本語', 'flag' => '🇯🇵', 'required' => false],
            'ko' => ['label' => '한국어', 'flag' => '🇰🇷', 'required' => false],
        ];

        $locales = array_map(function ($locale) use ($localeInfo) {
            return [
                'value' => $locale,
                'label' => $localeInfo[$locale]['label'] ?? $locale,
                'flag' => $localeInfo[$locale]['flag'] ?? '🌐',
                'required' => $localeInfo[$locale]['required'] ?? false,
            ];
        }, $supportedLocales);

        return $this->success('Supported locales retrieved successfully', [
            'locales' => $locales,
            'fallback_locale' => config('translatable.fallback_locale'),
        ]);
    }
}
