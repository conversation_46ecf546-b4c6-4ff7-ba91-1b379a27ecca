<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Contribute;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}contributes',
    title: 'Manage Contribute',
    desc: 'Manage Contribute',
    order: 115,
    auth: true
)]
class ContributeController extends Controller
{
    #[RouteRule(
        parent: '{prefix}contributes',
        title: 'List Contribute',
        desc: 'Get List Contribute',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $validated = $request->validate([
            'name' => 'nullable|string',
            'sortBy' => 'nullable|string|in:sort,created_at',
            'sort' => 'nullable|string|in:asc,desc',
            'phone' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);
        $query = Contribute::query();
        $query->with([
            'iTokens' => function ($query) {
                $query->select(['id', 'contribute_id', 'transaction_code', 'amount', 'expired_at']);
            },
            'user' => function ($query) {
                $query->select(['id', 'name', 'phone']);
            },
        ]);

        if (!empty($validated['name'])) {
            $query->whereRelation('user', 'name', 'like', '%' . $validated['name'] . '%');
        }
        if (!empty($validated['phone'])) {
            $query->whereRelation('user', 'phone', 'like', '%' . $validated['phone'] . '%');
        }
        if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
            $query->whereBetween('created_at', [
                Gmt8::toUtc($validated['start_date']),
                Gmt8::toUtc($validated['end_date'], true),
            ]);
        }

        if (!empty($validated['sortBy']) && !empty($validated['sort'])) {
            $query->orderBy($validated['sortBy'], $validated['sort']);
        } else {
            $query->orderBy('created_at', 'desc');
        }
        $contributes = $query->paginate($request->get('pageSize', 10));

        return $this->success(
            __('messages.success'),
            $contributes
        );
    }
}
