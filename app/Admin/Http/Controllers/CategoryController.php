<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Category\UpdateCategoryRequest;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}categories',
    title: 'Manage Category',
    desc: 'Manage Category',
    order: 115,
    auth: true
)]
class CategoryController extends Controller
{
    #[RouteRule(
        parent: '{prefix}categories',
        title: 'List Category',
        desc: 'Get List Category',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        return $this->success(__('messages.success'), Category::paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}categories',
        title: 'Category detail',
        desc: 'GET Category Detail',
        order: 9921,
        auth: true
    )]
    public function show(Request $request, Category $category)
    {
        return $this->success(__('messages.success'), $category->toArray());
    }

    #[RouteRule(
        parent: '{prefix}categories',
        title: 'Update Category',
        desc: 'Update Category Info',
        order: 9921,
        auth: true
    )]
    public function update(Category $category, UpdateCategoryRequest $request)
    {
        $validated = $request->validated();
        if ($request->has('banner')) {
            $validated['banner'] = Storage::disk('public')
                ->putFile('images', $request->file('banner'));
            $validated['banner'] = Storage::disk('public')->url($validated['banner']);
        }
        if ($category->update($validated)) {
            return $this->success(__('messages.success'));
        }

        return $this->error(__('messages.failed'));
    }

    #[RouteRule(
        parent: '{prefix}categories',
        title: 'Delete Category',
        desc: 'Delete Category',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }

    #[RouteRule(
        parent: '{prefix}categories',
        title: 'Delete Category',
        desc: 'Delete Category',
        order: 9921,
        auth: false
    )]
    public function getAll(Request $request)
    {
        return $this->success(__('messages.success'), Category::all());
    }
}
