<?php

namespace App\Admin\Http\Controllers;

use App\Constants\Activities\ActivityCycles;
use App\Constants\Activities\ActivityTypes;
use App\Constants\WalletType;
use App\Http\Controllers\Controller;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}.common',
    title: 'Common functions',
    desc: 'Common functions',
    order: 99000,
    auth: true,
)]
class CommonController extends Controller
{
    #[RouteRule(
        title: 'Activity Cycle data',
        desc: 'Activity Cycle data',
        order: 99002,
        auth: true
    )]
    public function activityCycles()
    {
        $cycleData = [];
        $cycles = ActivityCycles::cases();
        foreach ($cycles as $cycle) {
            $cycleData[] = [
                'value' => $cycle->value,
                'label' => $cycle->label(),
            ];
        }

        return $this->success(__('messages.success'), $cycleData);
    }

    #[RouteRule(
        title: 'Activity Type data',
        desc: 'Activity Type data',
        order: 99003,
        auth: true
    )]
    public function activityTypes()
    {
        $typeData = [];
        $types = ActivityTypes::cases();
        foreach ($types as $type) {
            $typeData[] = [
                'value' => $type->value,
                'label' => $type->label(),
            ];
        }

        return $this->success(__('messages.success'), $typeData);
    }

    #[RouteRule(
        title: 'Wallet Type data',
        desc: 'Wallet Type data',
        order: 99003,
        auth: true
    )]
    public function walletTypes()
    {
        $typeData = [];
        $types = WalletType::cases();
        foreach ($types as $type) {
            $typeData[] = [
                'value' => $type->value,
                'label' => $type->name,
            ];
        }

        return $this->success(__('messages.success'), $typeData);
    }
}
