<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}orders',
    title: 'Manage Order',
    desc: 'Manage Order',
    order: 115,
    auth: true
)]
class OrderController extends Controller
{
    #[RouteRule(
        parent: '{prefix}orders',
        title: 'List Order',
        desc: 'Get List Order',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $filters = $request->validate([
            'start_date' => 'date',
            'end_date' => 'date',
            'phone' => 'string|min:3',
            'name' => 'string|min:2',
            'product_name' => 'string|min:2',
            'pageSize' => 'numeric|min:1',
        ]);
        $query = Order::query();
        if ($request->has('start_date') & $request->has('end_date')) {
            $filters['start_date'] = Gmt8::toUtc($request->start_date);
            $filters['end_date'] = Gmt8::toUtc($request->end_date, true);
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }
        if ($request->has('phone')) {
            $query->whereRelation('user', 'phone', 'like', '%' . $filters['phone'] . '%');
        }

        if ($request->has('name')) {
            $query->whereRelation('user', 'name', 'like', '%' . $filters['name'] . '%');
        }

        if ($request->has('product_name')) {
            $query->whereRelation('product', 'name', 'like', '%' . $filters['product_name'] . '%');
        }

        $query->with(['product', 'user']);

        return $this->success(__('messages.success'), $query->paginate($request->get('pageSize', 10)));
    }
}
