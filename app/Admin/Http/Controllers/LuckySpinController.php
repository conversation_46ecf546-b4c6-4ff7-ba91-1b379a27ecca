<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\LuckySpinResultListRequest;
use App\Http\Requests\UpdateLuckySpinRequest;
use App\Models\LuckySpinEvent;
use App\Models\LuckySpinResult;
use App\Models\LuckySpinReward;
use App\Services\LuckySpinService;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}luckyspin',
    title: 'Manage Lucky spin',
    desc: 'Manage Lucky spin',
    order: 115,
    auth: true
)]
class LuckySpinController extends Controller
{
    private LuckySpinService $luckySpinService;

    public function __construct(
        LuckySpinService $luckySpinService
    ) {
        $this->luckySpinService = $luckySpinService;
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'List luckySpin result',
        desc: 'Get List luckySpin result',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $data = $this->luckySpinService->getLuckySpinList($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'List luckySpin result',
        desc: 'Get List luckySpin result',
        order: 9921,
        auth: true
    )]
    public function result(LuckySpinResultListRequest $request)
    {
        $data = $this->luckySpinService->getLuckySpinResultList($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'luckySpin detail',
        desc: 'GET luckySpin Detail',
        order: 9921,
        auth: true
    )]
    public function show(Request $request, LuckySpinResult $luckySpinResult)
    {
        return $this->success(__('messages.success'), $luckySpinResult->toArray());
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'Update luckySpin',
        desc: 'Update luckySpin Info',
        order: 9921,
        auth: true
    )]
    public function update($id, UpdateLuckySpinRequest $request)
    {
        $validated = $request->validated();
        try {
            $luckySpin = LuckySpinEvent::findOrFail($id);
            $validated['starts_at'] = Gmt8::toUtc($validated['starts_at']);
            if (!empty($validated['ends_at'])) {
                $validated['ends_at'] = Gmt8::toUtc($validated['ends_at']);
            }
            $luckySpin->update($validated);

            return $this->success(__('messages.success'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'luckySpin detail',
        desc: 'GET luckySpin Detail',
        order: 9921,
        auth: true
    )]
    public function createEventReward(Request $request, LuckySpinEvent $event)
    {
        $data = $request->validate([
            'name' => 'required|string',
            'reward_value' => 'required|numeric',
            'probability' => 'required|numeric',
            'icon_url' => 'nullable|file|mimes:jpg,png,svg,webp|max:2048',
        ]);
        $data['reward_type'] = 'usdt';
        $data['currency_code'] = 'USDT';

        if ($request->has('icon_url')) {
            $data['icon_url'] = Storage::disk('public')
                ->putFile('images', $request->file('icon_url'));
            $data['icon_url'] = Storage::disk('public')->url($data['icon_url']);
        }
        $event->rewards()->create($data);

        return $this->success(__('messages.success'), $event->toArray());
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'update luckySpin reward',
        desc: 'update luckySpin reward',
        order: 9921,
        auth: true
    )]
    public function updateEventReward(Request $request, LuckySpinReward $reward)
    {
        $data = $request->validate([
            'name' => 'required|string',
            'reward_value' => 'required|numeric',
            'probability' => 'required|numeric',
            'icon_url' => 'nullable|file|mimes:jpg,png,svg,webp|max:2048',
        ]);
        if ($request->has('icon_url')) {
            $data['icon_url'] = Storage::disk('public')
                ->putFile('images', $request->file('icon_url'));
            $data['icon_url'] = Storage::disk('public')->url($data['icon_url']);
        }
        $reward->update($data);

        return $this->success(__('messages.success'), $reward->toArray());
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'update luckySpin reward',
        desc: 'update luckySpin reward',
        order: 9921,
        auth: true
    )]
    public function assignLuckySpin(Request $request)
    {
        // should validate target id based on individual(user) or group
        $assignData = $request->validate([
            'event_id' => 'required|exists:lucky_spin_events,id',
            'assignment_type' => 'required|in:individual,multiple,group',
            'expires_at' => 'nullable|date',
            'total_chances' => 'required|integer|min:1',
            'guaranteed_rewards' => 'nullable|array',
        ]);
        // verify target id is valid
        if ($assignData['assignment_type'] === 'individual') {
            $assignData['target_id'] = $request->validate([
                'target_id' => 'required|exists:users,id',
            ])['target_id'];
        } elseif ($assignData['assignment_type'] === 'multiple') {
            // target_id should be a array and exists in user table
            $assignData['target_ids'] = $request->validate([
                'target_ids' => 'required|array',
                'target_ids.*' => 'exists:users,id',
            ])['target_ids'];
        } else {
            $assignData['target_id'] = $request->validate([
                'target_id' => 'required|exists:groups,id',
            ])['target_id'];
        }
        // verify Event is active
        /* @var LuckySpinEvent $event */
        $event = LuckySpinEvent::find($assignData['event_id'])->load([
            'rewards' => function ($query) {
                $query->where('is_active', true);
            },
        ]);
        if (!$event->isActive()) {
            return $this->error(__('messages.event_not_active'));
        }
        // verify Event still valid in period time
        if ($event->ends_at && $event->ends_at->lt(now())) {
            return $this->error(__('messages.event_expired'));
        }
        // if have guaranteed_rewards, verify it is still activing
        if (!empty($assignData['guaranteed_rewards'])) {
            // check using loaded relationship
            $invalidRewards = array_filter($assignData['guaranteed_rewards'], function ($rewardId) use ($event) {
                return !$event->rewards->contains('id', $rewardId);
            });
            if (!empty($invalidRewards)) {
                return $this->error(__('messages.invalid_reward'));
            }
            $assignData['guaranteed_rewards'] = array_unique($assignData['guaranteed_rewards']);
        }
        if (empty($assignData['expires_at'])) {
            $assignData['expires_at'] = $event->ends_at;
        } else {
            // check expired day should lower than event end date
            if ($assignData['expires_at'] > $event->ends_at) {
                return $this->error(__('messages.expired_date_invalid'));
            }
        }
        $this->luckySpinService->assignLuckySpinToUser($assignData);

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'luckySpin result',
        desc: 'luckySpin result',
        order: 9921,
        auth: true
    )]
    public function luckySpinResults(Request $request)
    {
        $query = LuckySpinResult::query();
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }
        if ($request->filled('assignment_id')) {
            $query->where('assignment_id', $request->assignment_id);
        }
        $query->with([
            'event', 'user' => function ($query) {
                $query->select(['id', 'name', 'email', 'phone']);
            },
        ]);

        return $this->success(__('messages.success'), $query->paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}luckyspin',
        title: 'create lucky spin',
        desc: 'create lucky spin',
        order: 9921,
        auth: true
    )]
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'is_enabled' => 'required|boolean',
            'starts_at' => 'required|date',
            'ends_at' => 'required|date|after:starts_at',
            'rewards' => 'required|array',
            'rewards.*.name' => 'required|string',
            'rewards.*.reward_value' => 'required|numeric',
            'rewards.*.probability' => 'required|numeric',
            'rewards.*.icon_url' => 'nullable|file|mimes:jpg,png,svg,webp|max:2048',
        ]);
        $newEvent = LuckySpinEvent::create($validated);
        $rewards = $validated['rewards'];
        foreach ($rewards as $reward) {
            $reward['event_id'] = $newEvent->id;
            $reward['reward_type'] = 'usdt';
            $reward['currency_code'] = 'USDT';
            if (!empty($reward['icon_url'])) {
                $path = Storage::disk('public')
                    ->putFile('images', $reward['icon_url']);
                $reward['icon_url'] = Storage::disk('public')->url($path);
            }
            $newEvent->rewards()->create($reward);
        }

        return $this->success(__('messages.success'));
    }
}
