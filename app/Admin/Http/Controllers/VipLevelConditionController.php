<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\VipLevelCondition\StoreVipLevelConditionRequest;
use App\Http\Requests\VipLevelCondition\UpdateVipLevelConditionRequest;
use App\Models\VipLevel;
use App\Models\VipLevelCondition;
use Illuminate\Http\Request;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}vip-level-conditions',
    title: 'Manage VIP Level Conditions',
    desc: 'Manage VIP Level Conditions',
    order: 121,
    auth: true
)]
class VipLevelConditionController extends Controller
{
    #[RouteRule(
        parent: '{prefix}vip-level-conditions',
        title: 'List VIP Level Conditions',
        desc: 'Get List VIP Level Conditions',
        order: 9931,
        auth: true
    )]
    public function index(Request $request)
    {
        $pageSize = $request->get('pageSize', 10);
        $vipLevelId = $request->get('vip_level_id');
        $isActive = $request->get('is_active');

        $query = VipLevelCondition::with('vipLevel');

        if ($vipLevelId) {
            $query->where('vip_level_id', $vipLevelId);
        }

        if ($isActive !== null) {
            $query->where('is_active', $isActive);
        }

        $conditions = $query->orderBy('vip_level_id')
            ->orderBy('created_at', 'desc')
            ->paginate($pageSize);

        return $this->success(__('messages.success'), $conditions);
    }

    #[RouteRule(
        parent: '{prefix}vip-level-conditions',
        title: 'VIP Level Condition detail',
        desc: 'GET VIP Level Condition Detail',
        order: 9932,
        auth: true
    )]
    public function show(VipLevelCondition $vipLevelCondition)
    {
        $vipLevelCondition->load('vipLevel');

        return $this->success(__('messages.success'), $vipLevelCondition->toArray());
    }

    #[RouteRule(
        parent: '{prefix}vip-level-conditions',
        title: 'Create VIP Level Condition',
        desc: 'Create New VIP Level Condition',
        order: 9933,
        auth: true
    )]
    public function store(StoreVipLevelConditionRequest $request)
    {
        $validated = $request->validated();

        try {
            // Check if VIP level exists and is valid
            $vipLevel = VipLevel::find($validated['vip_level_id']);
            if (!$vipLevel) {
                return $this->error(__('messages.vip_level_condition.vip_level_not_found'), 404);
            }

            // Check if there's already any condition for this VIP level (we don't allow multiple conditions per VIP level)
            $existingCondition = VipLevelCondition::where('vip_level_id', $validated['vip_level_id'])->first();

            if ($existingCondition) {
                return $this->error(__('messages.vip_level_condition.condition_already_exists'), 422);
            }

            // Check for threshold conflicts with other VIP level conditions
            $conflictErrors = $this->checkThresholdConflicts($validated);
            if (!empty($conflictErrors)) {
                return $this->error(implode('; ', $conflictErrors), 422);
            }

            // Set default active status if not provided
            if (!isset($validated['is_active'])) {
                $validated['is_active'] = true;
            }

            $vipLevelCondition = VipLevelCondition::create($validated);

            if ($vipLevelCondition) {
                $vipLevelCondition->load('vipLevel');

                return $this->success(__('messages.success'), $vipLevelCondition->toArray());
            }

            return $this->error(__('messages.failed'));
        } catch (\Exception $e) {
            \Log::error('Error creating VIP level condition: ' . $e->getMessage());

            return $this->error(__('messages.vip_level_condition.failed_to_create'), 500);
        }
    }

    #[RouteRule(
        parent: '{prefix}vip-level-conditions',
        title: 'Update VIP Level Condition',
        desc: 'Update VIP Level Condition Info',
        order: 9934,
        auth: true
    )]
    public function update(VipLevelCondition $vipLevelCondition, UpdateVipLevelConditionRequest $request)
    {
        $validated = $request->validated();

        try {
            // If VIP level is being changed, check if it exists and if another condition exists for that level
            if (isset($validated['vip_level_id']) && $validated['vip_level_id'] != $vipLevelCondition->vip_level_id) {
                $vipLevel = VipLevel::find($validated['vip_level_id']);
                if (!$vipLevel) {
                    return $this->error(__('messages.vip_level_condition.vip_level_not_found'), 404);
                }

                // Check if there's already any condition for the new VIP level (excluding current record)
                $existingCondition = VipLevelCondition::where('vip_level_id', $validated['vip_level_id'])
                    ->where('id', '!=', $vipLevelCondition->id)
                    ->first();

                if ($existingCondition) {
                    return $this->error(__('messages.vip_level_condition.condition_already_exists'), 422);
                }
            }

            // Check for threshold conflicts with other VIP level conditions (excluding current record)
            $conflictErrors = $this->checkThresholdConflicts($validated, $vipLevelCondition->id);
            if (!empty($conflictErrors)) {
                return $this->error(implode('; ', $conflictErrors), 422);
            }

            if ($vipLevelCondition->update($validated)) {
                $vipLevelCondition->fresh()->load('vipLevel');

                return $this->success(__('messages.success'), $vipLevelCondition->toArray());
            }

            return $this->error(__('messages.failed'));
        } catch (\Exception $e) {
            \Log::error('Error updating VIP level condition: ' . $e->getMessage());

            return $this->error(__('messages.vip_level_condition.failed_to_update'), 500);
        }
    }

    #[RouteRule(
        parent: '{prefix}vip-level-conditions',
        title: 'Delete VIP Level Condition',
        desc: 'Delete VIP Level Condition',
        order: 9935,
        auth: true
    )]
    public function destroy(VipLevelCondition $vipLevelCondition)
    {
        try {
            if ($vipLevelCondition->delete()) {
                return $this->success(__('messages.success'));
            }

            return $this->error(__('messages.failed'));
        } catch (\Exception $e) {
            return $this->error(__('messages.failed'), 500);
        }
    }

    /**
     * Check for threshold conflicts with existing VIP level conditions
     *
     * @param  array  $validated  The validated data
     * @param  int|null  $excludeId  ID to exclude from conflict check (for updates)
     * @return array Array of conflict error messages
     */
    private function checkThresholdConflicts(array $validated, ?int $excludeId = null): array
    {
        $errors = [];

        // Get all existing active conditions (excluding the current one if updating)
        $query = VipLevelCondition::where('is_active', true);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        $existingConditions = $query->get();

        foreach ($existingConditions as $condition) {
            // Check if the new thresholds would conflict with existing ones
            // Two conditions conflict if they have overlapping threshold ranges that could
            // make it ambiguous which VIP level a user qualifies for

            $conflicts = [];

            // Check remaining balance threshold conflicts
            if (
                $validated['remaining_balance_threshold'] > 0 &&
                $this->hasThresholdConflict(
                    $validated['remaining_balance_threshold'],
                    $condition->remaining_balance_threshold
                )
            ) {
                $conflicts[] = __('messages.vip_level_condition.threshold_conflicts.remaining_balance');
            }

            // Check total deposit threshold conflicts
            if (
                $validated['total_deposit_threshold'] > 0 &&
                $this->hasThresholdConflict(
                    $validated['total_deposit_threshold'],
                    $condition->total_deposit_threshold
                )
            ) {
                $conflicts[] = __('messages.vip_level_condition.threshold_conflicts.total_deposit');
            }

            // Check subordinates balance threshold conflicts
            if (
                $validated['subordinates_balance_threshold'] > 0 &&
                $this->hasThresholdConflict(
                    $validated['subordinates_balance_threshold'],
                    $condition->subordinates_balance_threshold
                )
            ) {
                $conflicts[] = __('messages.vip_level_condition.threshold_conflicts.subordinates_balance');
            }

            // Check subordinates deposit threshold conflicts
            if (
                $validated['subordinates_deposit_threshold'] > 0 &&
                $this->hasThresholdConflict(
                    $validated['subordinates_deposit_threshold'],
                    $condition->subordinates_deposit_threshold
                )
            ) {
                $conflicts[] = __('messages.vip_level_condition.threshold_conflicts.subordinates_deposit');
            }

            // Check subordinates count threshold conflicts
            if (
                isset($validated['subordinates_threshold']) &&
                $validated['subordinates_threshold'] > 0 &&
                $this->hasThresholdConflict(
                    $validated['subordinates_threshold'],
                    $condition->subordinates_threshold
                )
            ) {
                $conflicts[] = __('messages.vip_level_condition.threshold_conflicts.subordinates_count');
            }

            // If all thresholds are identical, this is a complete conflict
            if (count($conflicts) === 5) {
                $errors[] = __('messages.vip_level_condition.threshold_conflicts.complete_conflict') . ' ' . $condition->vipLevel->name;
            } elseif (!empty($conflicts)) {
                $conflictText = implode(', ', $conflicts);
                $errors[] = __('messages.vip_level_condition.threshold_conflicts.partial_conflict', [
                    'vip_level_name' => $condition->vipLevel->name,
                    'conflict_types' => $conflictText,
                ]);
            }
        }

        return $errors;
    }

    /**
     * Check if two threshold values conflict (are identical)
     * Identical thresholds can create ambiguous qualification scenarios
     */
    private function hasThresholdConflict($threshold1, $threshold2): bool
    {
        // For integer values (subordinates count), use exact comparison
        if (is_int($threshold1) && is_int($threshold2)) {
            return $threshold1 === $threshold2;
        }

        // For float values, use tolerance for floating point precision
        return abs((float) $threshold1 - (float) $threshold2) < 0.01;
    }
}
