<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Deposit;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}deposits',
    title: 'Manage Deposit',
    desc: 'Manage Deposit',
    order: 115,
    auth: true
)]
class DepositController extends Controller
{
    #[RouteRule(
        parent: '{prefix}deposits',
        title: 'List Deposit',
        desc: 'Get List Deposit',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $depositQuery = Deposit::with(['user', 'wallet']);
        if ($request->has('start_date') && $request->has('end_date')) {
            $depositQuery->whereBetween('created_at', [
                Gmt8::toUtc($request->start_date),
                Gmt8::toUtc($request->end_date, true),
            ]);
        }

        return $this->success(__('messages.success'), $depositQuery->paginate($request->get('pageSize', 10)));
    }
}
