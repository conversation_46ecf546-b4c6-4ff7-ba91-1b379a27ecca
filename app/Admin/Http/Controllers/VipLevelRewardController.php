<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\VipLevelReward\ListVipLevelRewardRequest;
use App\Models\VipLevelReward;
use App\Repositories\VipLevelRewardRepository;
use Illuminate\Http\Request;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}vip-level-rewards',
    title: 'Manage VIP Level Rewards',
    desc: 'Manage VIP Level Rewards',
    order: 121,
    auth: true
)]
class VipLevelRewardController extends Controller
{
    protected VipLevelRewardRepository $repository;

    public function __construct(VipLevelRewardRepository $repository)
    {
        $this->repository = $repository;
    }

    #[RouteRule(
        parent: '{prefix}vip-level-rewards',
        title: 'List VIP Level Rewards',
        desc: 'Get List VIP Level Rewards',
        order: 9921,
        auth: true
    )]
    public function index(ListVipLevelRewardRequest $request)
    {
        $pageSize = $request->validated()['pageSize'];
        $filters = $request->validated();

        $vipLevelRewards = $this->repository->paginateWithFilters($pageSize, $filters);

        return $this->success(__('messages.success'), $vipLevelRewards);
    }

    #[RouteRule(
        parent: '{prefix}vip-level-rewards',
        title: 'VIP Level Reward detail',
        desc: 'GET VIP Level Reward Detail',
        order: 9922,
        auth: true
    )]
    public function show(Request $request, VipLevelReward $vipLevelReward)
    {
        // Load relationships using repository
        $vipLevelRewardWithRelations = $this->repository->findWithRelations($vipLevelReward->id);

        return $this->success(__('messages.success'), $vipLevelRewardWithRelations->toArray());
    }
}
