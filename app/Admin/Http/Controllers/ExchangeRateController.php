<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}exchange-rates',
    title: 'Exchange rate functions',
    desc: 'Exchange rate functions',
    order: 9930,
    auth: true,
)]
class ExchangeRateController extends Controller
{
    #[RouteRule(
        parent: '{prefix}exchange-rates',
        title: 'Exchange Rates',
        desc: 'Get exchange rates from payment service',
        order: 9931,
        auth: true
    )]
    public function exchangeRates(Request $request)
    {
        $rates = ExchangeRate::paginate($request->get('pageSize', 10));

        return $this->success(__('messages.success'), $rates);
    }
}
