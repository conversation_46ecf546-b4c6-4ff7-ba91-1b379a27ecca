<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\UploadFileRequest;
use App\Models\BannerSlider;
use App\Services\UploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}banner-sliders',
    title: 'BannerSlider',
    desc: 'BannerSlider',
    order: 155,
    auth: true
)]
class BannerSliderController extends Controller
{
    #[RouteRule(
        title: 'List Slider',
        desc: 'List',
        order: 1,
        auth: true
    )]
    public function index(Request $request)
    {
        $list = BannerSlider::paginate($request->input('pageSize', 10));

        return $this->success(__('messages.success'), $list);
    }

    #[RouteRule(
        title: 'Create Slider',
        desc: 'Create',
        order: 1,
        auth: true
    )]
    public function store(UploadFileRequest $request)
    {
        /** @var UploadService $uploadService */
        $uploadService = App::make(UploadService::class);
        $uploadedFile = $uploadService->uploadFile($request->image, true);
        $slider = BannerSlider::create([
            'name' => $uploadedFile->name,
            'path' => Storage::disk($uploadedFile->uploadDisk)->url($uploadedFile->path),
            'mime' => $uploadedFile->mimeType,
            'extension' => $uploadedFile->extension,
            'size' => $uploadedFile->size,
            'md5' => $uploadedFile->md5,
            'sha1' => $uploadedFile->sha1,
            'driver' => $uploadedFile->driver,
            'status' => 1,
            'type' => $request->type ? Str::slug($request->type) : null,
            'redirect_link' => $request->redirect_link,
        ]);

        return $this->success(__('messages.success'), ['slider' => $slider]);
    }

    public function update(BannerSlider $bannerSlider, Request $request)
    {
        $request->validate([
            'image' => 'file|mimes:jpg,png,svg,webp|max:2048',
            'status' => 'bool',
        ]);

        $updateFile = [];
        if ($request->has('image')) {
            /** @var UploadService $uploadService */
            $uploadService = App::make(UploadService::class);
            $uploadedFile = $uploadService->uploadFile($request->image, true);
            if (!$uploadedFile) {
                return $this->error(__('messages.unknown_error'));
            }
            $uploadService->deleteFile($bannerSlider->path);
            $updateFile = [
                'name' => $uploadedFile->name,
                'path' => Storage::disk($uploadedFile->uploadDisk)->url($uploadedFile->path),
                'mime' => $uploadedFile->mimeType,
                'extension' => $uploadedFile->extension,
                'size' => $uploadedFile->size,
                'md5' => $uploadedFile->md5,
                'sha1' => $uploadedFile->sha1,
                'driver' => $uploadedFile->driver,
            ];
        }
        if ($request->has('status')) {
            $updateFile['status'] = $request->status;
        }
        if ($request->has('type')) {
            $updateFile['type'] = Str::slug($request->type);
        }
        if ($request->has('redirect_link')) {
            $updateFile['redirect_link'] = $request->redirect_link;
        }
        if (count($updateFile) > 0) {
            $bannerSlider->update($updateFile);
        }

        return $this->success(__('messages.success'), ['slider' => $bannerSlider]);
    }

    public function destroy(BannerSlider $bannerSlider)
    {
        /** @var UploadService $uploadService */
        $uploadService = App::make(UploadService::class);
        $uploadService->deleteFile($bannerSlider->path);
        $bannerSlider->delete();

        return $this->success(__('messages.success'));
    }
}
