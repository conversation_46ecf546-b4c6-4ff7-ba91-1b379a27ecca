<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\HourlyWithdrawRequest;
use App\Services\DepositService;
use App\Services\StatisticsService;
use App\Services\WithdrawService;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}dashboard',
    title: 'Dashboard Report',
    desc: 'Dashboard Report',
    order: 200,
    auth: true
)]
class DashboardController extends Controller
{
    private WithdrawService $withdrawService;

    private DepositService $depositService;

    private StatisticsService $statisticsService;

    public function __construct(
        WithdrawService $withdrawService,
        DepositService $depositService,
        StatisticsService $statisticsService
    ) {
        $this->withdrawService = $withdrawService;
        $this->depositService = $depositService;
        $this->statisticsService = $statisticsService;
    }

    #[RouteRule(
        parent: '{prefix}dashboard.hourly-withdraw',
        title: 'getHourlyWithdraw Report',
        desc: 'getHourlyWithdraw Report',
        order: 1,
        auth: true
    )]
    public function getHourlyWithdraw(HourlyWithdrawRequest $request)
    {
        $data = $this->withdrawService->getHourlyTransactions($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}dashboard.hourly-deposit',
        title: 'getHourlyDeposit Report',
        desc: 'getHourlyDeposit Report',
        order: 2,
        auth: true
    )]
    public function getHourlyDeposit(HourlyWithdrawRequest $request)
    {
        $data = $this->depositService->getHourlyTransactions($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}dashboard.hourly-order',
        title: 'getHourlyOrder Report',
        desc: 'getHourlyOrder Report',
        order: 2,
        auth: true
    )]
    public function getHourlyOrder(HourlyWithdrawRequest $request)
    {
        $data = $this->statisticsService->getHourlyOrder($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}dashboard.userStatistic',
        title: 'userStatistic Report',
        desc: 'userStatistic Report',
        order: 2,
        auth: true
    )]
    public function userStatistic(HourlyWithdrawRequest $request)
    {
        $data = $this->statisticsService->getUserStatistic($request);
        $data['hourly'] = $this->statisticsService->getRegisterUserStatistic($request);

        return $this->success(__('messages.success'), $data);
    }

    #[RouteRule(
        parent: '{prefix}dashboard.userHourlyStatistic',
        title: 'userHourlyStatistic Report',
        desc: 'userHourlyStatistic Report',
        order: 2,
        auth: true
    )]
    public function userRegisterHourly(HourlyWithdrawRequest $request)
    {
        $data = $this->statisticsService->getRegisterUserStatistic($request);

        return $this->success(__('messages.success'), $data);
    }
}
