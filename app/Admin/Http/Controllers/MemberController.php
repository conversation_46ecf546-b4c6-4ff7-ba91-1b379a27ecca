<?php

namespace App\Admin\Http\Controllers;

use App\Constants\ModifyBalanceType;
use App\Constants\OrderStatus;
use App\Constants\WalletType;
use App\Contracts\WalletServiceInterface;
use App\Events\UserRegistered;
use App\Exceptions\Wallet\InvalidAmount;
use App\Http\Client\IdenAuthenClient;
use App\Http\Controllers\Controller;
use App\Http\Requests\Member\AddFundsRequest;
use App\Http\Requests\Member\CreateUserRequest;
use App\Http\Requests\Member\ListMemberRequest;
use App\Http\Requests\Member\UpdateMemberGroupRequest;
use App\Http\Requests\Member\UpdateMemberRequest;
use App\Models\Order;
use App\Models\Otp;
use App\Models\TeamStatistic;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\MemberService;
use App\Services\SystemSettingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}members',
    title: 'Manage Member',
    desc: 'Manage Member',
    order: 115,
    auth: true
)]
class MemberController extends Controller
{
    public function __construct(
        protected MemberService $memberService,
        protected UserRepository $userRepository
    ) {
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'List Member',
        desc: 'Get List Member',
        order: 9921,
        auth: true
    )]
    public function index(ListMemberRequest $request)
    {
        $filters = $request->validated();
        $result = $this->memberService->getListMember(filter: $filters, pageSize: $request->get('pageSize', 10));

        return $this->success(__('messages.success'), $result);
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Create User',
        desc: 'Create a new user account',
        order: 9920,
        auth: true
    )]
    public function createUser(CreateUserRequest $request)
    {
        $parent = User::where('invitation_code', $request->invitation_code)->first();

        if (empty($parent->id)) {
            return $this->error(__('messages.invalid_invitation_code'), 400);
        }

        try {
            [$user, $token] = DB::transaction(function () use ($request, $parent) {
                // Create user
                $user = User::create([
                    'name' => '',
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'password' => Hash::make($request->password),
                    'transaction_passcode' => Hash::make($request->transaction_passcode),
                    'parent_id' => $parent->id,
                    'invitation_code' => $this->userRepository->generateInvitationCode(),
                    'account_type' => $request->account_type,
                ]);

                // Get parent hierarchy up to 5 levels
                $hierarchyData = $this->userRepository->getParentHierarchy($user->id);
                $hierarchy = $this->userRepository->buildHierarchy($user, $hierarchyData);
                $user->hierarchy = $hierarchy;
                $user->save();

                // Create wallets
                app(WalletServiceInterface::class)->createWallets($user);

                // Generate admin token (optional - admin doesn't need user token)
                $token = null;
                $user = $user->refresh();

                return [$user, $token];
            });

            // Dispatch event to update team stats
            event(new UserRegistered($user, $request->validated()));

            return $this->success(__('messages.user_created_successfully'), [
                'user' => $user->only(['id', 'name', 'email', 'phone', 'invitation_code', 'created_at']),
            ]);
        } catch (\Throwable $e) {
            Log::error("Admin user creation error: {$e->getMessage()}");

            return $this->error(__('messages.user_creation_failed') . ': ' . $e->getMessage(), 500);
        }
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Member detail',
        desc: 'Update member detail',
        order: 9921,
        auth: true
    )]
    public function show(Request $request, User $user)
    {
        return $this->success(__('messages.success'), $user->toArray());
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Update Member',
        desc: 'Update Member Info',
        order: 9921,
        auth: true
    )]
    public function update(User $member, UpdateMemberRequest $request)
    {
        $validated = $request->validated();
        if ($request->has('password')) {
            $validated['password'] = Hash::make($validated['password']);
        }
        if ($request->has('passcode')) {
            $validated['transaction_passcode'] = Hash::make($validated['passcode']);
            unset($validated['passcode']);
        }
        if ($request->has('active') && $request->active == false) {
            $member->tokens()->delete();
        }
        if ($request->has('id_name') && $request->has('id_card')) {
            $validated['name'] = $validated['id_name'];
        }

        // Handle agent type changes with validation
        if ($request->has('agent_type')) {
            $currentType = $member->agent_type ?? 'regular';
            $newType = $request->agent_type;

            // Define allowed upgrades for each level
            $allowedUpgrades = [
                'regular' => ['regional_super_agent', 'silver_super_agent', 'gold_super_agent'],
                'regional_super_agent' => ['silver_super_agent', 'gold_super_agent'],
                'silver_super_agent' => ['gold_super_agent'],
                'gold_super_agent' => [], // Cannot be changed
            ];

            if ($currentType === 'gold_super_agent') {
                abort(422, __('messages.member.gold_super_agent_cannot_change', locale: 'zh_CN'));
            } elseif (!in_array($newType, $allowedUpgrades[$currentType])) {
                abort(422, __('messages.member.invalid_agent_type_change', locale: 'zh_CN'));
            } else {
                $validated['agent_type'] = $newType;
            }
        }

        // Handle partner type changes
        if ($request->has('partner_type')) {
            $validated['partner_type'] = $request->partner_type;
        }

        $member->update($validated);

        return $this->success(__('messages.success'), $member->toArray());
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Delete Member',
        desc: 'Delete Member',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'All Member',
        desc: 'All Member',
        order: 9922,
        auth: true
    )]
    public function userOptions()
    {
        return $this->success(__('messages.success'), User::all()->toArray());
    }

    /**
     * @throws InvalidAmount
     */
    #[RouteRule(
        slug: '{prefix}balances.modify',
        title: 'Add Funds To Member',
        desc: 'Add Funds To Member',
        order: 115,
        auth: true
    )]
    public function modifyBalance(AddFundsRequest $request)
    {
        $memberPhones = array_unique(explode(',', $request->phones));
        $notFound = [];
        foreach ($memberPhones as $memberPhone) {
            $member = $this->memberService->getMemberByPhone($memberPhone);
            if ($member) {
                $this->memberService->modifyBalance(
                    $member,
                    $request->amount,
                    ModifyBalanceType::tryFrom($request->type),
                    WalletType::getType($request->wallet_type),
                    $request->remark
                );
            } else {
                $notFound[] = $memberPhone;
            }
        }

        return $this->success(__('messages.success'), [
            'not_found' => $notFound,
        ]);
    }

    #[RouteRule(
        slug: '{prefix}members.verify',
        title: 'Add Funds To Member',
        desc: 'Add Funds To Member',
        order: 115,
        auth: true
    )]
    public function verifyIdentify(User $member, Request $request)
    {
        $validated = $request->validate([
            'idNo' => 'string|required|unique:users,id_card|regex:/[0-9]{17}[0-9X]{1}/i',
            'name' => 'string|required',
        ], [
            'idNo' => 'member.id_card_taken',
            'idNo.regex' => 'member.id_format_not_match',
        ]);
        if (config('app.bypass_identify_verification')) {
            // overwrite config of force bypass id verification of 3rd
            $bypassVerification = true;
            $result = [
                'idNo' => $request->idNo,
                'name' => $request->name,
            ];
        } else {
            $verifyIdentifyService = App::make(IdenAuthenClient::class);
            $result = $verifyIdentifyService->sendRequest($validated);
            $bypassVerification = SystemSettingService::getSetting('force_bypass_id_verification');
        }
        // Early return for failed verification
        if ((!$result || ($result['respCode'] ?? '') !== '0000') && !$bypassVerification) {
            return $this->success(__('messages.failed'), ['result' => $result]);
        }

        // Handle bypass scenario
        if ($bypassVerification) {
            $result = [
                ...$result,
                'respCode' => '0000',
                'respMessage' => __('messages.force_bypass_id_verification_success'),
            ];
        }

        // Update member details
        $member->update([
            'id_card' => $result['idNo'],
            'name' => $result['name'],
            'id_name' => $result['name'],
            'sex' => isset($result['sex']) ? ($result['sex'] === 'M' ? 1 : 2) : null,
        ]);

        return $this->success(__('messages.success'), ['result' => $result]);
    }

    #[RouteRule(
        slug: '{prefix}members.generate-otp',
        title: 'Generate OTP',
        desc: 'Generate OTP',
        order: 115,
        auth: true
    )]
    public function generateOtp(User $member)
    {
        $returnData = [
            'code' => null,
            'expires_at' => null,
        ];

        $existingOtp = Otp::query()->whereNull('user_id')
            ->where('phone', $member->phone)
            ->where('expired_at', '>', Carbon::now())
            ->where('type', '=', 'login_by_otp')
            ->whereNull('verified_at')
            ->first();

        if ($existingOtp) {
            // If OTP exists and is valid, return a response indicating it is still active
            $returnData['id'] = $existingOtp->id;
            $returnData['code'] = $existingOtp->code;
            $returnData['expired_at'] = Carbon::parse($existingOtp->expired_at)->addHours(8)->toDateTimeString();
        } else {
            DB::beginTransaction();
            try {
                $otp = new Otp();
                $randomPin = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
                $otp->user_id = null;
                $otp->phone = $member->phone;
                $otp->code = $randomPin;
                $otp->type = 'login_by_otp';
                $otp->expired_at = Carbon::now()->addMinutes(10);
                $otp->save();

                DB::commit();
                $returnData['id'] = $otp->id;
                $returnData['code'] = $otp->code;
                $returnData['expired_at'] = Carbon::parse($otp->expired_at)->addHours(8)->toDateTimeString();
            } catch (\Exception $exception) {
                DB::rollBack();
                Log::error($exception->getMessage());
            }
        }

        return $this->success(__('messages.success'), ['otp' => $returnData]);
    }

    #[RouteRule(
        slug: '{prefix}members.generate-transaction-code',
        title: 'Generate Transaction Code',
        desc: 'Generate Transaction Code',
        order: 115,
        auth: true
    )]
    public function generateTransactionCode(User $member)
    {
        $returnData = [
            'code' => null,
            'expires_at' => null,
        ];

        $existingOtp = Otp::query()
            ->where('phone', $member->phone)
            ->whereNull('user_id')
            ->where('expired_at', '>', Carbon::now())
            ->where('type', '=', 'temp_transaction_code')
            ->whereNull('verified_at')
            ->first();

        if ($existingOtp) {
            // If OTP exists and is valid, return a response indicating it is still active
            $returnData['id'] = $existingOtp->id;
            $returnData['code'] = $existingOtp->code;
            $returnData['expired_at'] = Carbon::parse($existingOtp->expired_at)->addHours(8)->toDateTimeString();
        } else {
            DB::beginTransaction();
            try {
                $otp = new Otp();
                $randomPin = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
                $otp->user_id = null;
                $otp->phone = $member->phone;
                $otp->code = $randomPin;
                $otp->type = 'temp_transaction_code';
                $otp->expired_at = Carbon::now()->addMinutes(10);
                $otp->save();

                DB::commit();

                $returnData['id'] = $otp->id;
                $returnData['code'] = $otp->code;
                $returnData['expired_at'] = Carbon::parse($otp->expired_at)->addHours(8)->toDateTimeString();
            } catch (\Exception $exception) {
                DB::rollBack();
                Log::error($exception->getMessage());

                return $this->error(__('messages.unknown_error'));
            }
        }

        return $this->success(__('messages.success'), ['otp' => $returnData]);
    }

    #[RouteRule(
        slug: '{prefix}members.access',
        title: 'Lock Users',
        desc: 'Lock Users',
        order: 115,
        auth: true
    )]
    public function lockMember(Request $request)
    {
        $validated = $request->validate([
            'user_ids' => 'array',
            'user_ids.*' => 'integer|exists:users,id',
        ]);
        if (isset($validated['user_ids']) && count($validated['user_ids']) > 0) {
            User::whereIn('id', $validated['user_ids'])->update([
                'active' => 0,
            ]);
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        slug: '{prefix}members.access',
        title: 'Unlock user',
        desc: 'Unlock user',
        order: 115,
        auth: true
    )]
    public function unlockMember(Request $request)
    {
        $validated = $request->validate([
            'user_ids' => 'array',
            'user_ids.*' => 'integer|exists:users,id',
        ]);
        if (isset($validated['user_ids']) && count($validated['user_ids']) > 0) {
            User::whereIn('id', $validated['user_ids'])->update([
                'active' => 1,
            ]);
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        slug: '{prefix}members.sendPromotionCard',
        title: 'sendPromotionCard',
        desc: 'sendPromotionCard',
        order: 115,
        auth: true
    )]
    public function sendPromotionCard(Request $request)
    {
        $validated = $request->validate([
            'ids' => 'array',
            'ids.*' => 'integer|exists:users,id',
            'promotion_card_id' => 'required|integer|exists:promotion_cards,id',
            'quantity' => 'required|integer|min:1',
        ]);

        if (!empty($validated['ids'])) {
            $this->memberService->sendPromotionCard($validated['ids'], $validated['promotion_card_id'], $validated['quantity']);
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        slug: '{prefix}members.sendCoupon',
        title: 'sendCoupon',
        desc: 'sendCoupon',
        order: 116,
        auth: true
    )]
    public function sendCoupon(Request $request)
    {
        $validated = $request->validate([
            'ids' => 'array',
            'ids.*' => 'integer|exists:users,id',
            'coupon_id' => [
                'required',
                'integer',
                Rule::exists('coupons', 'id')->where('active', true),
            ],
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        if (!empty($validated['ids'])) {
            $this->memberService->sendCoupon($validated['ids'], $validated['coupon_id'], $validated['quantity']);
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Member Investment',
        desc: 'Get Member Investment Details',
        order: 9923,
        auth: true
    )]
    public function getMemberInvestment(User $member)
    {
        $investment = Order::where('user_id', $member->id)
            ->where('status', OrderStatus::Processed->value)
            ->select([
                DB::raw('SUM(total_amount) as investment'),
            ])
            ->first();

        return $this->success(__('messages.success'), [
            'investment' => $investment->investment ?? 0,
        ]);
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Team Investment',
        desc: 'Get Team Investment Details',
        order: 9924,
        auth: true
    )]
    public function getTeamInvestment(User $member)
    {
        // Get all team members (subordinates)
        $teamMembers = TeamStatistic::where(function ($query) use ($member) {
            $query->where('hierarchy', 'like', $member->id . '|%')
                ->orWhere('hierarchy', 'like', '%|' . $member->id . '|%')
                ->orWhere('hierarchy', 'like', '%|' . $member->id);
        })->pluck('user_id');
        $teamMembers->push($member->id);

        // sum total_amount of team members and him self
        $teamInvestment = Order::whereIn('user_id', $teamMembers)
            ->where('status', OrderStatus::Processed->value)
            ->select([
                DB::raw('SUM(total_amount) as total_team_investment'),
            ])
            ->first();

        return $this->success(__('messages.success'), [
            'investment' => $teamInvestment->total_team_investment ?? 0,
        ]);
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Toggle Bypass Face Auth',
        desc: 'Enable or disable bypass face authentication for a user',
        order: 9921,
        auth: true
    )]
    public function toggleBypassFaceAuth(User $member, Request $request)
    {
        $validated = $request->validate([
            'bypass_face_auth' => 'required|boolean',
        ]);

        $member->bypass_face_auth = $validated['bypass_face_auth'];
        $member->save();

        return $this->success(
            $validated['bypass_face_auth']
                ? __('messages.bypass_face_auth_enabled')
                : __('messages.bypass_face_auth_disabled'),
            $member->only(['id', 'name', 'phone', 'bypass_face_auth'])
        );
    }

    #[RouteRule(
        parent: '{prefix}members',
        title: 'Update Groups of users',
        desc: 'Update Groups of users',
        order: 9921,
        auth: true
    )]
    public function updateMemberGroup(UpdateMemberGroupRequest $request)
    {
        try {
            $validated = $request->validated();
            User::whereIn('id', $validated['user_ids'])->update([
                'group_id' => $validated['group_id'],
            ]);

            return $this->success(__('messages.success'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
