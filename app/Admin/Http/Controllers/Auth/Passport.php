<?php

declare(strict_types=1);

namespace App\Admin\Http\Controllers\Auth;

use App\Models\Admin as AdminModel;
use App\Models\LarkeAdminSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Controller\Base;
use Larke\Admin\Http\Response;
use Larke\Admin\Http\ResponseCode;
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\RSA;

use function Larke\Admin\do_action;

/**
 * 登陆
 *
 * @create 2020-10-19
 *
 * <AUTHOR>
 */
#[RouteRule(
    slug: '{prefix}passport',
    title: '登陆',
    desc: '系统登陆管理',
    order: 150,
    auth: false
)]
class Passport extends Base
{
    /**
     * 登陆
     *
     * @return Response
     */
    #[RouteRule(
        title: '登陆',
        desc: '登陆登陆',
        order: 98,
        auth: false
    )]
    public function login(Request $request)
    {
        // 监听事件
        do_action('passport_login_before', $request);

        $data = $request->all();
        $validator = Validator::make($data, [
            'name' => 'required',
            'password' => 'required',
            'captcha' => 'required|size:4',
        ], [
            'name.required' => __('larke-admin::passport.passport_dont_empty'),
            'password.required' => __('larke-admin::passport.password_dont_empty'),
            'captcha.required' => __('larke-admin::passport.captcha_dont_empty'),
            'captcha.size' => __('larke-admin::passport.captcha_error'),
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), ResponseCode::LOGIN_ERROR);
        }

        $captchaKey = config('larkeadmin.passport.header_captcha_key');
        $captchaUniq = $request->header($captchaKey);
        $captcha = $request->input('captcha');
        if (!app('larke-admin.captcha')->check($captcha, $captchaUniq)) {
            return $this->error(__('larke-admin::passport.captcha_check_fail'), ResponseCode::LOGIN_ERROR);
        }

        // 校验密码
        $name = $request->input('name');
        /** @var AdminModel $admin */
        $admin = AdminModel::where('name', $name)
            ->first();
        if (empty($admin)) {
            return $this->error(__('larke-admin::passport.passport_dont_exists'), ResponseCode::LOGIN_ERROR);
        }

        $adminInfo = $admin
            ->makeVisible(['password', 'password_salt'])
            ->toArray();

        $password = $request->input('password');
        if (strlen($password) <= 16) {
            return $this->error(__('larke-admin::passport.password_error'), ResponseCode::LOGIN_ERROR);
        }

        // 取出 RSA 缓存ID
        $prikeyCacheKey = substr($password, 0, 16);

        // 原始密码
        $password = substr($password, 16);

        // 解出密码
        $password = base64_decode($password);
        if (empty($password)) {
            return $this->error(__('larke-admin::passport.password_error'), ResponseCode::LOGIN_ERROR);
        }

        try {
            // 私钥
            $prikey = Cache::get($prikeyCacheKey);

            // 导入私钥
            $rsakey = PublicKeyLoader::load($prikey);

            // RSA 解出密码
            $password = $rsakey->withPadding(RSA::ENCRYPTION_PKCS1)
                ->decrypt($password);
        } catch (\Exception $e) {
            do_action('passport_login_key_error', $e->getMessage());

            return $this->error(__('larke-admin::passport.password_error'), ResponseCode::LOGIN_ERROR);
        }
        $encryptPassword = AdminModel::checkPassword($adminInfo, $password);
        if (!$encryptPassword) {
            do_action('passport_login_password_error', $admin);

            return $this->error(__('larke-admin::passport.password_check_fail'), ResponseCode::LOGIN_ERROR);
        }

        if ($adminInfo['status'] != 1) {
            do_action('passport_login_inactive', $admin);

            return $this->error(__('larke-admin::passport.passport_disabled_or_not_exists'), ResponseCode::LOGIN_ERROR);
        }

        try {
            $sessionId = Str::uuid()->toString();
            LarkeAdminSession::create([
                'session_id' => $sessionId,
                'admin_id' => $adminInfo['id'],
            ]);
            // 生成 accessToken
            $accessToken = app('larke-admin.auth-token')
                ->buildAccessToken([
                    'adminid' => $adminInfo['id'],
                    'session_id' => $sessionId,
                ]);
        } catch (\Exception $e) {
            do_action('passport_login_access_token_error', $e->getMessage());

            return $this->error(__('larke-admin::passport.login_fail'), ResponseCode::LOGIN_ERROR);
        }

        if (empty($accessToken)) {
            return $this->error(__('larke-admin::passport.login_fail'), ResponseCode::LOGIN_ERROR);
        }

        try {
            // 刷新token
            $refreshToken = app('larke-admin.auth-token')
                ->buildRefreshToken([
                    'adminid' => $adminInfo['id'],
                    'session_id' => $sessionId,
                ]);
        } catch (\Exception $e) {
            do_action('passport_login_refresh_token_error', $e->getMessage());

            return $this->error($e->getMessage(), ResponseCode::LOGIN_ERROR);
        }

        if (empty($refreshToken)) {
            return $this->error(__('larke-admin::passport.login_fail'), ResponseCode::LOGIN_ERROR);
        }

        // 清空 RSA 缓存
        Cache::forget($prikeyCacheKey);

        // 过期时间
        $expiresIn = app('larke-admin.auth-token')->getAccessTokenExpiresIn();
        if (!config('larkeadmin.two_fa.enabled')) {
            $verified_two_fa = true;
        } else {
            $verified_two_fa = $adminInfo['two_fa_verified'] == 1;
        }

        // 返回数据
        $data = [
            'access_token' => $accessToken,
            'expires_in' => $expiresIn,
            'refresh_token' => $refreshToken,
            'verified_two_fa' => $verified_two_fa,
            'registered_two_fa' => !empty($adminInfo['two_fa_secret']),
        ];
        activity('login')->causedBy($admin)->log('admin_login');
        // 监听事件
        do_action('passport_login_after', $admin, $data);

        return $this->success(__('larke-admin::passport.login_success'), $data);
    }

    /**
     * 退出
     *
     * @return Response
     */
    #[RouteRule(
        title: '退出',
        desc: '账号退出',
        order: 96,
        auth: true
    )]
    public function logout(Request $request)
    {
        $refreshToken = $request->input('refresh_token');
        if (empty($refreshToken)) {
            return $this->error(__('larke-admin::passport.refresh_token_dont_empty'), ResponseCode::LOGOUT_ERROR);
        }

        if (app('larke-admin.cache')->has(md5($refreshToken))) {
            return $this->error(__('larke-admin::passport.refresh_token_timeout'), ResponseCode::LOGOUT_ERROR);
        }

        try {
            // 刷新Token
            $decodeRefreshToken = app('larke-admin.auth-token')
                ->decodeRefreshToken($refreshToken);
            // 验证
            app('larke-admin.auth-token')->validate($decodeRefreshToken);

            // 签名
            app('larke-admin.auth-token')->verify($decodeRefreshToken);

            $refreshAdminid = $decodeRefreshToken->getData('adminid');

            // 过期时间
            $refreshTokenExpiresIn = $decodeRefreshToken->getClaim('exp')->getTimestamp() - $decodeRefreshToken->getClaim('iat')->getTimestamp();
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), ResponseCode::LOGOUT_ERROR);
        }

        $accessAdminid = app('larke-admin.auth-admin')->getId();
        $sessionId = app('larke-admin.auth-admin')->getSessionId();
        if ($accessAdminid != $refreshAdminid) {
            return $this->error(__('larke-admin::passport.logout_fail'), ResponseCode::LOGOUT_ERROR);
        }

        $accessToken = app('larke-admin.auth-admin')->getAccessToken();

        // 添加缓存黑名单
        app('larke-admin.cache')->add(md5($accessToken), time(), $refreshTokenExpiresIn);
        app('larke-admin.cache')->add(md5($refreshToken), time(), $refreshTokenExpiresIn);
        LarkeAdminSession::where('admin_id', $accessAdminid)
            ->where('session_id', $sessionId)
            ->delete();
        // 监听事件
        do_action('passport_logout_after', [
            'access_token' => $accessToken,
            'expires_in' => $refreshTokenExpiresIn,
            'refresh_token' => $refreshToken,
        ]);

        return $this->success(__('larke-admin::passport.logout_success'));
    }

    #[RouteRule(
        title: '刷新token',
        desc: '刷新token',
        order: 97,
        auth: false
    )]
    public function refreshToken(Request $request)
    {
        $refreshToken = $request->input('refresh_token');
        if (empty($refreshToken)) {
            return $this->error(__('larke-admin::passport.refresh_token_dont_empty'), ResponseCode::REFRESH_TOKEN_ERROR);
        }

        try {
            // 旧的刷新token
            $decodeRefreshToken = app('larke-admin.auth-token')
                ->decodeRefreshToken($refreshToken);
            $refreshAdminid = $decodeRefreshToken->getData('adminid');
            $sessionId = $decodeRefreshToken->getData('session_id');
            if (empty($sessionId)) {
                return $this->error(__('larke-admin::passport.refresh_token_timeout'), ResponseCode::REFRESH_TOKEN_ERROR);
            }
            if (app('larke-admin.cache')->has(md5($refreshToken))) {
                LarkeAdminSession::where('admin_id', $refreshAdminid)
                    ->where('session_id', $sessionId)
                    ->delete();

                return $this->error(__('larke-admin::passport.refresh_token_timeout'), ResponseCode::REFRESH_TOKEN_ERROR);
            }
            // 验证
            app('larke-admin.auth-token')->validate($decodeRefreshToken);

            // 签名
            app('larke-admin.auth-token')->verify($decodeRefreshToken);

            // 单点登陆处理
            $loginType = config('larkeadmin.passport.login_type', 'many');
            if ($loginType == 'single') {
                $iat = $decodeRefreshToken->getClaim('iat')->getTimestamp();

                // 账号信息
                $adminInfo = AdminModel::where('id', $refreshAdminid)
                    ->first();
                if (empty($adminInfo)) {
                    return $this->error(__('larke-admin::auth.passport_error'), ResponseCode::REFRESH_TOKEN_ERROR);
                }

                // 账号信息
                $adminInfo = $adminInfo->toArray();

                // 判断是否是单端登陆
                if ($adminInfo['last_active'] != $iat) {
                    return $this->error(__('larke-admin::auth.refresh_token_fail'), ResponseCode::REFRESH_TOKEN_ERROR);
                }
            }
            // 新建access_token
            $newAccessToken = app('larke-admin.auth-token')
                ->buildAccessToken([
                    'adminid' => $refreshAdminid,
                    'session_id' => $sessionId,
                ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), ResponseCode::REFRESH_TOKEN_ERROR);
        }

        if (empty($newAccessToken)) {
            return $this->error(__('larke-admin::auth.refresh_token_fail'), ResponseCode::REFRESH_TOKEN_ERROR);
        }

        // 过期时间
        $expiresIn = app('larke-admin.auth-token')->getAccessTokenExpiresIn();

        // 返回数据
        $data = [
            'access_token' => $newAccessToken,
            'expires_in' => $expiresIn,
        ];

        // 监听事件
        do_action('passport_refresh_token_after', $data);

        return $this->success(__('larke-admin::auth.refresh_token_success'), $data);
    }
}
