<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ActivityCreateRequest;
use App\Http\Requests\ActivityDeleteRequest;
use App\Http\Requests\ActivityListRequest;
use App\Http\Requests\ActivityToggleStatusRequest;
use App\Http\Requests\ActivityUpdateRequest;
use App\Services\ActivityService;
use App\Utils\Gmt8;
use Illuminate\Http\Response;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}activities',
    title: 'Activities management',
    desc: 'Activities management',
    order: 11000,
    auth: true,
)]
class ActivityController extends Controller
{
    private ActivityService $activityService;

    public function __construct(ActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'activities management export',
        desc: 'activities management export',
        order: 11001,
        auth: true
    )]
    public function index(ActivityListRequest $request): Response
    {
        $params = $request->validated();
        $result = $this->activityService->index($params);

        return $this->success(__('获取成功'), $result);
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities management create',
        desc: 'Refund activities management create',
        order: 11002,
        auth: true
    )]
    public function create(ActivityCreateRequest $request): Response
    {
        $params = $request->validated();
        $params['start_date'] = Gmt8::toUtc($params['start_date']);
        if (!empty($params['end_date'])) {
            $params['end_date'] = Gmt8::toUtc($params['end_date']);
        }
        $result = $this->activityService->create($params);
        if ($result) {
            return $this->success(__('信息添加成功'));
        }

        return $this->error(__('信息添加失败'));
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities management update',
        desc: 'Refund activities management update',
        order: 11004,
        auth: true
    )]
    public function update(int $id, ActivityUpdateRequest $request): Response
    {
        $params = $request->validated();
        $params['start_date'] = Gmt8::toUtc($params['start_date']);
        if (!empty($params['end_date'])) {
            $params['end_date'] = Gmt8::toUtc($params['end_date']);
        }
        $result = $this->activityService->update($id, $params);
        if ($result) {
            return $this->success(__('信息修改成功'));
        }

        return $this->error(__('信息修改失败'));
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities management delete',
        desc: 'Refund activities management delete',
        order: 11005,
        auth: true
    )]
    public function delete(int $id, ActivityDeleteRequest $request): Response
    {
        $result = $this->activityService->delete($id);
        if ($result) {
            return $this->success(__('数据删除成功'));
        }

        return $this->error(__('数据删除失败'));
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities management detail',
        desc: 'Refund activities management detail',
        order: 11003,
        auth: true
    )]
    public function detail(int $id): Response
    {
        $info = $this->activityService->detail($id);
        if (empty($info)) {
            return $this->error(__('数据不存在'));
        }

        return $this->success(__('获取成功'), $info);
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities toggle status',
        desc: 'Refund activities toggle status',
        order: 11006,
        auth: true
    )]
    public function toggleStatus(int $id, ActivityToggleStatusRequest $request): Response
    {
        $result = $this->activityService->toggleStatus($id);
        if ($result) {
            return $this->success(__('状态修改成功'));
        }

        return $this->error(__('状态修改失败'));
    }

    #[RouteRule(
        parent: '{prefix}activities',
        title: 'Refund activities new year activity summary',
        desc: 'Refund activities new year activity summary',
        order: 11007,
        auth: true
    )]
    public function newYearActivitySummary(): Response
    {
        $result = $this->activityService->newYearActivitySummary();

        return $this->success(__('获取成功'), $result);
    }
}
