<?php

namespace App\Admin\Http\Controllers;

use App\Constants\OrderStatus;
use App\Constants\RefundStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateRefundStatus;
use App\Models\Order;
use App\Models\Refund;
use App\Models\RefundHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}refunds',
    title: 'Manage User Refunds',
    desc: 'Manage User Refunds',
    order: 115,
    auth: true
)]
class RefundController extends Controller
{
    #[RouteRule(
        parent: '{prefix}refunds',
        title: 'List Refunds',
        desc: 'List Refunds',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        return $this->success(__('messages.success'), Refund::with(['user', 'order'])->paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}refunds',
        title: 'Get User Refunds',
        desc: 'get User Refunds',
        order: 9921,
        auth: true
    )]
    public function show(Refund $refund, Request $request)
    {
        return $this->success(__('messages.success'), [
            'refund' => $refund->load(['user', 'order.product', 'bank', 'histories.processUser']),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}refunds',
        title: 'Update User Refunds',
        desc: 'Update User Refunds',
        order: 9921,
        auth: true
    )]
    public function update(Refund $refund, UpdateRefundStatus $request)
    {
        return Cache::lock('refund-processing-' . $refund->id, 5)->get(function () use ($refund, $request) {
            if ($refund->status > RefundStatus::DocumentVerifying->value) {
                return $this->success(__('messages.success'));
            }

            if ($refund->status == 0 && $request->status !== 1) {
                return $this->error(__('messages.reload'), 5, ['reload' => true]);
            }
            if ($refund->status > $request->status) {
                return $this->error(__('messages.reload'), 5, ['reload' => true]);
            }
            if ($refund->status == $request->status) {
                return $this->success(__('messages.success'));
            }
            $validated = $request->validated();
            $note = null;
            if (isset($validated['note'])) {
                $note = $validated['note'];
                unset($validated['note']);
            }
            DB::beginTransaction();
            try {
                $refund->update($validated);
                // add history
                RefundHistory::create([
                    'refund_id' => $refund->id,
                    'processed_by' => Auth::id(),
                    'status' => $request->status,
                    'note' => $note,
                ]);
                if ($request->status == RefundStatus::Declined->value) {
                    Order::where('id', $refund->order_id)->where('user_id', $refund->user_id)->where('status', OrderStatus::RefundRequesting)->update([
                        'status' => OrderStatus::Processed,
                    ]);
                }
                if ($request->status == RefundStatus::Approved->value) {
                    Order::where('id', $refund->order_id)->where('user_id', $refund->user_id)->update([
                        'status' => OrderStatus::Refunded,
                    ]);
                }
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getMessage());
            }

            return $this->success(__('messages.success'));
        });
    }

    #[RouteRule(
        parent: '{prefix}refunds',
        title: 'Delete User Refunds',
        desc: 'Delete User Refunds',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }
}
