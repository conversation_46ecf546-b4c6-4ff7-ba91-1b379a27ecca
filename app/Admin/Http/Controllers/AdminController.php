<?php

namespace App\Admin\Http\Controllers;

use App\Models\Admin as AdminModel;
use BaconQrCode\Renderer\Color\Rgb;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\RendererStyle\Fill;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Controller\Admin;
use Larke\Admin\Http\ResponseCode;
use Larke\Admin\Model\AuthGroupAccess as AuthGroupAccessModel;
use PragmaRX\Google2FA\Google2FA;

#[RouteRule(
    title: '管理员',
    desc: '系统管理员账号管理',
    order: 115,
    auth: true,
    slug: '{prefix}admin'
)]
class AdminController extends Admin
{
    #[RouteRule(
        parent: '{prefix}admin',
        title: 'Create Larke Admin',
        desc: 'Create Larke Admin',
        order: 9921,
        auth: true
    )]
    public function create(Request $request)
    {
        $data = $request->all();
        $validator = Validator::make($data, [
            'group_id' => 'required',
            'name' => 'required|min:2|max:20|unique:' . AdminModel::class,
            'nickname' => 'required|min:2|max:150',
            'email' => 'required|email|min:5|max:100|unique:' . AdminModel::class,
            'introduce' => 'required|max:500',
            'status' => 'required',
        ], [
            'group_id.required' => __('larke-admin::admin.group_dont_empty'),
            'name.required' => __('larke-admin::admin.passport_dont_empty'),
            'name.unique' => __('larke-admin::admin.passport_exists'),
            'name.min' => __('larke-admin::admin.name_min'),
            'name.max' => __('larke-admin::admin.name_max'),
            'nickname.required' => __('larke-admin::admin.nickname_dont_empty'),
            'nickname.min' => __('larke-admin::admin.nickname_min'),
            'nickname.max' => __('larke-admin::admin.nickname_max'),
            'email.required' => __('larke-admin::admin.email_dont_empty'),
            'email.email' => __('larke-admin::admin.email_error'),
            'email.unique' => __('larke-admin::admin.email_exists'),
            'email.min' => __('larke-admin::admin.email_min'),
            'email.max' => __('larke-admin::admin.email_max'),
            'introduce.required' => __('larke-admin::admin.introduce_dont_empty'),
            'introduce.max' => __('larke-admin::admin.introduce_max'),
            'status.required' => __('larke-admin::admin.status_dont_empty'),
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $insertData = [
            'name' => $data['name'],
            'nickname' => $data['nickname'],
            'email' => $data['email'],
            'introduce' => $data['introduce'],
            'status' => ($data['status'] == 1) ? 1 : 0,
        ];
        if (!empty($data['avatar'])) {
            $validatorAvatar = Validator::make([
                'avatar' => $data['avatar'],
            ], [
                'avatar' => 'required|size:36',
            ], [
                'avatar.required' => __('larke-admin::admin.avatar_dont_empty'),
                'avatar.size' => __('larke-admin::admin.avatar_error'),
            ]);

            if ($validatorAvatar->fails()) {
                return $this->error($validatorAvatar->errors()->first());
            }

            $insertData['avatar'] = $data['avatar'];
        }

        $admin = AdminModel::create($insertData);

        if ($admin === false) {
            return $this->error(__('larke-admin::admin.create_passport_fail'));
        }

        // 添加关联
        AuthGroupAccessModel::create([
            'id' => Str::uuid()->toString(),
            'admin_id' => $admin->id,
            'group_id' => $data['group_id'],
        ]);

        return $this->success(__('larke-admin::admin.create_passport_success'), [
            'id' => $admin->id,
        ]);
    }

    #[RouteRule(
        parent: '{prefix}admin',
        title: 'Get Larke Admin Detail',
        desc: 'Get Larke Admin Detail',
        order: 9921,
        auth: true
    )]
    public function detail(string $id)
    {
        if (empty($id)) {
            return $this->error(__('larke-admin::admin.userid_dont_empty'));
        }

        $info = AdminModel::withAccess()
            ->where('id', '=', $id)
            ->with(['groups'])
            ->select([
                'id',
                'name',
                'nickname',
                'email',
                'avatar',
                'introduce',
                'is_root',
                'status',
                'last_active',
                'last_ip',
                'create_time',
                'create_ip',
            ])
            ->first();
        if (empty($info)) {
            return $this->error(__('larke-admin::admin.user_not_exists'));
        }

        $adminGroups = $info['groups'];
        unset($info['groupAccesses'], $info['groups']);
        $info['groups'] = collect($adminGroups)
            ->map(function ($data) {
                return [
                    'id' => $data['id'],
                    'parentid' => $data['parentid'],
                    'title' => $data['title'],
                    'description' => $data['description'],
                ];
            });

        return $this->success(__('larke-admin::common.get_success'), $info);
    }

    #[RouteRule(
        parent: '{prefix}admin',
        title: 'Update Larke Admin',
        desc: 'Update Larke Admin',
        order: 9921,
        auth: true
    )]
    public function update(string $id, Request $request)
    {
        if (empty($id)) {
            return $this->error(__('larke-admin::admin.userid_dont_empty'));
        }

        $adminid = app('larke-admin.auth-admin')->getId();
        if ($id == $adminid) {
            return $this->error(__('larke-admin::admin.dont_update_passport'));
        }

        $adminInfo = AdminModel::withAccess()
            ->where('id', '=', $id)
            ->first();
        if (empty($adminInfo)) {
            return $this->error(__('larke-admin::admin.user_not_exists'));
        }

        $data = $request->all();
        $validator = Validator::make($data, [
            'name' => 'required|max:20',
            'nickname' => 'required|max:150',
            'email' => 'required|email|max:100',
            'introduce' => 'required|max:500',
            'status' => 'required',
        ], [
            'name.required' => __('larke-admin::admin.name_required'),
            'nickname.required' => __('larke-admin::admin.nickname_dont_empty'),
            'email.required' => __('larke-admin::admin.email_dont_empty'),
            'email.email' => __('larke-admin::admin.email_error'),
            'introduce.required' => __('larke-admin::admin.introduce_dont_empty'),
            'introduce.max' => __('larke-admin::admin.introduce_too_long'),
            'status.required' => __('larke-admin::admin.status_dont_empty'),
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $nameInfo = AdminModel::orWhere(function ($query) use ($id, $data) {
            $query->where('id', '!=', $id);
            $query->where('name', '=', $data['name']);
        })
            ->orWhere(function ($query) use ($id, $data) {
                $query->where('id', '!=', $id);
                $query->where('email', '=', $data['email']);
            })
            ->first();
        if (!empty($nameInfo)) {
            return $this->error(__('larke-admin::admin.name_or_email_exists'));
        }

        $updateData = [
            'name' => $data['name'],
            'nickname' => $data['nickname'],
            'email' => $data['email'],
            'introduce' => $data['introduce'],
            'status' => ($data['status'] == 1) ? 1 : 0,
        ];

        if (!empty($data['avatar'])) {
            $validatorAvatar = Validator::make([
                'avatar' => $data['avatar'],
            ], [
                'avatar' => 'required|size:36',
            ], [
                'avatar.required' => __('larke-admin::admin.avatar_dont_empty'),
                'avatar.size' => __('larke-admin::admin.avatar_error'),
            ]);

            if ($validatorAvatar->fails()) {
                return $this->error($validatorAvatar->errors()->first());
            }

            $updateData['avatar'] = $data['avatar'];
        }

        // 更新信息
        $status = $adminInfo->update($updateData);
        if ($status === false) {
            return $this->error(__('larke-admin::admin.update_fail'));
        }

        return $this->success(__('larke-admin::admin.update_success'));
    }

    #[RouteRule(
        parent: '{prefix}admin',
        title: 'Update Larke Admin',
        desc: 'Update Larke Admin',
        order: 9921,
        auth: false
    )]
    public function getTwoFaQr()
    {
        $google2fa = new Google2FA();
        /** @var \App\Models\Admin $user */
        $user = Auth::user();
        if (empty($user->two_fa_secret)) {
            $user->two_fa_secret = $google2fa->generateSecretKey();
            $user->save();
        }

        $qrUrl = $google2fa->getQRCodeUrl(
            $user->name,
            config('app.name'),
            $user->two_fa_secret
        );
        $svg = (new Writer(
            new ImageRenderer(
                new RendererStyle(192, 0, null, null, Fill::uniformColor(new Rgb(255, 255, 255), new Rgb(45, 55, 72))),
                new SvgImageBackEnd()
            )
        ))->writeString($qrUrl);

        return $this->success(__('larke-admin::admin.update_success'), [
            'qr' => substr($svg, strpos($svg, "\n") + 1),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}admin',
        title: 'Update Larke Admin',
        desc: 'Update Larke Admin',
        order: 9921,
        auth: false
    )]
    public function verifyTwoFa(Request $request)
    {
        $validated = $request->validate([
            'code' => 'digits:6',
        ]);
        $google2fa = new Google2FA();
        /** @var \App\Models\Admin $user */
        $user = Auth::user();
        $sessionId = app('larke-admin.auth-admin')->getSessionId();
        /** @var \App\Models\LarkeAdminSession $session */
        $session = $user->sessions()->where('session_id', $sessionId)->first();
        if (!$session) {
            return $this->error(__('larke-admin::auth.token_timeout'), ResponseCode::REFRESH_TOKEN_ERROR);
        }
        $valid = $google2fa->verifyKey($user->two_fa_secret, $validated['code']);
        if ($valid) {
            $session->two_fa_verified = 1;
            $session->save();

            return $this->success(__('larke-admin::admin.update_success'), true);
        }

        return $this->error(__('messages.two_fa.invalid_2fa_code', locale: 'zh_CN'), 422);
    }
}
