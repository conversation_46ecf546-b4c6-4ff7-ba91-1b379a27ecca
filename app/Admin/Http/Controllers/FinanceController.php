<?php

namespace App\Admin\Http\Controllers;

use App\Constants\TransactionType;
use App\Constants\WalletType;
use App\Http\Controllers\Controller;
use App\Services\FinanceService;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}finances',
    title: 'Manage Finance',
    desc: 'Manage Finance',
    order: 115,
    auth: true
)]
class FinanceController extends Controller
{
    public function __construct(protected FinanceService $service)
    {
        //
    }

    #[RouteRule(
        parent: '{prefix}finances',
        title: 'List Transaction',
        desc: 'Get List Transaction',
        order: 9921,
        auth: true
    )]
    public function getTransactions(Request $request)
    {
        $filters = $request->validate([
            'start_date' => 'date',
            'end_date' => 'date',
            'phone' => 'string|min:3',
            'name' => 'string',
            'transaction_type' => [Rule::in(TransactionType::cases())],
            'wallet_type' => [Rule::in(WalletType::cases())],
        ]);
        if ($request->has('start_date') & $request->has('end_date')) {
            $filters['start_date'] = Gmt8::toUtc($request->start_date);
            $filters['end_date'] = Gmt8::toUtc($request->end_date, true);
        }

        return $this->success(__('messages.success'), [
            'type' => TransactionType::cases(),
            'transactions' => $this->service->getTransactions(filter: $filters, pageSize: $request->get('pageSize', 10)),
        ]);
    }

    #[RouteRule(
        parent: '{prefix}finances',
        title: 'List Commissions',
        desc: 'Get List Commissions',
        order: 9921,
        auth: true
    )]
    public function getCommissions(Request $request)
    {
        $filters = $request->validate([
            'phone' => 'nullable|string',
            'name' => 'nullable|string',
            'start_date' => 'date',
            'end_date' => 'date',
        ]);
        if ($request->has('start_date') & $request->has('end_date')) {
            $filters['start_date'] = Gmt8::toUtc($request->start_date);
            $filters['end_date'] = Gmt8::toUtc($request->end_date, true);
        }

        return $this->success(__('messages.success'), $this->service->getCommissions(filter: $filters, pageSize: $request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}finances',
        title: 'List Rebate',
        desc: 'Get List Rebate',
        order: 9921,
        auth: true
    )]
    public function getRebate(Request $request)
    {
        $filters = $request->validate([
            'start_date' => 'date|nullable',
            'end_date' => 'date|nullable',
            'phone' => 'string|min:3|nullable',
            'name' => 'string|nullable',
        ]);
        if ($request->has('start_date') & $request->has('end_date')) {
            $filters['start_date'] = Gmt8::toUtc($request->start_date);
            $filters['end_date'] = Gmt8::toUtc($request->end_date, true);
        }

        return $this->success(__('messages.success'), $this->service->getRebate(filter: $filters, pageSize: $request->get('pageSize', 10)));
    }
}
