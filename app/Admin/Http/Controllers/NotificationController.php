<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationResource;
use App\Models\Notification;
use App\Services\UploadService;
use Illuminate\Http\Request;
use <PERSON>rke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}notifications',
    title: 'Manage Notifications',
    desc: 'Manage Notifications',
    order: 115,
    auth: true
)]
class NotificationController extends Controller
{
    private UploadService $uploadService;

    public function __construct()
    {
        $this->uploadService = new UploadService();
    }

    #[RouteRule(
        parent: '{prefix}notifications',
        title: 'List Notifications',
        desc: 'Get List Notifications',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $query = Notification::where('type', $request->input('type', 'login-qr'))
            ->whereNull('user_id') // prevent get verification type
            ->with('attachments:id,name,path,belong_type,belong_id,driver');
        if ($request->has('sortBy')) {
            $query->orderBy($request->sortBy, $request->input('sort', 'desc'));
        }

        return $this->success(__('messages.success'), NotificationResource::make($query->paginate($request->get('pageSize', 10))));
    }

    #[RouteRule(
        parent: '{prefix}notifications',
        title: 'Create Notifications',
        desc: 'Create New Notifications',
        order: 9921,
        auth: true
    )]
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required',
            'message' => 'required',
            'sort' => 'required',
            'for_official_community' => 'integer|required',
            'images' => 'array',
            'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
        ]);
        if (isset($validatedData['images'])) {
            $uploadedFiles = $validatedData['images'];
            unset($validatedData['images']);
        } else {
            $uploadedFiles = null;
        }

        $validatedData['type'] = 'login-qr';
        $validatedData['status'] = true;
        $notification = Notification::create($validatedData);
        if ($uploadedFiles) {
            foreach ($uploadedFiles as $file) {
                $this->uploadService->uploadFileToModel($file, $notification, true);
            }
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}notifications',
        title: 'update Notifications',
        desc: 'update New Notifications',
        order: 9921,
        auth: true
    )]
    public function update(Notification $notification, Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required',
            'message' => 'required',
            'sort' => 'required',
            'new_images' => 'array',
            'new_images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'images' => 'array',
            'images.*' => 'string|exists:larke_attachment,id',
            'for_official_community' => 'integer|required',
        ]);
        $uploadedFiles = null;
        if (isset($validatedData['new_images'])) {
            $uploadedFiles = $validatedData['new_images'];
        }
        unset($validatedData['new_images']);
        // clean up
        $listDeleteFile = $notification->attachments()->get()->pluck('path');
        foreach ($listDeleteFile as $file) {
            $this->uploadService->deleteFile($file);
        }
        $notification->attachments()->delete();
        unset($validatedData['images']);
        // update notification
        $notification->update($validatedData);
        if ($uploadedFiles) {
            foreach ($uploadedFiles as $file) {
                $this->uploadService->uploadFileToModel($file, $notification, true);
            }
        }

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}notifications',
        title: 'Delete Notifications',
        desc: 'Delete Notifications',
        order: 9921,
        auth: true
    )]
    public function destroy(Notification $notification, Request $request)
    {
        $attachments = $notification->attachments()->get()->pluck('path');
        foreach ($attachments as $file) {
            if ($file) {
                $this->uploadService->deleteFile($file);
            }
        }
        $notification->attachments()->delete();
        $notification->delete();

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}notifications',
        title: 'Delete Notifications',
        desc: 'Delete Notifications',
        order: 9921,
        auth: true
    )]
    public function updateNotificationStatus(Notification $notification, Request $request)
    {
        $validated = $request->validate([
            'status' => 'boolean|required',
        ]);
        $notification->update($validated);

        return $this->success(__('messages.success'));
    }
}
