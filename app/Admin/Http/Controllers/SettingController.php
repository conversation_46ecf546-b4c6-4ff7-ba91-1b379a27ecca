<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\UpdateSettingRequest;
use App\Models\SystemSetting;
use App\Utils\Gmt8;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}settings',
    title: 'Manage App Settings',
    desc: 'Manage App',
    order: 115,
    auth: true
)]
class SettingController extends Controller
{
    #[RouteRule(
        parent: '{prefix}settings',
        title: 'List Settings',
        desc: 'Get List Settings',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        $excludeSetting = [
        ];

        $settings = SystemSetting::whereNotIn('key', $excludeSetting)->get();

        return $this->success(__('messages.success'), $settings->pluck('value', 'key'));
    }

    #[RouteRule(
        parent: '{prefix}settings',
        title: 'Update Settings',
        desc: 'Update Settings',
        order: 9921,
        auth: true
    )]
    public function update(UpdateSettingRequest $request)
    {
        $validatedData = $request->validated();

        if ($request->has('not_allowed_phone_prefix')) {
            $validatedData['not_allowed_phone_prefix'] = str_replace(' ', '', trim($request->get('not_allowed_phone_prefix')));
        }
        $validatedData = $this->handleFiles($validatedData, $request);
        $validatedData = $this->convertToUtc($validatedData);

        $updateData = [];
        foreach ($validatedData as $key => $value) {
            if (!isset($value)) {
                $value = '';
            }
            $updateData[] = ['key' => $key, 'value' => $value];
        }
        SystemSetting::upsert($updateData, ['key']);

        return $this->success(__('messages.success'), []);
    }

    private function handleFiles($validatedData, $request): array
    {
        if ($request->has('app_logo')) {
            $validatedData['app_logo'] = Storage::disk('public')
                ->putFile('images', $request->file('app_logo'));
            $validatedData['app_logo'] = Storage::disk('public')->url($validatedData['app_logo']);
        }
        // temporary disable because client may change the idea
        //        if ($request->has('login_popup_qr')) {
        //            $validatedData['login_popup_qr'] = Storage::disk('public')
        //                ->putFile('images', $request->file('login_popup_qr'));
        //            $validatedData['login_popup_qr'] = Storage::disk('public')->url($validatedData['login_popup_qr']);
        //        }
        if ($request->has('profile_ads_banner')) {
            $validatedData['profile_ads_banner'] = Storage::disk('public')
                ->putFile('images', $request->file('profile_ads_banner'));
            $validatedData['profile_ads_banner'] = Storage::disk('public')->url($validatedData['profile_ads_banner']);
        }
        if ($request->has('app_banner')) {
            $validatedData['app_banner'] = Storage::disk('public')
                ->putFile('images', $request->file('app_banner'));
            $validatedData['app_banner'] = Storage::disk('public')->url($validatedData['app_banner']);
        }
        if ($request->has('float_icon')) {
            $validatedData['float_icon'] = Storage::disk('public')
                ->putFile('images', $request->file('float_icon'));
            $validatedData['float_icon'] = Storage::disk('public')->url($validatedData['float_icon']);
        }
        if ($request->has('cs_img')) {
            $validatedData['cs_img'] = Storage::disk('public')
                ->putFile('images', $request->file('cs_img'));
            $validatedData['cs_img'] = Storage::disk('public')->url($validatedData['cs_img']);
        }
        if ($request->has('share_product_banner')) {
            $validatedData['share_product_banner'] = Storage::disk('public')
                ->putFile('images', $request->file('share_product_banner'));
            $validatedData['share_product_banner'] = Storage::disk('public')->url($validatedData['share_product_banner']);
        }

        // Handle partner banners
        $fileSystem = Storage::disk('public');
        if ($request->has('elementary_partner_banner')) {
            $validatedData['elementary_partner_banner'] = $fileSystem
                ->putFile('images', $request->file('elementary_partner_banner'));
            $validatedData['elementary_partner_banner'] = $fileSystem->url($validatedData['elementary_partner_banner']);
        }

        if ($request->has('senior_partner_banner')) {
            $validatedData['senior_partner_banner'] = $fileSystem
                ->putFile('images', $request->file('senior_partner_banner'));
            $validatedData['senior_partner_banner'] = $fileSystem
                ->url($validatedData['senior_partner_banner']);
        }

        if ($request->has('premium_partner_banner')) {
            $validatedData['premium_partner_banner'] = $fileSystem
                ->putFile('images', $request->file('premium_partner_banner'));
            $validatedData['premium_partner_banner'] = $fileSystem->url($validatedData['premium_partner_banner']);
        }

        return $validatedData;
    }

    public function convertToUtc($params)
    {
        if (!empty($params['maintenance_from_date'])) {
            $params['maintenance_from_date'] = Gmt8::toUtc($params['maintenance_from_date']);
        }
        if (!empty($params['maintenance_to_date'])) {
            $params['maintenance_to_date'] = Gmt8::toUtc($params['maintenance_to_date']);
        }
        if (!empty($params['enable_delivery_service_start_time'])) {
            $params['enable_delivery_service_start_time'] = Gmt8::toUtc($params['enable_delivery_service_start_time']);
        }
        if (!empty($params['enable_delivery_service_end_time'])) {
            $params['enable_delivery_service_end_time'] = Gmt8::toUtc($params['enable_delivery_service_end_time']);
        }

        return $params;
    }
}
