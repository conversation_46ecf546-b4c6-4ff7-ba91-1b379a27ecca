<?php

namespace App\Admin\Http\Controllers;

use App\Models\Admin as AdminModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Larke\Admin\Annotation\RouteRule;
use Larke\Admin\Controller\Profile;

#[RouteRule(
    title: '个人信息',
    desc: '个人信息管理',
    order: 145,
    auth: true,
    slug: '{prefix}profile'
)]
class ProfileController extends Profile
{
    #[RouteRule(
        title: '修改我的信息',
        desc: '修改我的信息管理',
        order: 99,
        auth: true
    )]
    public function update(Request $request)
    {
        $data = $request->only(['nickname', 'email', 'introduce']);

        $validator = Validator::make($data, [
            'nickname' => 'required|max:150',
            'email' => 'required|email|max:100',
            'introduce' => 'required|max:500',
        ], [
            'nickname.required' => __('larke-admin::profile.nickname_required'),
            'nickname.max' => __('larke-admin::profile.nickname_max'),
            'email.required' => __('larke-admin::profile.email_required'),
            'email.email' => __('larke-admin::profile.email_error'),
            'email.max' => __('larke-admin::profile.email_max'),
            'introduce.required' => __('larke-admin::profile.introduce_required'),
            'introduce.max' => __('larke-admin::profile.introduce_max'),
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $updateData = [
            'nickname' => $data['nickname'],
            'email' => $data['email'],
            'introduce' => $data['introduce'],
        ];

        // 更新信息
        $adminid = app('larke-admin.auth-admin')->getId();
        $status = AdminModel::where('id', $adminid)
            ->update($updateData);
        if ($status === false) {
            return $this->error(__('larke-admin::profile.update_fail'));
        }

        return $this->success(__('larke-admin::profile.update_success'));
    }
}
