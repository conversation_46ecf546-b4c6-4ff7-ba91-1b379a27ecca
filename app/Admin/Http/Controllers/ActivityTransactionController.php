<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ActivityTransactionListRequest;
use App\Http\Requests\ActivityTransactionReportRequest;
use App\Services\ActivityTransactionService;
use Illuminate\Http\Response;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}activity_transactions',
    title: 'Activity transaction management',
    desc: 'Activity transaction management',
    order: 11000,
    auth: true,
)]
class ActivityTransactionController extends Controller
{
    public function __construct(private ActivityTransactionService $activityTransactionService)
    {
    }

    #[RouteRule(
        parent: '{prefix}activity_transactions',
        title: 'Activity transaction list',
        desc: 'Activity transaction list',
        order: 11001,
        auth: true
    )]
    public function index(ActivityTransactionListRequest $request): Response
    {
        $result = $this->activityTransactionService->index($request->validated());

        return $this->success(__('获取成功'), $result);
    }

    #[RouteRule(
        parent: '{prefix}activity_transactions',
        title: 'Activity transaction detail',
        desc: 'Activity transaction detail',
        order: 11002,
        auth: true
    )]
    public function detail(int $id): Response
    {
        $info = $this->activityTransactionService->detail($id);
        if ($info === null) {
            return $this->error(__('数据不存在'));
        }

        return $this->success(__('获取成功'), $info);
    }

    #[RouteRule(
        parent: '{prefix}activity_transactions',
        title: 'Activity report',
        desc: 'Activity report',
        order: 11002,
        auth: true
    )]
    public function report(ActivityTransactionReportRequest $request): Response
    {
        $result = $this->activityTransactionService->transactionReport($request->toArray());

        return $this->success(__('获取成功'), $result);
    }
}
