<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Reports\GlobalReportRequest;
use App\Models\User;
use App\Services\StatisticsService;
use App\Services\TeamReportService;
use App\Utils\Gmt8;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}reports',
    title: 'Global Report',
    desc: 'Global Report',
    order: 200,
    auth: true
)]
class GlobalController extends Controller
{
    #[RouteRule(
        parent: '{prefix}reports',
        title: 'Global Report',
        desc: 'Global Report',
        order: 1,
        auth: true
    )]
    public function index(GlobalReportRequest $request)
    {
        $filters = $request->validated();
        if (!empty($filters['start_date'])) {
            $filters['start_date'] = Gmt8::toUtc($filters['start_date']);
        }
        if (!empty($filters['end_date'])) {
            $filters['end_date'] = Gmt8::toUtc($filters['end_date'], true);
        }

        $statisticReport = app(StatisticsService::class)->getStatistics($filters);

        return $this->success(__('messages.success'), $statisticReport);
    }

    #[RouteRule(
        parent: '{prefix}reports',
        title: 'Wallet Summary',
        desc: 'Wallet Summary',
        order: 1,
        auth: true
    )]
    public function getWalletSummary(Request $request)
    {
        $filters = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
        ]);
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $filters['start_date'] = Gmt8::toUtc($filters['start_date']);
            $filters['end_date'] = Gmt8::toUtc($filters['end_date'], true);
        } else {
            $filters['start_date'] = Gmt8::toUtc(Gmt8::nowGmt8Instance()->startOfMonth());
            $filters['end_date'] = Gmt8::toUtc(Gmt8::nowGmt8Instance()->endOfMonth(), true);
        }
        $summary = App::make(StatisticsService::class)->getWalletSummary($filters);

        return $this->success(__('messages.success'), $summary);
    }

    /**
     * @throws BindingResolutionException
     */
    #[RouteRule(
        parent: '{prefix}reports',
        title: 'Wallet Summary',
        desc: 'Wallet Summary',
        order: 1,
        auth: true
    )]
    public function agentReport(Request $request)
    {
        $filters = $request->validate([
            'period' => 'string|in:today,yesterday,this_week,last_week,this_month,last_month',
            'test' => 'bool',
            'email' => 'required',
        ]);
        if (!empty($filters['period'])) {
            switch ($filters['period']) {
                case 'today':
                    $filters['start_date'] = Gmt8::today();
                    $filters['end_date'] = Gmt8::today();
                    break;
                case 'yesterday':
                    $filters['start_date'] = Gmt8::yestedday();
                    $filters['end_date'] = Gmt8::yestedday();
                    break;
                case 'this_week':
                    $filters['start_date'] = Gmt8::nowGmt8Instance()->startOfWeek()->format('Y-m-d');
                    $filters['end_date'] = Gmt8::nowGmt8Instance()->endOfWeek()->format('Y-m-d');
                    break;
                case 'last_week':
                    $filters['start_date'] = Gmt8::nowGmt8Instance()->subWeek()->startOfWeek()->format('Y-m-d');
                    $filters['end_date'] = Gmt8::nowGmt8Instance()->subWeek()->endOfWeek()->format('Y-m-d');
                    break;
                case 'this_month':
                    $filters['start_date'] = Gmt8::nowGmt8Instance()->startOfMonth()->format('Y-m-d');
                    $filters['end_date'] = Gmt8::nowGmt8Instance()->endOfMonth()->format('Y-m-d');
                    break;
                case 'last_month':
                    $filters['start_date'] = Gmt8::nowGmt8Instance()->subMonth()->startOfMonth()->format('Y-m-d');
                    $filters['end_date'] = Gmt8::nowGmt8Instance()->subMonth()->endOfMonth()->format('Y-m-d');
                    break;
            }
            $startTime = Gmt8::toUtc($filters['start_date']);
            $endTime = Gmt8::toUtc($filters['end_date'], true);
        } else {
            $startTime = Gmt8::toUtc(Gmt8::nowGmt8Instance()->startOfMonth());
            $endTime = Gmt8::toUtc(Gmt8::nowGmt8Instance()->endOfMonth(), true);
        }
        $user = User::where('email', $filters['email'])->first();
        if (!$user) {
            return $this->error(__('messages.user_not_found'));
        }
        $service = App::make(TeamReportService::class);
        $teamStats = $service->getTeamStats($user, $startTime, $endTime, $filters['period']);

        //        $directSubordinates = $service->getDirectSubordinateStats($user, $startTime, $endTime);

        return $this->success(__('messages.success'), $teamStats);
    }
}
