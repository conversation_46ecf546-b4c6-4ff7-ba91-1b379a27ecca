<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreatePaymentOrganizationRequest;
use App\Http\Requests\UpdatePaymentOrganizationRequest;
use App\Models\PaymentOrganization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}payment-organizations',
    title: 'Manage Payment Organization',
    desc: 'Manage Payment Organization',
    order: 115,
    auth: true
)]
class PaymentOrganizationController extends Controller
{
    #[RouteRule(
        parent: '{prefix}payment-organizations',
        title: 'List Payment Organization',
        desc: 'Get List Payment Organization',
        order: 9921,
        auth: true
    )]
    public function index(Request $request)
    {
        if ($request->has('full')) {
            return $this->success(__('messages.success'), PaymentOrganization::all(['id', 'name']));
        }

        return $this->success(__('messages.success'), PaymentOrganization::paginate($request->get('pageSize', 10)));
    }

    #[RouteRule(
        parent: '{prefix}payment-organizations',
        title: 'Create Payment Organization',
        desc: 'Create Payment Organization Info',
        order: 9921,
        auth: true
    )]
    public function store(CreatePaymentOrganizationRequest $request)
    {
        $validated = $request->validated();
        if ($request->has('logo')) {
            $validated['logo'] = Storage::disk('public')
                ->putFile('logos', $request->file('logo'));
            $validated['logo'] = Storage::disk('public')->url($validated['logo']);
        }
        PaymentOrganization::create($validated);

        return $this->success(__('messages.success'));
    }

    #[RouteRule(
        parent: '{prefix}payment-organizations',
        title: 'Update Payment Organization',
        desc: 'Update Payment Organization Info',
        order: 9921,
        auth: true
    )]
    public function update(PaymentOrganization $paymentOrganization, UpdatePaymentOrganizationRequest $request)
    {
        $validated = $request->validated();
        if ($request->has('logo')) {
            $validated['logo'] = Storage::disk('public')
                ->putFile('logos', $request->file('logo'));
            $validated['logo'] = Storage::disk('public')->url($validated['logo']);
        }

        if ($paymentOrganization->update($validated)) {
            return $this->success(__('messages.success'));
        }

        return $this->error(__('messages.failed'));
    }

    #[RouteRule(
        parent: '{prefix}payment-organizations',
        title: 'Delete Payment Organization',
        desc: 'Delete Payment Organization',
        order: 9921,
        auth: true
    )]
    public function destroy(Request $request)
    {
        //
    }
}
