<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\PromotionCardListRequest;
use App\Http\Requests\StorePromotionCardRequest;
use App\Http\Requests\UpdatePromotionCardRequest;
use App\Models\PromotionCard;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}promotion_card',
    title: 'Manage Promotion Card',
    desc: 'Manage Promotion Card',
    order: 115,
    auth: true
)]
class PromotionCardController extends Controller
{
    #[RouteRule(
        parent: '{prefix}promotion_card',
        title: 'List Promotion card',
        desc: 'Get List Promotion card',
        order: 9921,
        auth: true
    )]
    public function index(PromotionCardListRequest $request)
    {
        $params = $request->validated();
        $query = PromotionCard::query();

        // Apply filters
        if (!empty($params['status'])) {
            $query->where('status', $request->status);
        }

        if ($request->has('is_gift')) {
            $query->where('is_gift', $request->boolean('is_gift'));
        }

        $promotionCards = $query->paginate($request->input('per_page', 15));

        return $this->success(__('messages.success'), $promotionCards);
    }

    #[RouteRule(
        parent: '{prefix}promotion_card',
        title: 'Update Promotion card',
        desc: 'Update Promotion card Info',
        order: 9921,
        auth: true
    )]
    public function update(PromotionCard $promotionCard, UpdatePromotionCardRequest $request)
    {
        $updateData = $request->validated();
        $promotionCard->update($updateData);

        return $this->success(__('messages.success'), $promotionCard->refresh());
    }

    #[RouteRule(
        parent: '{prefix}promotion_card',
        title: 'Update Promotion card',
        desc: 'Update Promotion card Info',
        order: 9921,
        auth: true
    )]
    public function store(StorePromotionCardRequest $request)
    {
        $createData = $request->validated();

        return $this->success(__('messages.success'), PromotionCard::create($createData));
    }
}
