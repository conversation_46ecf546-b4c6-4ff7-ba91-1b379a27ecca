<?php

namespace App\Admin\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\SavingReportRequest;
use App\Services\ActivityService;
use Larke\Admin\Annotation\RouteRule;

#[RouteRule(
    slug: '{prefix}saving_reports',
    title: 'saving_reports',
    desc: 'saving_reports',
    order: 200,
    auth: true
)]
class SavingReportController extends Controller
{
    private ActivityService $activityService;

    public function __construct(
        ActivityService $activityService
    ) {
        $this->activityService = $activityService;
    }

    #[RouteRule(
        parent: '{prefix}saving_reports.list',
        title: 'saving_reports',
        desc: 'saving_reports',
        order: 1,
        auth: true
    )]
    public function index(SavingReportRequest $request)
    {
        $params = $request->validated();
        $activities = $this->activityService->savingLists($params);

        return $this->success(__('messages.success'), $activities);
    }

    #[RouteRule(
        parent: '{prefix}saving_reports.summary.lists',
        title: 'saving_reports_summary_lists',
        desc: 'saving_reports_summary_lists',
        order: 1,
        auth: true
    )]
    public function userSummaryLists(SavingReportRequest $request)
    {
        $params = $request->validated();
        $activities = $this->activityService->savingUserSummaryLists($params);

        return $this->success(__('messages.success'), $activities);
    }
}
