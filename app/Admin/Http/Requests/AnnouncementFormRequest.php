<?php

namespace App\Admin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AnnouncementFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Multilingual fields - can be either string (backward compatibility) or array of translations
            'title' => 'required|array|min:1',
            'title.*' => 'nullable|string|max:255',
            'message' => 'required|array|min:1',
            'message.*' => 'nullable|string',
            'button_text' => 'nullable|array',
            'button_text.*' => 'nullable|string|max:255',

            // Non-translatable fields
            'release_time' => [Rule::requiredIf(!$this->request->get('force_popup', false)), 'string', 'nullable'],
            'status' => 'required|integer',
            'user_phone' => 'nullable|string|exists:users,phone',
            'subordinate_type' => 'nullable|integer',
            'force_popup' => 'bool|required',
            'type' => 'nullable|string',
        ];
    }
}
