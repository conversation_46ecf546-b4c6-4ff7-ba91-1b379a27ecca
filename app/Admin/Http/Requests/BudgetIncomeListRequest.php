<?php

namespace App\Admin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class BudgetIncomeListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'page' => ['nullable', 'integer'],
            'pageSize' => ['nullable', 'integer'],
            'name' => ['nullable', 'string'],
            'group_id' => ['nullable', 'integer'],
            'budget_category_id' => ['nullable', 'integer'],
        ];
    }
}
