<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * VIP Level Reward Tracking
 *
 * @property int $id
 * @property int $user_id
 * @property int $vip_level_id
 * @property string|null $user_name
 * @property string|null $vip_level_name
 * @property float $reward_amount
 * @property string $transaction_code
 * @property \Illuminate\Support\Carbon $claimed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @property-read \App\Models\VipLevel $vipLevel
 */
class VipLevelReward extends Model
{
    protected $fillable = [
        'user_id',
        'vip_level_id',
        'user_name',
        'vip_level_name',
        'reward_amount',
        'transaction_code',
        'claimed_at',
    ];

    protected $casts = [
        'reward_amount' => 'decimal:10',
        'claimed_at' => 'datetime',
    ];

    /**
     * Get the user that owns this reward record
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the VIP level that this reward is for
     */
    public function vipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class);
    }

    /**
     * Check if a user has already claimed reward for a specific VIP level
     */
    public static function hasClaimedReward(int $userId, int $vipLevelId): bool
    {
        return static::where('user_id', $userId)
            ->where('vip_level_id', $vipLevelId)
            ->exists();
    }

    /**
     * Create a reward claim record
     */
    public static function createClaim(
        int $userId,
        int $vipLevelId,
        float $rewardAmount,
        string $transactionCode,
        ?string $userName = null,
        ?string $vipLevelName = null
    ): static {
        return static::create([
            'user_id' => $userId,
            'vip_level_id' => $vipLevelId,
            'user_name' => $userName,
            'vip_level_name' => $vipLevelName,
            'reward_amount' => $rewardAmount,
            'transaction_code' => $transactionCode,
            'claimed_at' => now(),
        ]);
    }
}
