<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * VIP Level Condition
 *
 * @property int $id
 * @property int $vip_level_id
 * @property float $remaining_balance_threshold
 * @property float $total_deposit_threshold
 * @property float $subordinates_balance_threshold
 * @property float $subordinates_deposit_threshold
 * @property int $subordinates_threshold
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\VipLevel $vipLevel
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelCondition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelCondition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelCondition query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelCondition whereVipLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelCondition whereIsActive($value)
 *
 * @mixin \Eloquent
 */
class VipLevelCondition extends Model
{
    protected $fillable = [
        'vip_level_id',
        'remaining_balance_threshold',
        'total_deposit_threshold',
        'subordinates_balance_threshold',
        'subordinates_deposit_threshold',
        'subordinates_threshold',
        'is_active',
    ];

    protected $casts = [
        'remaining_balance_threshold' => 'decimal:2',
        'total_deposit_threshold' => 'decimal:2',
        'subordinates_balance_threshold' => 'decimal:2',
        'subordinates_deposit_threshold' => 'decimal:2',
        'subordinates_threshold' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the VIP level that owns this condition
     */
    public function vipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class);
    }

    /**
     * Check if user metrics meet all threshold requirements for this VIP level
     */
    public function meetsAllConditions(
        float $userBalance,
        float $userTotalDeposit,
        float $subordinatesBalance,
        float $subordinatesTotalDeposit,
        int $subordinatesCount = 0
    ): bool {
        return $userBalance >= $this->remaining_balance_threshold &&
               $userTotalDeposit >= $this->total_deposit_threshold &&
               $subordinatesBalance >= $this->subordinates_balance_threshold &&
               $subordinatesTotalDeposit >= $this->subordinates_deposit_threshold &&
               $subordinatesCount >= $this->subordinates_threshold;
    }

    /**
     * Scope to get only active conditions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
