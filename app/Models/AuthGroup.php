<?php

namespace App\Models;

use Larke\Admin\Model\AuthGroup as BaseModel;

/*
 * AuthGroup
 *
 * @create 2020-10-20
 * <AUTHOR>
 */
/**
 * @property string $id 用户组id
 * @property string $parentid 父组别
 * @property string $title 用户组中文名称
 * @property string|null $description 描述信息
 * @property int|null $listorder 排序ID
 * @property int $is_system 1-系统默认角色
 * @property int $status 状态
 * @property int $update_time 更新时间
 * @property string $update_ip 修改IP
 * @property int $create_time 创建时间
 * @property string $create_ip 创建IP
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\Admin> $admins
 * @property-read int|null $admins_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AuthGroup> $childrenModule
 * @property-read int|null $children_module_count
 * @property-read \Larke\Admin\Model\AuthGroupAccess|null $groupAccess
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\AuthRuleAccess> $ruleAccesses
 * @property-read int|null $rule_accesses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\AuthRule> $rules
 * @property-read int|null $rules_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereCreateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereIsSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereListorder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereParentid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereUpdateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup whereUpdateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroup withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 */
class AuthGroup extends BaseModel
{
    //
}
