<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;

/**
 * IP Whitelist
 *
 * @property int $id
 * @property string $ip
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IpWhitelist whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class IpWhitelist extends Model
{
    use HasTimestamps;

    protected $fillable = [
        'ip',
        'type',
    ];
}
