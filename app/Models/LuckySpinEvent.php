<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * Lucky Spin Event
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property bool $is_enabled
 * @property Carbon|null $starts_at
 * @property Carbon|null $ends_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinReward> $rewards
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinAssignment> $assignments
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinUserChance> $userChances
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinResult> $results
 */
class LuckySpinEvent extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $fillable = [
        'name',
        'description',
        'is_enabled',
        'starts_at',
        'ends_at',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    public function rewards(): HasMany
    {
        return $this->hasMany(LuckySpinReward::class, 'event_id');
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(LuckySpinAssignment::class, 'event_id');
    }

    public function userChances(): HasMany
    {
        return $this->hasMany(LuckySpinUserChance::class, 'event_id');
    }

    public function results(): HasMany
    {
        return $this->hasMany(LuckySpinResult::class, 'event_id');
    }

    public function isActive(): bool
    {
        if (!$this->is_enabled) {
            return false;
        }

        $now = now();

        if ($this->starts_at && $now->lt($this->starts_at)) {
            return false;
        }

        if ($this->ends_at && $now->gt($this->ends_at)) {
            return false;
        }

        return true;
    }

    public function getActiveRewards()
    {
        return $this->rewards()->where('is_active', true)->get();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(function ($event) {
                return $event . ' ' . Str::replace('App\\Models\\', '', get_class($this)) . ' with name ' . $this->name;
            })
            ->useLogName('lucky_spin');
    }
}
