<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * User Bank
 *
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $account
 * @property string $account_name
 * @property int $active
 * @property string|null $mobile_number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereMobileNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserBank withoutTrashed()
 *
 * @mixin \Eloquent
 */
class UserBank extends Model
{
    use HasTimestamps;
    use SoftDeletes;

    /**
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'name',
        'account',
        'account_name',
        'active',
        'mobile_number',
        'deleted_at',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
