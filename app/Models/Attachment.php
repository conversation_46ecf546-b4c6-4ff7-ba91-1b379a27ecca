<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * Attachment
 *
 * @property string $id
 * @property string|null $belong_type 附件属于
 * @property string|null $belong_id 附件属于ID
 * @property string $name 文件名
 * @property string $path 文件路径
 * @property string $mime 文件mime类型
 * @property string $extension 文件类型
 * @property int $size 文件大小
 * @property string $md5 文件md5
 * @property string $sha1 sha1 散列值
 * @property string $driver 上传驱动
 * @property int $status 状态
 * @property int $update_time 更新时间
 * @property string $update_ip 修改IP
 * @property int $create_time 创建时间
 * @property string $create_ip 创建IP
 * @property-read mixed $url
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereBelongId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereBelongType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereCreateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMd5($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereSha1($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereUpdateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereUpdateTime($value)
 *
 * @mixin \Eloquent
 */
class Attachment extends Model
{
    protected $table = 'larke_attachment';

    protected $keyType = 'string';

    protected $primaryKey = 'id';

    protected $guarded = [];

    protected $appends = [
        'url',
    ];

    public $incrementing = false;

    public $timestamps = false;

    public function getUrlAttribute()
    {
        return Storage::url($this->path);
    }
}
