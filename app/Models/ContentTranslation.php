<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ContentTranslation extends Model
{
    protected $fillable = [
        'entity_type',
        'entity_id',
        'field_key',
        'locale',
        'content',
    ];

    public function entity(): MorphTo
    {
        return $this->morphTo();
    }

    public static function getTranslation(string $entityType, int $entityId, string $fieldKey, string $locale): ?string
    {
        return static::where('entity_type', $entityType)
            ->where('entity_id', $entityId)
            ->where('field_key', $fieldKey)
            ->where('locale', $locale)
            ->value('content');
    }

    public static function setTranslation(string $entityType, int $entityId, string $fieldKey, string $locale, string $content): void
    {
        static::updateOrCreate(
            [
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'field_key' => $fieldKey,
                'locale' => $locale,
            ],
            ['content' => $content]
        );
    }

    public static function deleteTranslations(string $entityType, int $entityId): void
    {
        static::where('entity_type', $entityType)
            ->where('entity_id', $entityId)
            ->delete();
    }

    public static function deleteFieldTranslations(string $entityType, int $entityId, string $fieldKey): void
    {
        static::where('entity_type', $entityType)
            ->where('entity_id', $entityId)
            ->where('field_key', $fieldKey)
            ->delete();
    }

    public static function getEntityTranslations(string $entityType, int $entityId): array
    {
        $translations = static::where('entity_type', $entityType)
            ->where('entity_id', $entityId)
            ->get(['field_key', 'locale', 'content']);

        $result = [];
        foreach ($translations as $translation) {
            $result[$translation->field_key][$translation->locale] = $translation->content;
        }

        return $result;
    }
}
