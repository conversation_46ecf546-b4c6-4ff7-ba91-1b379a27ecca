<?php

namespace App\Models;

use App\Constants\WithdrawalFeeType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @method static \Database\Factories\WithdrawSettingFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting query()
 *
 * @property int $id
 * @property string $withdrawal_week_days
 * @property string $withdrawal_customize_amount
 * @property string $withdrawal_min_amount_per_day
 * @property string $withdrawal_max_amount_per_day
 * @property string $withdrawal_fee
 * @property string $withdrawal_fee_charge_type
 * @property int $free_withdrawal_times
 * @property string $withdrawal_extra_fee_limit
 * @property string $withdrawal_extra_fee_percentage
 * @property int $daily_withdrawal_times
 * @property int $enabled
 * @property string $withdrawal_start_time
 * @property string $withdrawal_end_time
 * @property string $type
 * @property int|null $belongs_id
 * @property string|null $belongs_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereBelongsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereBelongsType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereDailyWithdrawalTimes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereFreeWithdrawalTimes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalCustomizeAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalExtraFeeLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalExtraFeePercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalFeeChargeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalMaxAmountPerDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalMinAmountPerDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalWeekDays($value)
 *
 * @property-read Model|\Eloquent|null $belongs
 * @property string $withdrawal_min_amount
 * @property string $withdrawal_max_amount
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalMaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereWithdrawalMinAmount($value)
 *
 * @property string $tax_rate
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawSetting whereTaxRate($value)
 *
 * @mixin \Eloquent
 */
class WithdrawSetting extends Model
{
    /** @use HasFactory<\Database\Factories\WithdrawSettingFactory> */
    use HasFactory;

    use LogsActivity;

    protected $fillable = [
        'withdrawal_week_days',
        'withdrawal_customize_amount',
        'withdrawal_min_amount',
        'withdrawal_max_amount',
        'withdrawal_max_amount_per_day',
        'withdrawal_fee',
        'withdrawal_fee_charge_type',
        'free_withdrawal_times',
        'withdrawal_extra_fee_limit',
        'withdrawal_extra_fee_percentage',
        'daily_withdrawal_times',
        'enabled',
        'withdrawal_start_time',
        'withdrawal_end_time',
        'type',
        'belongs_id',
        'belongs_type',
        'tax_rate',
    ];

    public function belongs(): MorphTo
    {
        return $this->morphTo();
    }

    public function getActivitylogOptions(): LogOptions
    {
        $relatedModel = $this->belongs;

        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(function ($event) use ($relatedModel) {
                return $event . ' withdraw settings to ' . Str::replace('App\\Models\\', '', get_class($relatedModel)) . ' with name ' . $relatedModel->name;
            })
            ->useLogName('withdraw_setting');
    }

    protected function casts(): array
    {
        return [
            'enabled' => 'boolean',
            'withdrawal_week_days' => 'json',
            'withdrawal_customize_amount' => 'json',
            'withdrawal_fee_charge_type' => WithdrawalFeeType::class,
        ];
    }
}
