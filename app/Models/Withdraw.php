<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Withdraw
 *
 * @property int $id
 * @property int $wallet_id
 * @property int $user_id
 * @property string $code
 * @property string $amount
 * @property string $fee
 * @property string $actual_amount
 * @property string $old_balance
 * @property string $new_balance
 * @property string|null $remark
 * @property int $status 0: pending, 1: processing, 2: approved, 3: rejected
 * @property string|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $user_bank_id
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\UserBank|null $userBank
 * @property-read \App\Models\Wallet|null $wallet
 * @property-read \App\Models\PaymentChannel|null $paymentChannel
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereActualAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereNewBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereOldBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereUserBankId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereWalletId($value)
 *
 * @property string|null $notes
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw whereNotes($value)
 *
 * @property int|null $payment_channel_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Withdraw wherePaymentChannelId($value)
 *
 * @mixin \Eloquent
 */
class Withdraw extends Model
{
    use HasFactory;
    use HasTimestamps;

    /**
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'wallet_id',
        'payment_channel_id',
        'user_bank_id',
        'code',
        'amount',
        'fee',
        'actual_amount',
        'old_balance',
        'new_balance',
        'remark',
        'status',
        'processed_at',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'metadata' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function userBank(): BelongsTo
    {
        return $this->belongsTo(UserBank::class);
    }

    public function paymentChannel(): BelongsTo
    {
        return $this->belongsTo(PaymentChannel::class);
    }
}
