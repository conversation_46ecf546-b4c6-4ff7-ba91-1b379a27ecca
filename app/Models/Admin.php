<?php

namespace App\Models;

/*
 * Admin 模型
 *
 * @create 2020-10-19
 * <AUTHOR>
 */

use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id ID
 * @property string $name 管理账号
 * @property string $password 管理密码
 * @property string $password_salt 加密因子
 * @property string $nickname 昵称
 * @property string|null $email
 * @property string|null $avatar 头像
 * @property string|null $introduce 简介
 * @property int $is_root 1-超级管理
 * @property int $status 状态
 * @property int $refresh_time 刷新时间
 * @property string $refresh_ip 刷新IP
 * @property int $last_active 最后登录时间
 * @property string $last_ip 最后登录IP
 * @property int $create_time 创建时间
 * @property string $create_ip 创建IP
 * @property string|null $two_fa_secret
 * @property int $two_fa_verified
 * @property int $two_fa_active
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\AuthGroupAccess> $groupAccesses
 * @property-read int|null $group_accesses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Larke\Admin\Model\AuthGroup> $groups
 * @property-read int|null $groups_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereCreateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereIntroduce($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereIsRoot($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereLastActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereLastIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wherePasswordSalt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereRefreshIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereRefreshTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTwoFaActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTwoFaSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin whereTwoFaVerified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withAccess(array $ids = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Admin withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 */
class Admin extends \Larke\Admin\Model\Admin
{
    // add relationship for current addmin
    public function getAuthIdentifier()
    {
        return $this->id;
    }

    public function sessions(): HasMany
    {
        return $this->hasMany(LarkeAdminSession::class, 'admin_id');
    }
}
