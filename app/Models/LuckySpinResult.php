<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Lucky Spin Result
 *
 * @property int $id
 * @property int $user_id
 * @property int $event_id
 * @property int $assignment_id
 * @property int $reward_id
 * @property string $spin_number
 * @property string $reward_source
 * @property float $reward_value
 * @property string $currency_code
 * @property int|null $transaction_id
 * @property bool $is_claimed
 * @property Carbon|null $claimed_at
 * @property Carbon|null $expires_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @property-read \App\Models\LuckySpinEvent $event
 * @property-read \App\Models\LuckySpinAssignment $assignment
 * @property-read \App\Models\LuckySpinReward $reward
 * @property-read \App\Models\TransactionLog|null $transaction
 */
class LuckySpinResult extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'assignment_id',
        'reward_id',
        'spin_number',
        'reward_source',
        'reward_value',
        'currency_code',
        'transaction_id',
        'is_claimed',
        'claimed_at',
        'expires_at',
    ];

    protected $casts = [
        'reward_value' => 'decimal:2',
        'is_claimed' => 'boolean',
        'claimed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(LuckySpinEvent::class, 'event_id');
    }

    public function assignment(): BelongsTo
    {
        return $this->belongsTo(LuckySpinAssignment::class, 'assignment_id');
    }

    public function reward(): BelongsTo
    {
        return $this->belongsTo(LuckySpinReward::class, 'reward_id');
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(TransactionLog::class, 'transaction_id');
    }

    public function isGuaranteed(): bool
    {
        return $this->reward_source === 'guaranteed';
    }

    public function isRandom(): bool
    {
        return $this->reward_source === 'random';
    }

    public function isExpired(): bool
    {
        return $this->expires_at && now()->gt($this->expires_at);
    }

    public function canBeClaimed(): bool
    {
        return !$this->is_claimed && !$this->isExpired();
    }

    public function markAsClaimed(int $transactionId): void
    {
        $this->update([
            'is_claimed' => true,
            'claimed_at' => now(),
            'transaction_id' => $transactionId,
        ]);
    }

    public function getFormattedValue(): string
    {
        return number_format($this->reward_value, 2) . ' ' . $this->currency_code;
    }

    public static function generateSpinNumber(int $userId, int $eventId): string
    {
        $timestamp = now()->format('Ymd_His');
        $random = mt_rand(1000, 9999);

        return "SPIN_{$userId}_{$eventId}_{$timestamp}_{$random}";
    }
}
