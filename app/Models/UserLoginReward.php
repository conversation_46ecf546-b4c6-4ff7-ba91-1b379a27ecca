<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * User Login Reward
 *
 * @property int $id
 * @property int $period
 * @property float $reward
 * @property bool $can_repeated
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward whereCanRepeated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward wherePeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLoginReward whereReward($value)
 *
 * @mixin \Eloquent
 */
class UserLoginReward extends Model
{
    protected $table = 'user_login_rewards';

    protected $fillable = [
        'period',
        'reward',
        'can_repeated',
    ];

    protected $casts = [
        'period' => 'integer',
        'reward' => 'float',
        'can_repeated' => 'boolean',
    ];
}
