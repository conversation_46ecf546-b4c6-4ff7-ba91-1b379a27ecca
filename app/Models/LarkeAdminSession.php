<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Query\Builder;

/**
 * @property int $id
 * @property string $session_id
 * @property string $admin_id
 * @property int $two_fa_verified
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession where2faVerified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession whereAdminId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession whereSessionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|LarkeAdminSession whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 * @mixin Builder
 */
class LarkeAdminSession extends Model
{
    protected $fillable = [
        'session_id',
        'admin_id',
        'two_fa_verified',
    ];

    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class);
    }
}
