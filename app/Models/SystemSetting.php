<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class SystemSetting
 *
 * @property int $id
 * @property string $key
 * @property string $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemSetting whereValue($value)
 *
 * @mixin \Eloquent
 */
class SystemSetting extends Model
{
    protected $table = 'system_settings';

    protected $fillable = ['key', 'value'];

    protected $jsonFields = [
        'general_team_leader',
        'silver_diamond_team_leader',
        'gold_diamond_team_leader',
        'star_diamond_team_leader',
        'partnership_commission_rates',
    ];

    public function getValueAttribute($value)
    {
        if (in_array($this->key, $this->jsonFields)) {
            return json_decode($value, true);
        }

        return $value;
    }
}
