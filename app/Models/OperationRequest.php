<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;

/**
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest query()
 *
 * @property int $id
 * @property int $user_id
 * @property int $store_id
 * @property int $order_quantity
 * @property string $order_profit
 * @property int $profit_percentage
 * @property string $profit
 * @property string $cost
 * @property int $status 0: pending, 10: delivering, 20: success
 * @property Carbon|null $approved_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereOrderProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereOrderQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereProfitPercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereUserId($value)
 *
 * @property-read \App\Models\Store|null $store
 * @property-read \App\Models\User|null $user
 * @property int $type 1: free, 2: extra_subordinate,3: paid
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OperationRequest whereType($value)
 *
 * @mixin Builder
 * @mixin \Eloquent
 */
class OperationRequest extends Model
{
    protected $fillable = [
        'user_id',
        'store_id',
        'order_quantity',
        'order_profit',
        'profit_percentage',
        'profit',
        'cost',
        'status',
        'approved_at',
        'type',
        'created_at',
        'updated_at',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
}
