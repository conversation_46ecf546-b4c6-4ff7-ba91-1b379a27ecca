<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Deposit Request
 *
 * @property int $id
 * @property string $provider
 * @property string $order_external_id Order external Id to integrate with provider
 * @property string $type
 * @property string $method
 * @property string $amount
 * @property string|null $currency
 * @property string|null $converted_amount
 * @property string|null $converted_currency
 * @property string|null $exchange_rate
 * @property \Illuminate\Support\Carbon|null $exchange_rate_time
 * @property string $status
 * @property int|null $user_id
 * @property int|null $wallet_id
 * @property int|null $payment_channel_id
 * @property string|null $client_ip
 * @property string|null $pay_url Provider pay url
 * @property string|null $pay_order_id Provider order id
 * @property int|null $pay_order_time Provider order time
 * @property string|null $callback_data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\Wallet|null $wallet
 * @property-read \App\Models\PaymentChannel|null $paymentChannel
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereCallbackData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereClientIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereOrderExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest wherePayOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest wherePayOrderTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest wherePayUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest wherePaymentChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereWalletId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereConvertedAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereConvertedCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DepositRequest whereExchangeRateTime($value)
 *
 * @mixin \Eloquent
 */
class DepositRequest extends Model
{
    protected $table = 'deposit_requests';

    protected $fillable = [
        'provider',
        'order_external_id',
        'type',
        'method',
        'amount',
        'currency',
        'converted_amount',
        'converted_currency',
        'exchange_rate',
        'exchange_rate_time',
        'status',
        'user_id',
        'wallet_id',
        'payment_channel_id',
        'client_ip',
        'pay_url',
        'pay_order_id',
        'pay_order_time',
        'callback_data',
        'remark',
    ];

    protected $casts = [
        'amount' => 'string',
        'converted_amount' => 'string',
        'exchange_rate' => 'string',
        'exchange_rate_time' => 'datetime',
        'callback_data' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function paymentChannel(): BelongsTo
    {
        return $this->belongsTo(PaymentChannel::class);
    }

    /**
     * Check if this deposit has conversion data
     */
    public function hasConversion(): bool
    {
        return !is_null($this->converted_amount) && !is_null($this->converted_currency);
    }

    /**
     * Get the effective amount (converted if available, otherwise original)
     */
    public function getEffectiveAmount(): string
    {
        return $this->hasConversion() ? $this->converted_amount : $this->amount;
    }

    /**
     * Get the effective currency (converted if available, otherwise original)
     */
    public function getEffectiveCurrency(): ?string
    {
        return $this->hasConversion() ? $this->converted_currency : $this->currency;
    }
}
