<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $activity_id
 * @property int $version
 * @property array|null $rates
 * @property string $start_date
 * @property string $end_date
 * @property int $status
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereActivityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereRates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityVersion whereVersion($value)
 *
 * @mixin \Eloquent
 */
class ActivityVersion extends Model
{
    protected $table = 'activity_versions';

    protected $fillable = [
        'activity_id',
        'version',
        'rates',
        'start_date',
        'end_date',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'rates' => 'array',
    ];
}
