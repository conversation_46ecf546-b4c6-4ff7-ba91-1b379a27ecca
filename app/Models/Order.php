<?php

namespace App\Models;

use App\Scopes\UserLocalScope;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $code
 * @property int $user_id
 * @property int|null $wallet_id
 * @property int $product_id
 * @property numeric $product_price
 * @property int $quantity
 * @property string $amount
 * @property string $fee
 * @property numeric $total_amount
 * @property int $status 0: pending, 1: processing, 2: processed, 3: cancelled
 * @property string $payment_type
 * @property string|null $integration_type
 * @property int|null $payment_channel_id
 * @property string|null $external_id
 * @property int|null $category_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\Wallet|null $wallet
 *
 * @method static Builder<static>|Order mine()
 * @method static Builder<static>|Order newModelQuery()
 * @method static Builder<static>|Order newQuery()
 * @method static Builder<static>|Order query()
 * @method static Builder<static>|Order whereAmount($value)
 * @method static Builder<static>|Order whereCode($value)
 * @method static Builder<static>|Order whereCreatedAt($value)
 * @method static Builder<static>|Order whereExternalId($value)
 * @method static Builder<static>|Order whereFee($value)
 * @method static Builder<static>|Order whereId($value)
 * @method static Builder<static>|Order whereIntegrationType($value)
 * @method static Builder<static>|Order wherePaymentChannelId($value)
 * @method static Builder<static>|Order wherePaymentType($value)
 * @method static Builder<static>|Order whereCategoryId($value)
 * @method static Builder<static>|Order whereProductId($value)
 * @method static Builder<static>|Order whereProductPrice($value)
 * @method static Builder<static>|Order whereQuantity($value)
 * @method static Builder<static>|Order whereStatus($value)
 * @method static Builder<static>|Order whereTotalAmount($value)
 * @method static Builder<static>|Order whereUpdatedAt($value)
 * @method static Builder<static>|Order whereUserId($value)
 * @method static Builder<static>|Order whereWalletId($value)
 *
 * @property array<array-key, mixed>|null $metadata
 * @property-read \App\Models\Category|null $category
 *
 * @method static Builder<static>|Order whereMetadata($value)
 *
 * @mixin \Eloquent
 */
class Order extends Model
{
    use Timestamp;
    use UserLocalScope;

    protected $fillable = [
        'code',
        'user_id',
        'wallet_id',
        'product_id',
        'product_price',
        'quantity',
        'amount',
        'fee',
        'total_amount',
        'status',
        'payment_type',
        'integration_type',
        'payment_channel_id',
        'external_id',
        'category_id',
        'metadata',
    ];

    protected $casts = [
        'product_price' => 'decimal:2',
        'discount' => 'decimal:2',
        'daily_return' => 'decimal:2',
        'metadata' => 'json',
    ];

    protected $hidden = ['metadata'];

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
