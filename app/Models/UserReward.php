<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserReward extends Model
{
    protected $table = 'user_rewards';

    protected $fillable = [
        'user_id',
        'milestone_id',
        'amount',
        'type',
        'reward_source',
        'auto_release',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function milestone()
    {
        return $this->belongsTo(RewardMilestone::class);
    }
}
