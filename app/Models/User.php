<?php

namespace App\Models;

use App\Constants\AccountType;
use App\Constants\AgentType;
use App\Constants\MilestoneType;
use App\Constants\OrderStatus;
use App\Constants\PartnerType;
use App\Constants\RewardSourceType;
use App\Traits\ManagesHierarchy;
use Database\Factories\UserFactory;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;

/**
 * User
 *
 * @property int $id
 * @property int|null $parent_id
 * @property string $name
 * @property string|null $email
 * @property string $phone
 * @property string $invitation_code
 * @property string|null $hierarchy exp: 1|2|
 * @property Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $nickname
 * @property int|null $sex 1: male, 2: female
 * @property string|null $avatar
 * @property string $level
 * @property string|null $id_card
 * @property string|null $id_name
 * @property bool $face_verified
 * @property Carbon|null $face_verified_at
 * @property bool $face_auth_bypass
 * @property int $active
 * @property bool $locked_deposit
 * @property bool $locked_withdraw
 * @property int $vip_level_id
 * @property string $total_deposit
 * @property PartnerType|null $partner_type
 * @property AgentType|null $agent_type
 * @property-read Collection<int, User> $children
 * @property-read int|null $children_count
 * @property-read Collection<int, \App\Models\Notification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \App\Models\TeamStatistic|null $teamStatistic
 * @property-read Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read Collection<int, \App\Models\Wallet> $wallets
 * @property-read int|null $wallets_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static Builder<static>|User newModelQuery()
 * @method static Builder<static>|User newQuery()
 * @method static Builder<static>|User query()
 * @method static Builder<static>|User whereActive($value)
 * @method static Builder<static>|User whereAvatar($value)
 * @method static Builder<static>|User whereCreatedAt($value)
 * @method static Builder<static>|User whereEmail($value)
 * @method static Builder<static>|User whereEmailVerifiedAt($value)
 * @method static Builder<static>|User whereId($value)
 * @method static Builder<static>|User whereIdCard($value)
 * @method static Builder<static>|User whereIdName($value)
 * @method static Builder<static>|User whereInvitationCode($value)
 * @method static Builder<static>|User whereHierarchy($value)
 * @method static Builder<static>|User whereLevel($value)
 * @method static Builder<static>|User whereLockedDeposit($value)
 * @method static Builder<static>|User whereLockedWithdraw($value)
 * @method static Builder<static>|User whereName($value)
 * @method static Builder<static>|User whereNickname($value)
 * @method static Builder<static>|User whereParentId($value)
 * @method static Builder<static>|User wherePassword($value)
 * @method static Builder<static>|User wherePhone($value)
 * @method static Builder<static>|User whereRememberToken($value)
 * @method static Builder<static>|User whereSex($value)
 * @method static Builder<static>|User whereTransactionPasscode($value)
 * @method static Builder<static>|User whereUpdatedAt($value)
 * @method static Builder<static>|User whereBypassFaceAuth($value)
 * @method static Builder<static>|User whereFaceVerified($value)
 * @method static Builder<static>|User whereFaceVerifiedAt($value)
 *
 * @property-read string $user_title
 * @property-read Collection<int, \App\Models\UserLog> $logs
 * @property-read int|null $logs_count
 * @property string $total_deposits
 *
 * @method static Builder<static>|User whereTotalDeposits($value)
 * @method static Builder<static>|User whereVipLevelId($value)
 *
 * @property int $operation_times
 * @property int $extra_operation_times
 * @property bool $qualification_rewarded
 * @property-read Collection<int, \App\Models\Store> $stores
 * @property-read int|null $stores_count
 * @property-read \App\Models\Wallet|null $generalWallet
 * @property-read \App\Models\TFactory|null $use_factory
 * @property-read User|null $parent
 * @property-read \App\Models\VipLevel|null $user_vip_level
 *
 * @method static Builder<static>|User whereOperationTimes($value)
 *
 * @mixin \Eloquent
 * @mixin Builder
 */
class User extends Authenticatable
{
    /** @use HasFactory<UserFactory> */
    use HasApiTokens;

    use HasFactory;
    use ManagesHierarchy;
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'parent_id',
        'transaction_passcode',
        'invitation_code',
        'hierarchy',
        'email_verified_at',
        'remember_token',
        'nickname',
        'sex',
        'avatar',
        'id_card',
        'id_name',
        'face_verified',
        'face_verified_at',
        'face_auth_bypass',
        'active',
        'locked_deposit',
        'locked_withdraw',
        'level',
        'agent_type',
        'partner_type',
        'vip_level_id',
        'group_id',
        'transaction_passcode_updated_at',
        'account_type',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'transaction_passcode',
        'vip_level_id',
        'hierarchy',
    ];

    /**
     * @return HasMany
     *
     * @deprecated
     */
    public function wallets()
    {
        return $this->hasMany(Wallet::class);
    }

    /**
     * @return HasOne
     */
    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    /**
     * @return HasMany
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * @return HasMany
     */
    public function children()
    {
        return $this->hasMany(User::class, 'parent_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo(__CLASS__, 'parent_id', 'id');
    }

    public function teamStatistic()
    {
        return $this->hasOne(TeamStatistic::class);
    }

    /**
     * Get team hierarchy levels with commission rates
     *
     * This function retrieves the team hierarchy and calculates commission rates
     * based on partner types and levels.
     *
     * @return array Array of hierarchy levels with user IDs and commission rates
     */
    public function getTeamHierarchyLevel(): array
    {
        // Early return if no team statistic or hierarchy
        if (!$this->teamStatistic?->hierarchy) {
            return [];
        }

        $result = [];
        $hierarchyString = $this->teamStatistic->hierarchy;
        $teamHierarchy = explode('|', rtrim($hierarchyString, '|'));
        foreach ($teamHierarchy as $index => $userId) {
            $level = $index + 1;
            $parent = self::find($userId);

            // Skip if parent not found
            if (!$parent) {
                continue;
            }

            // Check if this level is eligible for commission based on partner type
            if ($this->isEligibleForCommission($parent, $level)) {
                $result[] = [
                    'user_id' => $userId,
                    'level' => $level,
                    'rate' => $this->getCommissionRate($parent->partner_type, $level),
                ];
            }
        }

        return $result;
    }

    public function logs(): HasMany
    {
        return $this->hasMany(UserLog::class);
    }

    public function maskUsername(): string
    {
        $username = $this->id_name;
        if (!$username || strlen($username) < 1) {
            return '';
        }

        $len = mb_strlen($username);
        $first = mb_substr($username, 0, 1);
        $last = mb_substr($username, -1);

        return match ($len) {
            1 => '*',
            2 => $first . '*',
            3 => $first . '*' . mb_strtoupper($last),
            default => $first . str_repeat('*', max(1, $len - 2)) . $last
        };
    }

    public function maskPhone(): string
    {
        return substr_replace($this->phone, '****', 3, 4);
    }

    public function maskInfo(): string
    {
        if ($this->id_name) {
            return $this->maskUsername();
        }

        return $this->maskPhone();
    }

    public function markInfos()
    {
        $this->id_name = $this->maskUsername();
        $this->phone = $this->maskPhone();
    }

    public function getVipLevelAttribute(): ?VipLevel
    {
        return VipLevel::find($this->vip_level_id);
    }

    public function generalWallet()
    {
        return $this->hasOne(Wallet::class)->where('type', 'general');
    }

    public function stores()
    {
        return $this->hasMany(Store::class);
    }

    public function verifiedReferrals(): HasMany
    {
        return $this->children()->whereNotNull('id_card');
    }

    public function rewards(): HasMany
    {
        return $this->hasMany(UserReward::class);
    }

    public function baseReferralRewards(): HasMany
    {
        return $this->rewards()->where('type', MilestoneType::Default->value)->where('reward_source', RewardSourceType::Referral->value);
    }

    public function extraReferralRewards(): HasMany
    {
        return $this->rewards()->where('type', MilestoneType::Extra->value)->where('reward_source', RewardSourceType::Referral->value);
    }

    public function banks(): HasMany
    {
        return $this->hasMany(UserBank::class, 'user_id');
    }

    /**
     * Get the ice tokens for the user.
     */
    public function iTokens(): HasMany
    {
        return $this->hasMany(IToken::class);
    }

    /**
     * Get the active ice tokens for the user.
     */
    public function activeITokens(): HasMany
    {
        return $this->iTokens()->active();
    }

    /**
     * Get the contributions for the user.
     */
    public function contributes(): HasMany
    {
        return $this->hasMany(Contribute::class);
    }

    public function isAgent()
    {
        return $this->partner_type === PartnerType::Agent;
    }

    public function isPaidSecurityDeposit(): bool
    {
        return Order::query()->where('user_id', $this->id)->where('status', OrderStatus::Processed->value)->exists();
    }

    public function hasChildrenPaidSecurityDeposit(): bool
    {
        $userId = $this->id;

        return Order::query()
            ->join('users', function (JoinClause $joinClause) use ($userId) {
                $joinClause->on('users.id', '=', 'orders.user_id');
                $joinClause->where('users.parent_id', $userId);
            })
            ->where('orders.status', OrderStatus::Processed->value)->exists();
    }

    public function vipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class, 'vip_level_id');
    }

    public function vipLevelHistory(): HasMany
    {
        return $this->hasMany(VipLevelHistory::class, 'user_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'group_id');
    }

    /**
     * Scope to filter users by hierarchy depth
     */
    public function scopeWithHierarchyDepth(Builder $query, int $depth): Builder
    {
        if ($depth === 0) {
            return $query->where(function ($q) {
                $q->whereNull('hierarchy')->orWhere('hierarchy', '');
            });
        }

        return $query->whereRaw('CHAR_LENGTH(hierarchy) - CHAR_LENGTH(REPLACE(hierarchy, "|", "")) = ?', [$depth]);
    }

    /**
     * Scope to filter users by maximum hierarchy depth
     */
    public function scopeWithMaxHierarchyDepth(Builder $query, int $maxDepth): Builder
    {
        return $query->whereRaw('CHAR_LENGTH(COALESCE(hierarchy, "")) - CHAR_LENGTH(REPLACE(COALESCE(hierarchy, ""), "|", "")) <= ?', [$maxDepth]);
    }

    /**
     * Scope to get root users (no parent)
     */
    public function scopeRootUsers(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('parent_id')->orWhere('parent_id', 0);
        });
    }

    /**
     * Get all descendants using hierarchy service
     */
    public function getAllDescendants(int $maxLevels = 10): \Illuminate\Database\Eloquent\Collection
    {
        return app(\App\Services\UserHierarchyService::class)->getDescendants($this, $maxLevels);
    }

    /**
     * Get hierarchy tree for admin display
     */
    public function getHierarchyTreeForAdmin(int $maxDepth = 5): array
    {
        return app(\App\Services\UserHierarchyService::class)->getHierarchyTree($this, $maxDepth);
    }

    /**
     * Validate and optionally fix hierarchy
     *
     * @param bool $autoFix Whether to automatically fix invalid hierarchy
     * @return array Validation result
     */
    public function validateHierarchy(bool $autoFix = false): array
    {
        $service = app(\App\Services\UserHierarchyService::class);
        $validation = $service->validateUserHierarchy($this);

        if ($autoFix && $validation['needs_fix']) {
            $fixed = $service->fixUserHierarchy($this);
            $validation['auto_fixed'] = $fixed;

            if ($fixed) {
                $this->refresh(); // Reload the model with updated hierarchy
            }
        }

        return $validation;
    }

    /**
     * Update hierarchy based on current parent
     *
     * @return bool Success status
     */
    public function updateHierarchy(): bool
    {
        return app(\App\Services\UserHierarchyService::class)->updateUserHierarchy($this);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'face_verified_at' => 'datetime',
            'face_auth_bypass' => 'boolean',
            'locked_deposit' => 'boolean',
            'locked_withdraw' => 'boolean',
            'invitation_code' => 'string',
            'agent_type' => AgentType::class,
            'partner_type' => PartnerType::class,
            'vip_level_updated_at' => 'datetime',
            'vip_level_locked_until' => 'datetime',
            'last_vip_check_at' => 'datetime',
            'transaction_passcode_updated_at' => 'datetime',
            'account_type' => AccountType::class,
            'active' => 'boolean',
        ];
    }

    protected function serializeDate(DateTimeInterface $date): ?string
    {
        return \Carbon\Carbon::parse($date)
            ->setTimezone(config('app.timezone'))
            ->format('Y-m-d H:i:s');
    }

    protected function scopeActive(Builder $query): Builder
    {
        return $query->where('active', 1);
    }

    public function subordinates(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            UserHierarchy::class,
            'ancestor_id',
            'id',
            'id',
            'descendant_id'
        );
    }
}
