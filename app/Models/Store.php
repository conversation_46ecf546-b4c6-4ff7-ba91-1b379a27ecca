<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Str;

/**
 * @property int $id
 * @property int $user_id
 * @property string|null $serial_number
 * @property float $cost
 * @property float $remaining_cost
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereRemainingCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereUserId($value)
 *
 * @property int $type 1:free,2:paid
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereType($value)
 *
 * @property string $daily_profit
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereDailyProfit($value)
 *
 * @property int|null $product_id
 * @property int $release_times
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Store whereReleaseTimes($value)
 *
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\User|null $user
 *
 * @mixin \Eloquent
 * @mixin Builder
 */
class Store extends Model
{
    protected $table = 'stores';

    protected $fillable = [
        'user_id',
        'serial_number',
        'cost',
        'remaining_cost',
        'type',
        'daily_profit',
        'product_id',
        'release_times',
    ];

    protected $casts = [
        'cost' => 'decimal:2',
        'remaining_cost' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($store) {
            // Generate a unique serial number if not provided
            if (!$store->serial_number) {
                $store->serial_number = static::generateUniqueSerialNumber();
            }
        });
    }

    protected static function generateUniqueSerialNumber(): string
    {
        do {
            // Generate a serial number format (customize as needed)
            $serial = 'STR-' . strtoupper(Str::random(8));
        } while (static::where('serial_number', $serial)->exists());

        return $serial;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
