<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * VIP Level History
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $old_vip_level_id
 * @property int $new_vip_level_id
 * @property string $trigger_event
 * @property int|null $trigger_reference_id
 * @property string $action_type
 * @property float $calculated_points
 * @property float $user_balance
 * @property float $user_total_deposit
 * @property float $subordinates_balance
 * @property float $subordinates_total_deposit
 * @property int $subordinates_count
 * @property \Illuminate\Support\Carbon $created_at
 * @property-read \App\Models\User $user
 * @property-read \App\Models\VipLevel|null $oldVipLevel
 * @property-read \App\Models\VipLevel $newVipLevel
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory whereTriggerEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevelHistory whereCreatedAt($value)
 *
 * @mixin \Eloquent
 */
class VipLevelHistory extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'old_vip_level_id',
        'new_vip_level_id',
        'trigger_event',
        'trigger_reference_id',
        'action_type',
        'calculated_points',
        'user_balance',
        'user_total_deposit',
        'subordinates_balance',
        'subordinates_total_deposit',
        'subordinates_count',
    ];

    protected $casts = [
        'action_type' => 'string',
        'calculated_points' => 'decimal:2',
        'user_balance' => 'decimal:2',
        'user_total_deposit' => 'decimal:2',
        'subordinates_balance' => 'decimal:2',
        'subordinates_total_deposit' => 'decimal:2',
        'subordinates_count' => 'integer',
        'created_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function oldVipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class, 'old_vip_level_id');
    }

    public function newVipLevel(): BelongsTo
    {
        return $this->belongsTo(VipLevel::class, 'new_vip_level_id');
    }

    /**
     * Get the trigger reference model (Deposit, Withdraw, etc.)
     */
    public function triggerReference()
    {
        return match ($this->trigger_event) {
            'deposit' => $this->belongsTo(Deposit::class, 'trigger_reference_id'),
            'withdrawal' => $this->belongsTo(Withdraw::class, 'trigger_reference_id'),
            default => null,
        };
    }
}
