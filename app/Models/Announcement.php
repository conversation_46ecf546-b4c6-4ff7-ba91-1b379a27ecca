<?php

namespace App\Models;

use App\Traits\HasContentTranslations;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Announcement
 *
 * @property int $id
 * @property string $type
 * @property string $title
 * @property string $button_text
 * @property string|null $message
 * @property int|null $user_id
 * @property int $subordinate_type
 * @property int $status 0: disable,1 :enable
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $release_time
 * @property int $force_popup
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\Notification|null $notification
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Notification> $notifications
 * @property-read int|null $notifications_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereButtonText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereForcePopup($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereReleaseTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Announcement whereSubordinateType($value)
 *
 * @mixin \Eloquent
 */
class Announcement extends Model
{
    use HasContentTranslations;
    use Timestamp;

    protected $fillable = [
        'type',
        'title',
        'message',
        'user_id',
        'subordinate_type',
        'status',
        'button_text',
        'release_time',
        'force_popup',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function notification(): HasOne
    {
        return $this->hasOne(Notification::class, 'announcement_id');
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'announcement_id');
    }

    protected $translatable = [
        'title',
        'message',
        'button_text',
    ];

    public function getTitle(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('title', $locale);
    }

    public function getMessage(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('message', $locale);
    }

    public function getButtonText(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('button_text', $locale);
    }

    public function toArrayWithTranslations(): array
    {
        $data = $this->toArray();
        $data['translations'] = $this->getAllTranslations();

        return $data;
    }
}
