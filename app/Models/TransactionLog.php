<?php

namespace App\Models;

use App\Constants\TransactionType;
use App\Scopes\UserLocalScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Transaction Log
 *
 * @property int $id
 * @property int $wallet_id
 * @property int $user_id
 * @property string $code transaction code
 * @property string $type
 * @property numeric $amount
 * @property numeric $old_balance
 * @property numeric $new_balance
 * @property string $remark
 * @property string|null $reference
 * @property int|null $reference_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array|null $metadata
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\Wallet|null $wallet
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog mine()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereNewBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereOldBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TransactionLog whereWalletId($value)
 *
 * @mixin \Eloquent
 */
class TransactionLog extends Model
{
    use UserLocalScope;

    protected $fillable = [
        'wallet_id',
        'user_id',
        'code',
        'amount',
        'old_balance',
        'new_balance',
        'remark',
        'type',
        'reference',
        'reference_id',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'old_balance' => 'decimal:2',
        'new_balance' => 'decimal:2',
        'order_total_amount' => 'decimal:2',
        'metadata' => 'json',
    ];

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function reference(): BelongsTo
    {
        return $this->morphTo('references', 'reference', 'reference_id');
    }

    public function isReward(): bool
    {
        return in_array($this->type, [
            TransactionType::Activity_sign_in->value,
            TransactionType::Activity_register->value,
            TransactionType::Activity->value,
            TransactionType::Subordinate_verification_invited->value,
        ], true);
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'reference_id');
    }

    public function operationRequest(): BelongsTo
    {
        return $this->belongsTo(OperationRequest::class, 'reference_id');
    }

    public function userReward(): BelongsTo
    {
        return $this->belongsTo(UserReward::class, 'reference_id');
    }
}
