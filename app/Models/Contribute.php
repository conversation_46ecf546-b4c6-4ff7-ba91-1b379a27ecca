<?php

namespace App\Models;

use App\Constants\ContributeStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Contribute
 *
 * @property int $id
 * @property int $user_id
 * @property string $transaction_id
 * @property float $amount
 * @property float $profit
 * @property ContributeStatus $status
 * @property Carbon $expired_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User $user
 * @property-read \Illuminate\Database\Eloquent\Collection|IToken[] $iTokens
 * @property-read float $total_amount
 * @property-read string $formatted_amount
 * @property-read string $formatted_profit
 * @property-read string $status_text
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute valid()
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute invalid()
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute forUser($userId)
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute active()
 * @method static \Illuminate\Database\Eloquent\Builder|Contribute expired()
 *
 * @property-read int|null $i_tokens_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contribute query()
 *
 * @mixin \Eloquent
 */
class Contribute extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'transaction_id',
        'amount',
        'profit',
        'status',
        'expired_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'profit' => 'decimal:2',
        'status' => ContributeStatus::class,
        'expired_at' => 'datetime',
    ];

    /**
     * Get the user that owns the contribution.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the ice tokens associated with this contribution.
     */
    public function iTokens(): HasMany
    {
        return $this->hasMany(IToken::class);
    }

    /**
     * Scope a query to only include valid contributions.
     */
    public function scopeValid($query)
    {
        return $query->where('status', ContributeStatus::Valid);
    }

    /**
     * Scope a query to only include invalid contributions.
     */
    public function scopeInvalid($query)
    {
        return $query->where('status', ContributeStatus::Invalid);
    }

    /**
     * Scope a query to only include contributions for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include active (non-expired) contributions.
     */
    public function scopeActive($query)
    {
        return $query->where('expired_at', '>', now());
    }

    /**
     * Scope a query to only include expired contributions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expired_at', '<=', now());
    }

    /**
     * Check if the contribution is valid.
     */
    public function isValid(): bool
    {
        return $this->status === ContributeStatus::Valid;
    }

    /**
     * Check if the contribution is invalid.
     */
    public function isInvalid(): bool
    {
        return $this->status === ContributeStatus::Invalid;
    }

    /**
     * Check if the contribution is expired.
     */
    public function isExpired(): bool
    {
        return $this->expired_at->isPast();
    }

    /**
     * Check if the contribution is active (not expired).
     */
    public function isActive(): bool
    {
        return !$this->isExpired();
    }

    /**
     * Get the total amount including profit.
     */
    public function getTotalAmountAttribute(): float
    {
        return $this->amount + $this->profit;
    }

    /**
     * Get the formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }

    /**
     * Get the formatted profit with currency.
     */
    public function getFormattedProfitAttribute(): string
    {
        return number_format($this->profit, 2);
    }

    /**
     * Get the status text.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status->label();
    }

    /**
     * Get the remaining days until expiration.
     */
    public function getRemainingDays(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->expired_at);
    }

    /**
     * Mark the contribution as valid.
     */
    public function markAsValid(): bool
    {
        return $this->update(['status' => ContributeStatus::Valid]);
    }

    /**
     * Mark the contribution as invalid.
     */
    public function markAsInvalid(): bool
    {
        return $this->update(['status' => ContributeStatus::Invalid]);
    }
}
