<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $from
 * @property string $to
 * @property string $rate
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder<static>|ExchangeRate newModelQuery()
 * @method static Builder<static>|ExchangeRate newQuery()
 * @method static Builder<static>|ExchangeRate query()
 * @method static Builder<static>|ExchangeRate whereCreatedAt($value)
 * @method static Builder<static>|ExchangeRate whereFrom($value)
 * @method static Builder<static>|ExchangeRate whereId($value)
 * @method static Builder<static>|ExchangeRate whereRate($value)
 * @method static Builder<static>|ExchangeRate whereTo($value)
 * @method static Builder<static>|ExchangeRate whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ExchangeRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'from',
        'to',
        'rate',
        'is_fixed_rate',
        'type',
    ];

    protected $casts = [
        'is_fixed_rate' => 'boolean',
    ];
}
