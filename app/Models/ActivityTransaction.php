<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Activity Transaction
 *
 * @property int $id
 * @property int $activity_id
 * @property int $user_id
 * @property string|null $phone
 * @property int $activity_version
 * @property string $activity_period
 * @property string $activity_type
 * @property string $activity_cycle
 * @property string|null $activity_rate
 * @property string $activity_amount
 * @property string $reference_amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Activity $activity
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereActivityVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereReferenceAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereUserId($value)
 *
 * @property string|null $claimed_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ActivityTransaction whereClaimedAt($value)
 *
 * @mixin \Eloquent
 */
class ActivityTransaction extends Model
{
    protected $table = 'activity_transactions';

    protected $fillable = [
        'user_id',
        'phone',
        'activity_id',
        'activity_version',
        'activity_period',
        'activity_type',
        'activity_cycle',
        'activity_rate',
        'activity_amount',
        'reference_amount',
        'claimed_at',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }
}
