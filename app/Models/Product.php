<?php

namespace App\Models;

use App\Constants\OrderStatus;
use App\Services\SystemSettingService;
use App\Utils\Math;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

/**
 * @property int $id
 * @property string $name
 * @property string $image
 * @property string|null $description
 * @property float $price
 * @property float $promotion_price
 * @property string $daily_profit
 * @property string $reward
 * @property int $limited
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $active
 * @property int $category_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Order> $orders
 * @property-read int|null $orders_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDailyProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereLimited($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePromotionPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereReward($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUpdatedAt($value)
 *
 * @property string $type car, store
 * @property string|null $rules
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Store> $stores
 * @property-read int|null $stores_count
 * @property-read mixed $limit_purchase
 * @property-read mixed $total_daily_release
 * @property-read mixed $total_purchased
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereType($value)
 *
 * @property array<array-key, mixed>|null $metadata
 * @property int $sort
 * @property-read \App\Models\Category|null $category
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSort($value)
 *
 * @mixin \Eloquent
 */
class Product extends Model
{
    use Timestamp;

    protected $fillable = [
        'category_id',
        'name',
        'image',
        'description',
        'price',
        'promotion_price',
        'daily_profit',
        'reward',
        'limited',
        'active',
        'rules',
        'metadata',
        'sort',
        'type',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function stores(): HasMany
    {
        return $this->hasMany(Store::class, 'product_id');
    }

    public function totalPurchased(): Attribute
    {
        return new Attribute(
            get: fn () => (int) Order::where('product_id', $this->id)
                ->where('user_id', Auth::id())
                ->where('status', OrderStatus::Processed)
                ->sum('quantity')
        );
    }

    public function limitPurchase(): Attribute
    {
        if (Auth::user()) {
            $defaultLimit = config('features.purchase.limit_purchased_special_product');
            //            $userSubordinates = Auth::user()->children()
            //                ->from('users')
            //                ->leftJoin('user_banks as ub', 'ub.user_id', '=', 'users.id')
            //                ->whereNotNull('ub.id')
            //                ->where('ub.active', 1)
            //                ->whereNotNull('users.id_card')
            //                ->count('users.id') ?? 0;
            //            $limitation = $defaultLimit + $userSubordinates;
            // get total limited purchased products
            $purchasedOrder = Order::where('user_id', Auth::id())->where('product_id', $this->id)
                ->where('status', OrderStatus::Processed)
                ->sum('quantity');

            return new Attribute(
                get: fn () => (int) max(0, $limitation - $purchasedOrder)
            );
        }

        return new Attribute(
            get: fn () => 0
        );
    }

    public function getTypeText(): string
    {
        return __('messages.stores.types.' . $this->type);
    }

    public function reserveFundProfit(): Attribute
    {
        $reservedFundRate = SystemSettingService::getSetting('reserved_fund_rate');

        return new Attribute(
            get: fn () => Math::formatNumber((float) $this->daily_profit * $reservedFundRate, true)
        );
    }

    protected function casts(): array
    {
        return [
            'metadata' => 'json',
        ];
    }
}
