<?php

namespace App\Models;

use App\Scopes\UserLocalScope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * TeamStatistic
 *
 * @property int $id
 * @property int $user_id
 * @property int $total_members
 * @property string $total_investment
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $rebate_commission
 * @property string|null $investment_commission
 * @property string|null $team_rebate_commission
 * @property string|null $team_investment_commission
 * @property string|null $profit_rebate_commission
 * @property string|null $team_profit_rebate_commission
 * @property-read \App\Models\User|null $user
 *
 * @method static Builder<static>|TeamStatistic mine()
 * @method static Builder<static>|TeamStatistic newModelQuery()
 * @method static Builder<static>|TeamStatistic newQuery()
 * @method static Builder<static>|TeamStatistic query()
 * @method static Builder<static>|TeamStatistic whereCreatedAt($value)
 * @method static Builder<static>|TeamStatistic whereId($value)
 * @method static Builder<static>|TeamStatistic whereInvestmentCommission($value)
 * @method static Builder<static>|TeamStatistic whereRebateCommission($value)
 * @method static Builder<static>|TeamStatistic whereTeamInvestmentCommission($value)
 * @method static Builder<static>|TeamStatistic whereTeamRebateCommission($value)
 * @method static Builder<static>|TeamStatistic whereTotalInvestment($value)
 * @method static Builder<static>|TeamStatistic whereTotalMembers($value)
 * @method static Builder<static>|TeamStatistic whereUpdatedAt($value)
 * @method static Builder<static>|TeamStatistic whereUserId($value)
 * @method static Builder<static>|TeamStatistic withHierarchyChain(int $userId)
 *
 * @mixin \Eloquent
 */
class TeamStatistic extends Model
{
    use UserLocalScope;

    protected $table = 'team_statistic';

    protected $fillable = [
        'user_id',
        'total_members',
        'total_investment',
        'rebate_commission',
        'investment_commission',
        'team_rebate_commission',
        'team_investment_commission',
        'profit_rebate_commission',
        'team_profit_rebate_commission',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeWithHierarchyChain(Builder $query, int $userId): Builder
    {
        return $query->where(function ($query) use ($userId) {
            $query->where('team_statistic.user_id', $userId) // Include the user themselves
                ->orWhere('hierarchy', 'like', $userId . '|%')
                ->orWhere('hierarchy', 'like', '%|' . $userId . '|%')
                ->orWhere('hierarchy', 'like', '%|' . $userId . '|');
        });
    }
}
