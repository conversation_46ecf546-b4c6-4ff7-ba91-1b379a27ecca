<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $ancestor_id
 * @property int $descendant_id
 * @property int $depth
 * @property \Illuminate\Support\Carbon $created_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy whereAncestorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy whereDepth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy whereDescendantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserHierarchy query()
 *
 * @mixin \Eloquent
 */
class UserHierarchy extends Model
{
    protected $primaryKey = null;

    public $incrementing = false;

    public $timestamps = false;

    protected $fillable = [
        'ancestor_id',
        'descendant_id',
        'depth',
    ];
}
