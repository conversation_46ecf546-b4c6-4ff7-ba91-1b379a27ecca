<?php

declare(strict_types=1);

namespace App\Models;

use <PERSON>rke\Admin\Model\AuthGroupAccess as BaseAuthGroupAccess;

/*
 * AuthGroupAccess
 *
 * @create 2020-10-20
 * <AUTHOR>
 */

/**
 * @property string $id
 * @property string $admin_id
 * @property string $group_id
 * @property-read \Larke\Admin\Model\AuthGroup|null $group
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess whereAdminId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthGroupAccess withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 */
class AuthGroupAccess extends BaseAuthGroupAccess
{
    //
}
