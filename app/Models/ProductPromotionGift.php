<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ProductPromotionGift extends Model
{
    protected $fillable = [
        'product_id',
        'promotion_card_id',
        'status',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function promotionCard()
    {
        return $this->belongsTo(PromotionCard::class);
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }
}
