<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * UserStatistic
 *
 * @property int $id
 * @property int $user_id
 * @property int $qualified
 * @property string|null $total_deposit
 * @property string|null $total_profit
 * @property string|null $total_reward
 * @property string|null $total_withdraw
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereQualified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereTotalDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereTotalProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereTotalReward($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereTotalWithdraw($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserStatistic whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserStatistic extends Model
{
    use HasFactory;

    protected $table = 'user_statistic';

    protected $fillable = [
        'user_id',
        'qualified',
        'total_deposit',
        'total_profit',
        'total_reward',
        'total_withdraw',
    ];

    public $timestamps = false;
}
