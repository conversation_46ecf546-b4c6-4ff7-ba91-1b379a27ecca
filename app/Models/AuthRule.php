<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Support\Facades\Cache;
use <PERSON><PERSON><PERSON>\Admin\Model\AuthRule as AuthRuleModel;

/*
 * AuthRule
 *
 * @create 2020-10-20
 * <AUTHOR>
 */
/**
 * @property string $id 规则id
 * @property string $parentid 上级分类ID
 * @property string $title 名称
 * @property string $url 权限链接
 * @property string $method 请求类型
 * @property string $slug 地址标识
 * @property string|null $description 描述
 * @property int|null $listorder 排序ID
 * @property int $is_need_auth 1-验证权限
 * @property int $is_system 1-系统权限
 * @property int $status 状态
 * @property int $update_time 更新时间
 * @property string $update_ip 修改IP
 * @property int $create_time 创建时间
 * @property string $create_ip 创建IP
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AuthRule> $childrenModule
 * @property-read int|null $children_module_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AuthGroup> $groups
 * @property-read int|null $groups_count
 * @property-read \App\Models\AuthRuleAccess|null $ruleAccess
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereCreateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereIsNeedAuth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereIsSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereListorder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereParentid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereUpdateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereUpdateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRule withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 */
class AuthRule extends AuthRuleModel
{
    protected $table = 'larke_auth_rule';

    protected $keyType = 'string';

    protected $primaryKey = 'id';

    protected $guarded = [];

    protected $casts = [
        'id' => 'string',
        'parentid' => 'string',
    ];

    public $incrementing = false;

    public $timestamps = false;

    /**
     * 规则的分组列表
     */
    public function groups()
    {
        return $this->belongsToMany(AuthGroup::class, AuthRuleAccess::class, 'rule_id', 'group_id');
    }

    /**
     * 授权
     */
    public function ruleAccess()
    {
        return $this->hasOne(AuthRuleAccess::class, 'rule_id', 'id');
    }

    /**
     * 获取子模块
     */
    public function childrenModule()
    {
        return $this->hasMany($this, 'parentid', 'id');
    }

    /**
     * 递归获取子模块
     */
    public function children()
    {
        return $this->childrenModule()->with('children');
    }

    public static function getCacheStore()
    {
        $store = config('larkeadmin.cache.auth_rule.store');
        $store = ($store == 'default') ? null : $store;
        $cacheStore = Cache::store($store);

        return $cacheStore;
    }

    public static function getAuthRules()
    {
        $cacheStore = static::getCacheStore();

        $configKey = config('larkeadmin.cache.auth_rule.key');
        $rules = $cacheStore->get($configKey);
        if (!$rules) {
            $rules = self::all()->toArray();

            $configTtl = config('larkeadmin.cache.auth_rule.ttl');
            $cacheStore->put($configKey, $rules, $configTtl);
        }

        return $rules;
    }
}
