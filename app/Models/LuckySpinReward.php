<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Lucky Spin Reward
 *
 * @property int $id
 * @property int $event_id
 * @property string $name
 * @property string $reward_type
 * @property float $reward_value
 * @property float $probability
 * @property string $currency_code
 * @property string|null $icon_url
 * @property bool $is_active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\LuckySpinEvent $event
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinResult> $results
 */
class LuckySpinReward extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'name',
        'reward_type',
        'reward_value',
        'probability',
        'currency_code',
        'icon_url',
        'is_active',
    ];

    protected $casts = [
        'reward_value' => 'decimal:2',
        'probability' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(LuckySpinEvent::class, 'event_id');
    }

    public function results(): HasMany
    {
        return $this->hasMany(LuckySpinResult::class, 'reward_id');
    }

    public function isUsdt(): bool
    {
        return $this->reward_type === 'usdt';
    }

    public function getFormattedValue(): string
    {
        return number_format($this->reward_value, 2) . ' ' . $this->currency_code;
    }
}
