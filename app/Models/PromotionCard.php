<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PromotionCard extends Model
{
    protected $fillable = [
        'description',
        'original_price',
        'selling_price',
        'stocks',
        'days_reduction',
        'is_gift',
        'active',
    ];

    protected $casts = [
        'original_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'stocks' => 'integer',
        'days_reduction' => 'integer',
        'is_gift' => 'boolean',
        'active' => 'boolean',
    ];

    public function userCards(): HasMany
    {
        return $this->hasMany(UserPromotionCard::class);
    }

    public function productGifts(): HasMany
    {
        return $this->hasMany(ProductPromotionGift::class);
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    public function scopeInStock(Builder $query): Builder
    {
        return $query->where('stocks', '>', 0);
    }

    public function scopeGiftCards(Builder $query): Builder
    {
        return $query->where('is_gift', true);
    }
}
