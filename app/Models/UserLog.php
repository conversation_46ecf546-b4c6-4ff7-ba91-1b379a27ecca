<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * User Log
 *
 * @property int $id
 * @property int $user_id
 * @property string $type
 * @property string $time
 * @property string $ip
 * @property string $ip_country
 * @property array|null $metadata
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereIpCountry($value)
 *
 * @mixin \Eloquent
 */
class UserLog extends Model
{
    protected $table = 'user_logs';

    protected $fillable = [
        'user_id',
        'type',
        'ip',
        'time',
        'ip_country',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    public $timestamps = false;
}
