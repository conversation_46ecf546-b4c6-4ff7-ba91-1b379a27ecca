<?php

namespace App\Models;

use App\Scopes\UserLocalScope;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Refund extends Model
{
    use Timestamp;
    use UserLocalScope;

    protected $fillable = [
        'user_id',
        'order_id',
        'status',
        'amount',
        'user_name',
        'user_id_card',
        'user_bank_id',
        'id_card_front',
        'id_card_back',
        'note',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function histories(): HasMany
    {
        return $this->hasMany(RefundHistory::class);
    }
}
