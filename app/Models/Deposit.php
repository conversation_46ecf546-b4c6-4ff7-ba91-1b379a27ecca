<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Deposit
 *
 * @property int $id
 * @property int $wallet_id
 * @property int|null $payment_channel_id
 * @property int $user_id
 * @property string $code
 * @property int $method 1: auto_deposit, 2: manual_deposit
 * @property string $amount
 * @property string $fee
 * @property string $actual_amount
 * @property numeric $old_balance
 * @property numeric $new_balance
 * @property string|null $original_amount
 * @property string|null $original_currency
 * @property string|null $exchange_rate
 * @property \Illuminate\Support\Carbon|null $exchange_rate_time
 * @property string|null $bank_name
 * @property string|null $bank_account
 * @property string|null $bank_account_name
 * @property string|null $remark
 * @property int $status 0: pending, 1: processing, 2: approved, 3: rejected
 * @property string|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $integration
 * @property-read \App\Models\PaymentChannel|null $paymentChannel
 * @property-read \App\Models\User|null $user
 * @property-read \App\Models\Wallet|null $wallet
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereActualAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereBankAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereBankAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereBankName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereIntegration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereNewBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereOldBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit wherePaymentChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Deposit whereWalletId($value)
 *
 * @mixin \Eloquent
 */
class Deposit extends Model
{
    use HasFactory;
    use HasTimestamps;

    /**
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'wallet_id',
        'payment_channel_id',
        'method',
        'user_bank_id',
        'code',
        'amount',
        'currency',
        'fee',
        'actual_amount',
        'old_balance',
        'new_balance',
        'original_amount',
        'original_currency',
        'exchange_rate',
        'exchange_rate_time',
        'remark',
        'status',
        'processed_at',
        'integration',
    ];

    protected $casts = [
        'amount' => 'string',
        'fee' => 'string',
        'actual_amount' => 'string',
        'old_balance' => 'string',
        'new_balance' => 'string',
        'original_amount' => 'string',
        'exchange_rate_time' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function paymentChannel(): BelongsTo
    {
        return $this->belongsTo(PaymentChannel::class);
    }

    /**
     * Check if this deposit has original currency data (was converted)
     */
    public function hasOriginalCurrency(): bool
    {
        return !is_null($this->original_amount) && !is_null($this->original_currency);
    }

    /**
     * Get the display amount (original if available, otherwise current amount)
     */
    public function getDisplayAmount(): string
    {
        return $this->amount;
    }

    /**
     * Get the display currency (always app currency)
     */
    public function getDisplayCurrency(): string
    {
        return config('app.currency');
    }
}
