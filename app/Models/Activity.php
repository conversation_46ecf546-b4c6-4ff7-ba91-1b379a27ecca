<?php

namespace App\Models;

use App\Constants\ActivityCycle;
use App\Constants\ActivityType;
use Illuminate\Database\Eloquent\Model;

/**
 * Activity
 *
 * @property int $id
 * @property string $name Activity name
 * @property int $version
 * @property ActivityType $type
 * @property ActivityCycle $cycle
 * @property array|null $rates
 * @property string $start_date
 * @property string $end_date
 * @property float|null $min_threshold
 * @property float|null $max_threshold
 * @property int $status 0: pending, 1: processing, 2: processed, 3: cancelled
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\Models\User|null $createdBy
 * @property-read \App\Models\User|null $updatedBy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereDeletedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereMaxThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereMinThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereRates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Activity whereVersion($value)
 *
 * @mixin \Eloquent
 */
class Activity extends Model
{
    protected $fillable = [
        'name',
        'version',
        'type',
        'cycle',
        'rates',
        'start_date',
        'end_date',
        'min_threshold',
        'max_threshold',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'type' => ActivityType::class,
        'cycle' => ActivityCycle::class,
        'rates' => 'array',
    ];

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getConfig($value)
    {
        $rates = $this->rates;
        if (!is_array($this->rates)) {
            $rates = json_decode($this->rates, true);
        }
        if (!empty($rates[$value])) {
            return $rates[$value];
        }

        return null;
    }
}
