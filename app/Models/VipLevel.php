<?php

namespace App\Models;

use App\Traits\HasContentTranslations;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * User Vip Level
 *
 * @property int $id
 * @property string $name
 * @property int|null $level_order
 * @property float $discount
 * @property float $withdraw_fee
 * @property float $min_withdraw_amount
 * @property float|null $contribute_amount_min
 * @property float|null $contribute_amount_max
 * @property float|null $contribute_amount_fixed
 * @property float $profit_rate_bonus
 * @property float $investment_profit_rebate_rate
 * @property float $level_up_reward
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereLevelOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereMaxPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereMinPoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereDiscount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereWithdrawFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereMinWithdrawAmount($value)
 *
 * @property-read \App\Models\VipLevelCondition|null $activeCondition
 * @property-read \App\Models\VipLevelCondition|null $condition
 * @property-read \App\Models\WithdrawSetting|null $withdrawSettings
 *
 * @method static \Database\Factories\VipLevelFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereContributeAmountFixed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereContributeAmountMax($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereContributeAmountMin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereLevelUpReward($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VipLevel whereProfitRateBonus($value)
 *
 * @mixin \Eloquent
 */
class VipLevel extends Model
{
    use HasContentTranslations;
    use LogsActivity;

    protected static array $recordEvents = ['created', 'updated', 'deleted'];

    protected $fillable = [
        'name',
        'description',
        'level_order',
        'discount',
        'withdraw_fee',
        'min_withdraw_amount',
        'contribute_amount_min',
        'contribute_amount_max',
        'contribute_amount_fixed',
        'profit_rate_bonus',
        'investment_profit_rebate_rate',
        'level_up_reward',
        'is_active',
    ];

    public array $translatable = ['name', 'description'];

    /**
     * Find the highest VIP level that user qualifies for based on conditions
     */
    public static function getQualifyingLevel(
        float $userBalance,
        float $userTotalDeposit,
        float $subordinatesBalance,
        float $subordinatesTotalDeposit
    ): ?VipLevel {
        $levels = static::with('activeCondition')
            ->whereHas('activeCondition')
            ->orderBy('level_order', 'desc') // Check highest levels first
            ->get();

        foreach ($levels as $level) {
            if (
                $level->activeCondition &&
                $level->activeCondition->meetsAllConditions(
                    $userBalance,
                    $userTotalDeposit,
                    $subordinatesBalance,
                    $subordinatesTotalDeposit
                )
            ) {
                return $level;
            }
        }

        // Return lowest level as fallback
        return static::orderBy('level_order', 'asc')->first();
    }

    /**
     * @deprecated Use getQualifyingLevel instead
     */
    public static function getCurrentLevelForPoints($points, $currentLevelOrder = null)
    {
        $query = static::query();

        // If user has a current level, don't allow downgrading
        if ($currentLevelOrder !== null) {
            $query->where('level_order', '>=', $currentLevelOrder);
        }

        return $query->orderBy('level_order', 'desc')
            ->first();
    }

    /**
     * Get the condition for this VIP level
     */
    public function condition(): HasOne
    {
        return $this->hasOne(VipLevelCondition::class);
    }

    /**
     * Get the active condition for this VIP level
     */
    public function activeCondition(): HasOne
    {
        return $this->hasOne(VipLevelCondition::class)->where('is_active', true);
    }

    public function withdrawSettings(): MorphOne
    {
        return $this->morphOne(WithdrawSetting::class, 'belongs');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->useLogName('vip_level');
    }

    public function getName(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('name', $locale);
    }

    public function getDescription(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('description', $locale);
    }

    public function toArrayWithTranslations(): array
    {
        $data = $this->toArray();
        $data['translations'] = $this->getAllTranslations();

        return $data;
    }
}
