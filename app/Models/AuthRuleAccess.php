<?php

declare(strict_types=1);

namespace App\Models;

use Larke\Admin\Model\AuthRuleAccess as BaseModel;

/*
 * AuthRuleAccess
 *
 * @create 2020-10-20
 * <AUTHOR>
 */
/**
 * @property string $id
 * @property string $group_id
 * @property string $rule_id
 * @property-read \Larke\Admin\Model\AuthRule|null $rule
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess whereRuleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthRuleAccess withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 */
class AuthRuleAccess extends BaseModel
{
}
