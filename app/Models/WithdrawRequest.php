<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Withdraw request
 *
 * @property int $id
 * @property string $provider
 * @property string $order_external_id Order external Id to integrate with provider
 * @property string $withdraw_id
 * @property int $user_id
 * @property int $wallet_id
 * @property int $payment_channel_id
 * @property string $amount
 * @property string $fee
 * @property string $status
 * @property string $bank_account_name
 * @property string $bank_account_number
 * @property string $bank_name
 * @property string|null $bank_code
 * @property string|null $phone
 * @property string|null $pay_url Provider pay url
 * @property string|null $pay_order_id Provider order id
 * @property int|null $pay_order_time Provider order time
 * @property string|null $callback_data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\PaymentChannel $paymentChannel
 * @property-read \App\Models\User $user
 * @property-read \App\Models\Wallet $wallet
 * @property-read \App\Models\Withdraw $withdraw
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereBankAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereBankAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereBankCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereBankName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereCallbackData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereOrderExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest wherePaymentChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest wherePayOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest wherePayOrderTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest wherePayUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereWalletId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WithdrawRequest whereWithdrawId($value)
 *
 * @mixin \Eloquent
 */
class WithdrawRequest extends Model
{
    protected $table = 'withdraw_requests';

    protected $fillable = [
        'provider',
        'order_external_id',
        'withdraw_id',
        'user_id',
        'wallet_id',
        'payment_channel_id',
        'amount',
        'fee',
        'status',
        'bank_account_name',
        'bank_account_number',
        'bank_name',
        'bank_code',
        'phone',
        'pay_url',
        'pay_order_id',
        'pay_order_time',
        'callback_data',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class);
    }

    public function paymentChannel(): BelongsTo
    {
        return $this->belongsTo(PaymentChannel::class);
    }

    public function withdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class);
    }
}
