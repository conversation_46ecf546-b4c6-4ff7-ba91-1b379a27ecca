<?php

namespace App\Models;

use App\Traits\HasContentTranslations;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;

/**
 * News
 *
 * @property int $id
 * @property string $author
 * @property string|null $thumbnail
 * @property int $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $title
 * @property string|null $description
 * @property string|null $content
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereAuthor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereThumbnail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|News whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class News extends Model
{
    use HasContentTranslations;
    use Timestamp;

    public array $translatable = ['title', 'description', 'content'];

    protected $fillable = [
        'author',
        'thumbnail',
        'active',
        'title',
        'description',
        'content',
    ];

    public function getTitle(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('title', $locale);
    }

    public function getDescription(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('description', $locale);
    }

    public function getContent(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('content', $locale);
    }

    public function toArrayWithTranslations(): array
    {
        $data = $this->toArray();
        $data['translations'] = $this->getAllTranslations();

        return $data;
    }

    /**
     * Get the full URL for the thumbnail (handles both old and new path formats)
     */
    public function getThumbnailUrl(): ?string
    {
        if (empty($this->thumbnail)) {
            return null;
        }

        // If path already starts with /storage/, return as-is (current format)
        if (str_starts_with($this->thumbnail, '/storage/')) {
            return $this->thumbnail;
        }

        // If path starts with http, return as-is (full URL)
        if (str_starts_with($this->thumbnail, 'http')) {
            return $this->thumbnail;
        }

        // For legacy relative paths, prepend /storage/
        return '/storage/' . ltrim($this->thumbnail, '/');
    }

    /**
     * Get the table associated with the model.
     */
    public function getTable(): string
    {
        return 'news';
    }
}
