<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

/**
 * OTP
 *
 * @property int $id
 * @property string|null $phone
 * @property int|null $user_id
 * @property string $code
 * @property int $used
 * @property int $status
 * @property string $expired_at
 * @property string|null $type
 * @property string|null $verified_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $encrypted_details
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereUsed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Otp whereVerifiedAt($value)
 *
 * @mixin \Eloquent
 */
class Otp extends Model
{
    use HasTimestamps;

    public $table = 'otp';

    /**
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'phone',
        'email',
        'code',
        'status',
        'type',
        'created_at',
        'expired_at',
        'verified_at',
        'used',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Encrypt the id, phone, and type into one string.
     *
     * @return string
     */
    public function getEncryptedDetailsAttribute()
    {
        // Combine the fields into an array or string
        $data = [
            'id' => $this->id,
            'phone' => $this->phone,
            'type' => $this->type,
        ];

        // Encrypt the data and return it as a string
        return Crypt::encrypt(json_encode($data));
    }

    public function useOtp($encryptedDetails, $type)
    {
        $otp = self::validateSecurityPin($encryptedDetails, $type);
        if (!empty($otp)) {
            $otp->used = true;
            $otp->save();

            return true;
        }

        return false;
    }

    /**
     * Decrypt the encrypted details back into the original data.
     *
     * @param  string  $encrypted
     * @return array
     */
    public static function validateSecurityPin($encryptedDetails, string $type, ?string $phone = null, ?User $user = null): ?Otp
    {
        try {
            // Decrypt the data
            $decryptedData = Crypt::decrypt($encryptedDetails);

            // Decode the decrypted JSON back into an array
            $data = json_decode($decryptedData, true);

            // Ensure all required fields (id, phone, type) are present
            if (isset($data['id'], $data['phone'], $data['type'])) {
                $otpEntity = self::where('id', $data['id'])->where('phone', $data['phone'])->where('type', $data['type'])->where('used', false)->first();
                if (empty($otpEntity)) {
                    throw new \Exception('OTP used or invalid');
                }

                if (!empty($phone) && $otpEntity->phone !== $phone) {
                    throw new \Exception('Phone is not same');
                }

                // Find and return the user model based on the decrypted ID
                if (!empty($user) && $otpEntity->phone !== $user->phone) {
                    throw new \Exception('Phone is not same');
                }

                if ($otpEntity->type !== $type) {
                    throw new \Exception('Type is not same');
                }

                return $otpEntity;
            } else {
                throw new \Exception('Decrypted data is missing required fields.');
            }
        } catch (\Exception $e) {
            // Handle any decryption errors or missing fields
            // Optionally log the error message for debugging
            Log::error('Decryption failed: ' . $e->getMessage());

            // Return null or throw a custom exception if needed
            return null;
        }
    }
}
