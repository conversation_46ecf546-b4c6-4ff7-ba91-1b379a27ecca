<?php

namespace App\Models;

use App\Constants\TransactionType;
use App\Constants\WalletType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Wallet
 *
 * @property int $id
 * @property int $user_id
 * @property numeric $balance
 * @property numeric $profit
 * @property string $type
 * @property int $active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read mixed $investment_profit
 * @property-read Collection<int, \App\Models\TransactionLog> $transactions
 * @property-read int|null $transactions_count
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereUserId($value)
 *
 * @property-read mixed $remaining_balance
 * @property-read mixed $remaining_profit
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereProfit($value)
 *
 * @property-read float $raw_balance
 * @property-read float $raw_profit
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereActive($value)
 *
 * @property string $deposit
 * @property string $reward
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Wallet whereReward($value)
 *
 * @mixin \Eloquent
 */
class Wallet extends Model
{
    use HasFactory;
    use HasTimestamps;

    /**
     * @var string[]
     */
    protected $fillable = [
        'user_id',
        'balance',
        'profit',
        'type',
        'active',
        'deposit',
        'reward',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'profit' => 'decimal:2',
        'deposit' => 'decimal:2',
        'reward' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(TransactionLog::class, 'wallet_id');
    }

    public function getInvestmentProfitAttribute()
    {
        return $this->transactions()
            ->where('wallet_id', $this->id)
            ->where('type', TransactionType::Investment->value)
            ->sum('amount');
    }

    public function getRemainingProfitAttribute()
    {
        $withdrawAmount = $this->transactions()
            ->where('wallet_id', $this->id)
            ->where('type', TransactionType::Withdraw->value)
            ->sum('amount');

        return $this->profit - $withdrawAmount;
    }

    public function getRemainingBalanceAttribute()
    {
        if ($this->type === WalletType::Reward->value) {
            $rewardsAmount = $this->transactions()
                ->where('wallet_id', $this->id)
                ->whereIn('type', TransactionType::getRewardTypes())
                ->sum('amount');

            return $this->profit + $rewardsAmount;
        }

        return $this->balance;
    }

    public function getWalletNumber(): string
    {
        $number = ($this->id + $this->user_id) * 123;
        if ($number < 1000) {
            $number = str_pad($number, 4, '0');
        }

        return (string) $number;
    }
}
