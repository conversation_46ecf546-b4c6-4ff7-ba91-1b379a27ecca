<?php

namespace App\Models;

use App\Traits\HasContentTranslations;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;

/**
 * Page
 *
 * @property int $id
 * @property string $slug
 * @property string|null $thumbnail
 * @property int $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array $title
 * @property array $description
 * @property array $content
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereThumbnail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Page whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Page extends Model
{
    use HasContentTranslations;
    use Timestamp;

    public array $translatable = ['title', 'description', 'content'];

    protected $fillable = [
        'slug',
        'thumbnail',
        'active',
        'title',
        'description',
        'content',
    ];

    public function getTitle(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('title', $locale);
    }

    public function getDescription(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('description', $locale);
    }

    public function getContent(?string $locale = null): ?string
    {
        return $this->getTranslatedAttribute('content', $locale);
    }

    public function toArrayWithTranslations(): array
    {
        $data = $this->toArray();
        $data['translations'] = $this->getAllTranslations();

        return $data;
    }

    /**
     * Get the full URL for the thumbnail (handles both old and new path formats)
     */
    public function getThumbnailUrl(): ?string
    {
        if (empty($this->thumbnail)) {
            return null;
        }

        // If path already starts with /storage/, return as-is (current format)
        if (str_starts_with($this->thumbnail, '/storage/')) {
            return $this->thumbnail;
        }

        // If path starts with http, return as-is (full URL)
        if (str_starts_with($this->thumbnail, 'http')) {
            return $this->thumbnail;
        }

        // For legacy relative paths, prepend /storage/
        return '/storage/' . ltrim($this->thumbnail, '/');
    }
}
