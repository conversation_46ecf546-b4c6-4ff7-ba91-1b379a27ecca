<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Lucky Spin User Chance
 *
 * @property int $id
 * @property int $user_id
 * @property int $event_id
 * @property int $assignment_id
 * @property int $total_chances
 * @property int $used_chances
 * @property array|null $guaranteed_rewards
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @property-read \App\Models\LuckySpinEvent $event
 * @property-read \App\Models\LuckySpinAssignment $assignment
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinResult> $results
 */
class LuckySpinUserChance extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'assignment_id',
        'total_chances',
        'used_chances',
        'guaranteed_rewards',
    ];

    protected $casts = [
        'guaranteed_rewards' => 'json',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(LuckySpinEvent::class, 'event_id');
    }

    public function assignment(): BelongsTo
    {
        return $this->belongsTo(LuckySpinAssignment::class, 'assignment_id');
    }

    public function results(): HasMany
    {
        return $this->hasMany(LuckySpinResult::class, 'user_id', 'user_id')
            ->where('event_id', $this->event_id)
            ->where('assignment_id', $this->assignment_id);
    }

    public function getRemainingChances(): int
    {
        return max(0, $this->total_chances - $this->used_chances);
    }

    public function hasRemainingChances(): bool
    {
        return $this->getRemainingChances() > 0;
    }

    public function hasGuaranteedRewards(): bool
    {
        return !empty($this->guaranteed_rewards);
    }

    public function getGuaranteedRewards(): array
    {
        return $this->guaranteed_rewards ?? [];
    }

    public function getNextGuaranteedReward(): ?int
    {
        $rewards = $this->getGuaranteedRewards();

        return !empty($rewards) ? $rewards[0] : null;
    }

    public function consumeGuaranteedReward(): ?int
    {
        $rewards = $this->getGuaranteedRewards();

        if (empty($rewards)) {
            return null;
        }

        $nextReward = array_shift($rewards);
        $this->guaranteed_rewards = $rewards;

        return $nextReward;
    }

    public function incrementUsedChances(): void
    {
        $this->used_chances += 1;
    }

    public function isFullyUsed(): bool
    {
        return $this->used_chances >= $this->total_chances;
    }

    public function canSpin(): bool
    {
        return $this->hasRemainingChances() &&
               $this->event->isActive() &&
               !$this->assignment->isExpired();
    }
}
