<?php

namespace App\Models;

use App\Observers\LuckySpinAssignmentObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * Lucky Spin Assignment
 *
 * @property int $id
 * @property int $event_id
 * @property string $assignment_type
 * @property int $target_id
 * @property int $total_chances
 * @property int $remaining_chances
 * @property array|null $guaranteed_rewards
 * @property int|null $assigned_by
 * @property Carbon $assigned_at
 * @property Carbon|null $expires_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\LuckySpinEvent $event
 * @property-read \App\Models\User|null $assignedBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinUserChance> $userChances
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LuckySpinResult> $results
 */
#[ObservedBy(LuckySpinAssignmentObserver::class)]
class LuckySpinAssignment extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $fillable = [
        'event_id',
        'assignment_type',
        'target_id',
        'total_chances',
        'remaining_chances',
        'guaranteed_rewards',
        'assigned_by',
        'assigned_at',
        'expires_at',
    ];

    protected $casts = [
        'guaranteed_rewards' => 'json',
        'assigned_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(LuckySpinEvent::class, 'event_id');
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function userChances(): HasMany
    {
        return $this->hasMany(LuckySpinUserChance::class, 'assignment_id');
    }

    public function results(): HasMany
    {
        return $this->hasMany(LuckySpinResult::class, 'assignment_id');
    }

    public function target()
    {
        if ($this->assignment_type === 'individual') {
            return $this->belongsTo(User::class, 'target_id');
        }

        return $this->belongsTo(Group::class, 'target_id');
    }

    public function isIndividual(): bool
    {
        return $this->assignment_type === 'individual';
    }

    public function isGroup(): bool
    {
        return $this->assignment_type === 'group';
    }

    public function isExpired(): bool
    {
        return $this->expires_at && now()->gt($this->expires_at);
    }

    public function hasGuaranteedRewards(): bool
    {
        return !empty($this->guaranteed_rewards);
    }

    public function getGuaranteedRewards(): array
    {
        return $this->guaranteed_rewards ?? [];
    }

    public function getTargetUsers()
    {
        if ($this->isIndividual()) {
            return collect([User::find($this->target_id)])->filter();
        }

        return User::where('group_id', $this->target_id)->get();
    }

    public function getActivitylogOptions(): LogOptions
    {
        $relatedModel = $this->target;
        if ($relatedModel instanceof User) {
            $name = $relatedModel->phone;
        } else {
            if ($relatedModel instanceof Group) {
                $name = $relatedModel->name;
            } else {
                $name = 'unknown';
            }
        }

        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(function ($event) use ($relatedModel, $name) {
                return 'assigned lucky spin to ' . Str::replace('App\\Models\\', '', get_class($relatedModel)) . ' with name ' . $name;
            })
            ->useLogName('lucky_spin');
    }
}
