<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * User Login
 *
 * @property int $id
 * @property int $user_id
 * @property string $login_date
 * @property int $is_complement
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ActivityTransaction> $activityTransaction
 * @property-read int|null $activity_transaction_count
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereIsComplement($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereLoginDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLogin whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserLogin extends Model
{
    protected $table = 'user_login';

    protected $fillable = [
        'user_id',
        'login_date',
        'is_complement',
        'reward_points',
        'consecutive_days',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function activityTransaction()
    {
        return $this->hasMany(ActivityTransaction::class, 'user_id', 'user_id');
    }
}
