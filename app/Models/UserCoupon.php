<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * User Coupons
 *
 * @property int $id
 * @property int $user_id
 * @property int $coupon_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $used_at
 * @property string|null $used_for
 * @property int|null $used_for_id
 * @property-read \App\Models\Coupon|null $coupon
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon mine()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereCouponId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereUsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserCoupon whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserCoupon extends Model
{
    protected $fillable = [
        'user_id',
        'coupon_id',
        'used_at',
        'used_for',
        'used_for_id',
    ];

    protected $casts = [
        'used_at' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }
}
