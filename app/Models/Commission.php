<?php

namespace App\Models;

use App\Casts\LongDecimal;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Commission
 *
 * @property int $id
 * @property string $type Ex: investment, rebate...
 * @property int $user_id
 * @property string $amount
 * @property float $rate
 * @property int $level User level
 * @property string $reference_type Source Type that user has commission from.
 * @property int $reference_id Source Id that user has commission from. For example: order id or activity id.
 * @property int $reference_user_id Source User Id that user has commission from. For example: subordinate user id.
 * @property int $status 0 - Pending, 1 - Processing, 2 - Processed, 3 - Cancelled
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $available_at Time that commission should be executable
 * @property string|null $expired_at Time that commission should be expired
 * @property string|null $claimed_at Time that commission should be claimed
 * @property string|null $processed_at Time that commission is processed
 * @property-read mixed $referenceObject
 * @property mixed $category_id
 * @property-read \App\Models\User|null $referenceUser
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereAvailableAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereReferenceUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereCategoryId($value)
 *
 * @property-read mixed $reference_object
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereClaimedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Commission whereExpiredAt($value)
 *
 * @mixin \Eloquent
 */
class Commission extends Model
{
    use Timestamp;

    protected $fillable = [
        'type',
        'user_id',
        'amount',
        'rate',
        'level',
        'reference_type',
        'reference_id',
        'reference_user_id',
        'status',
        'available_at',
        'expired_at',
        'claimed_at',
        'processed_at',
        'category_id',
        'metadata',
    ];

    protected $casts = [
        'amount' => LongDecimal::class,
        'rate' => LongDecimal::class,
        'available_at' => 'datetime',
        'expired_at' => 'datetime',
        'claimed_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function referenceUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reference_user_id');
    }

    public function getReferenceObjectAttribute()
    {
        if (!empty($this->reference_type) && class_exists($this->reference_type)) {
            return $this->reference_type::find($this->reference_id);
        }

        return null;
    }
}
