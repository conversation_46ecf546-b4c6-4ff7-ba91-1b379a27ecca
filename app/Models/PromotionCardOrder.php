<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Promotion Card Order
 *
 * @property int $id
 * @property string $code
 * @property int $user_id
 * @property int $promotion_card_id
 * @property numeric $amount
 * @property int $quantity
 * @property numeric $total_amount
 * @property int $status
 * @property string $payment_type
 * @property int|null $wallet_id
 * @property string|null $integration_type
 * @property int|null $payment_channel_id
 * @property string|null $external_id
 * @property-read PromotionCard|null $promotionCard
 *
 * @method static Builder<static>|PromotionCardOrder newModelQuery()
 * @method static Builder<static>|PromotionCardOrder newQuery()
 * @method static Builder<static>|PromotionCardOrder query()
 * @method static Builder<static>|PromotionCardOrder whereAmount($value)
 * @method static Builder<static>|PromotionCardOrder whereCode($value)
 * @method static Builder<static>|PromotionCardOrder whereExternalId($value)
 * @method static Builder<static>|PromotionCardOrder whereId($value)
 * @method static Builder<static>|PromotionCardOrder whereIntegrationType($value)
 * @method static Builder<static>|PromotionCardOrder wherePaymentChannelId($value)
 * @method static Builder<static>|PromotionCardOrder wherePaymentType($value)
 * @method static Builder<static>|PromotionCardOrder wherePromotionCardId($value)
 * @method static Builder<static>|PromotionCardOrder whereQuantity($value)
 * @method static Builder<static>|PromotionCardOrder whereStatus($value)
 * @method static Builder<static>|PromotionCardOrder whereTotalAmount($value)
 * @method static Builder<static>|PromotionCardOrder whereUserId($value)
 * @method static Builder<static>|PromotionCardOrder whereWalletId($value)
 *
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 *
 * @method static Builder<static>|PromotionCardOrder whereCreatedAt($value)
 * @method static Builder<static>|PromotionCardOrder whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PromotionCardOrder extends Model
{
    use Timestamp;

    protected $fillable = [
        'code',
        'user_id',
        'promotion_card_id',
        'amount',
        'quantity',
        'total_amount',
        'status',
        'payment_type',
        'wallet_id',
        'integration_type',
        'payment_channel_id',
        'external_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function promotionCard(): BelongsTo
    {
        return $this->belongsTo(PromotionCard::class);
    }
}
