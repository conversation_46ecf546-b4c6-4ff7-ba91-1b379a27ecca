<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * PaymentServiceProvider
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property int $deposit
 * @property int $withdraw
 * @property int $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentServiceProvider whereWithdraw($value)
 *
 * @mixin \Eloquent
 */
class PaymentServiceProvider extends Model
{
    protected $fillable = [
        'code',
        'name',
        'deposit',
        'withdraw',
        'active',
    ];
}
