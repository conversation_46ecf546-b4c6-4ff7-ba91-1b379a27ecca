<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * IceToken
 *
 * @property int $id
 * @property int $user_id
 * @property int $contribute_id
 * @property float $amount
 * @property Carbon $expired_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User $user
 * @property-read Contribute $contribute
 *
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereContributeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IToken active()
 * @method static \Illuminate\Database\Eloquent\Builder|IToken expired()
 * @method static \Illuminate\Database\Eloquent\Builder|IToken forUser($userId)
 *
 * @property-read string $formatted_amount
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IToken query()
 *
 * @mixin \Eloquent
 */
class IToken extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'contribute_id',
        'amount',
        'expired_at',
        'transaction_code',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'expired_at' => 'datetime',
    ];

    /**
     * Get the user that owns the ice token.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the contribute associated with the ice token.
     */
    public function contribute(): BelongsTo
    {
        return $this->belongsTo(Contribute::class);
    }

    /**
     * Scope a query to only include active (non-expired) tokens.
     */
    public function scopeActive($query)
    {
        return $query->where('expired_at', '>', now());
    }

    /**
     * Scope a query to only include expired tokens.
     */
    public function scopeExpired($query)
    {
        return $query->where('expired_at', '<=', now());
    }

    /**
     * Scope a query to only include tokens for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if the token is expired.
     */
    public function isExpired(): bool
    {
        return $this->expired_at->isPast();
    }

    /**
     * Check if the token is active (not expired).
     */
    public function isActive(): bool
    {
        return !$this->isExpired();
    }

    /**
     * Get the remaining days until expiration.
     */
    public function getRemainingDays(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->expired_at);
    }

    /**
     * Get the formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }
}
