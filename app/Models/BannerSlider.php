<?php

namespace App\Models;

use App\Models\Scopes\BannerSliderScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;

#[ScopedBy(BannerSliderScope::class)]
/**
 * @property string $id
 * @property string|null $belong_type 附件属于
 * @property string|null $belong_id 附件属于ID
 * @property string $name 文件名
 * @property string $path 文件路径
 * @property string $mime 文件mime类型
 * @property string $extension 文件类型
 * @property int $size 文件大小
 * @property string $md5 文件md5
 * @property string $sha1 sha1 散列值
 * @property string $driver 上传驱动
 * @property int $status 状态
 * @property int $update_time 更新时间
 * @property string $update_ip 修改IP
 * @property int $create_time 创建时间
 * @property string $create_ip 创建IP
 * @property string|null $category
 * @property-read Model|\Eloquent|null $attachmentable
 * @property-read mixed $url
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider actived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider byMd5($md5)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider bySha1($sha1)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider fieldByHidden($field)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider inactived()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider orWheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereBelongId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereBelongType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereCreateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereMd5($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereMime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereSha1($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereUpdateIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider whereUpdateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider wheres(array $columns)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BannerSlider withCertain($relation, array $columns)
 *
 * @mixin \Eloquent
 * @mixin Builder
 */
class BannerSlider extends Attachment
{
    protected $table = 'larke_attachment';

    protected $fillable = [
        'belong_type',
        'belong_id',
        'name',
        'path',
        'mime',
        'extension',
        'size',
        'md5',
        'sha1',
        'driver',
        'status',
        'update_time',
        'update_ip',
        'create_time',
        'create_ip',
        'category',
    ];

    protected function casts(): array
    {
        return [
            'status' => 'boolean',
        ];
    }
}
