<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Payment Channel
 *
 * @property int $id
 * @property string $name
 * @property string $service_provider
 * @property string $logo
 * @property string $minimum_amount
 * @property string $maximum_amount
 * @property int $sort
 * @property int $active
 * @property string|null $merchant_name
 * @property string|null $merchant_private_key
 * @property string|null $merchant_public_key
 * @property string|null $platform_private_key
 * @property string|null $platform_public_key
 * @property string|null $sub_merchant_name
 * @property string|null $extend_prop_1
 * @property string|null $extend_prop_2
 * @property string|null $extend_prop_3
 * @property string|null $extend_prop_4
 * @property int|null $min_user_vip_level
 * @property int|null $payment_organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Database\Factories\PaymentChannelFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereExtendProp1($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereExtendProp2($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereExtendProp3($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereExtendProp4($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMaximumAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMerchantName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMerchantPrivateKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMerchantPublicKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMinimumAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereServiceProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel wherePlatformPrivateKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel wherePlatformPublicKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereSubMerchantName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereUpdatedAt($value)
 *
 * @property int $for_deposit
 * @property int $for_withdraw
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereForDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereForWithdraw($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel whereMinUserVipLevel($value)
 *
 * @property-read \App\Models\TFactory|null $use_factory
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentChannel withoutTrashed()
 *
 * @mixin \Eloquent
 */
class PaymentChannel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'service_provider',
        'for_deposit',
        'for_withdraw',
        'active',
        'logo',
        'minimum_amount',
        'maximum_amount',
        'sort',
        'merchant_name',
        'merchant_private_key',
        'merchant_public_key',
        'platform_private_key',
        'platform_public_key',
        'sub_merchant_name',
        'extend_prop_1',
        'extend_prop_2',
        'extend_prop_3',
        'extend_prop_4',
        'min_user_vip_level',
        'payment_organization_id',
    ];

    protected $casts = [
        'minimum_amount' => 'decimal:2',
        'maximum_amount' => 'decimal:2',
    ];
}
