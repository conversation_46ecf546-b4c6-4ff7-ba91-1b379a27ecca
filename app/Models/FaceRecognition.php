<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * FaceRecognition
 *
 * @property int $id
 * @property int $user_id
 * @property string $request_id
 * @property string $business_id
 * @property string $url
 * @property string $img_url
 * @property int $status
 * @property string $reference
 * @property string $reference_id
 * @property string|object $response
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FaceRecognition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FaceRecognition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FaceRecognition query()
 *
 * @mixin \Eloquent
 */
class FaceRecognition extends Model
{
    protected $fillable = [
        'user_id',
        'request_id',
        'business_id',
        'url',
        'img_url',
        'status',
        'response',
    ];

    protected $casts = [
        'status' => 'integer',
        'response' => 'json',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
