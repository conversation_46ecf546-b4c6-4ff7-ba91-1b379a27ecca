<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $report_date
 * @property int $is_registered
 * @property int $is_deposit
 * @property int $is_first_deposit
 * @property int $is_contribute
 * @property string $deposit_amount
 * @property string $withdraw_amount
 * @property string $reward_amount
 * @property string $contribute_profit_amount
 * @property string $investment_interest_amount
 * @property string $commission_amount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereCommissionAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereContributeProfitAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereDepositAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereInvestmentInterestAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereIsContribute($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereIsDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereIsFirstDeposit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereIsRegistered($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereReportDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereRewardAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamDailyReport whereWithdrawAmount($value)
 *
 * @mixin \Eloquent
 */
class TeamDailyReport extends Model
{
    protected $fillable = [
        'user_id',
        'report_date',
        'is_registered',
        'is_deposit',
        'is_first_deposit',
        'is_contribute',
        'deposit_amount',
        'withdraw_amount',
        'reward_amount',
        'contribute_profit_amount',
        'investment_interest_amount',
        'commission_amount',
    ];
}
