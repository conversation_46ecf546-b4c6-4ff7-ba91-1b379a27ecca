<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $required_points
 * @property float $reward_amount
 * @property string $reward_type
 * @property string $reward_source
 * @property string $type
 * @property string $name
 * @property string $description
 * @property bool $is_active
 * @property bool $auto_release
 * @property string $wallet
 * @property Carbon $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder<static>|RewardMilestone newModelQuery()
 * @method static Builder<static>|RewardMilestone newQuery()
 * @method static Builder<static>|RewardMilestone query()
 * @method static Builder<static>|RewardMilestone whereId($value)
 */
class RewardMilestone extends Model
{
    protected $table = 'reward_milestones';

    protected $fillable = [
        'required_points',
        'reward_amount',
        'reward_type',
        'reward_source',
        'type',
        'description',
        'name',
        'is_active',
        'auto_release',
        'wallet',
    ];
}
