<?php

namespace App\Repositories;

use App\Models\User;
use App\Services\SystemSettingService;
use Illuminate\Support\Facades\DB;

class UserRepository
{
    /**
     * Generate a unique 8-digit invitation code for a new user.
     */
    public function generateInvitationCode(): string
    {
        do {
            $code = str_pad(random_int(10000000, 99999999), 8, '0', STR_PAD_LEFT);
        } while (User::where('invitation_code', $code)->exists());

        return $code;
    }

    /**
     * Get parent hierarchy up to 5 levels for building user hierarchy.
     */
    public function getParentHierarchy(int $userId): array
    {
        return DB::select("
            WITH RECURSIVE ParentHierarchy AS (
                -- Base case: immediate parent
                SELECT
                    id,
                    parent_id,
                    1 as level
                FROM users
                WHERE id = ?

                UNION ALL

                -- Recursive case: parent's parent up to level 3
                SELECT
                    u.id,
                    u.parent_id,
                    ph.level + 1
                FROM users u
                INNER JOIN ParentHierarchy ph ON u.id = ph.parent_id
                WHERE ph.level < ?
            )
            SELECT id, parent_id, level
            FROM ParentHierarchy
            ORDER BY level ASC
        ", [$userId, SystemSettingService::getSetting('max_sub_level', 5)]);
    }

    /**
     * Build hierarchy string from hierarchy data.
     */
    public function buildHierarchy(?User $user, array $parentHierarchy = []): string
    {
        // If we have parent hierarchy, process it
        if (!empty($parentHierarchy)) {
            return collect($parentHierarchy)
                ->filter(fn ($parent) => !is_null($parent->parent_id))
                ->pluck('parent_id')
                ->whenNotEmpty(
                    fn ($ids) => $ids->implode('|') . '|',
                    fn () => $this->getDefaultHierarchy($user)
                );
        }

        return $this->getDefaultHierarchy($user);
    }

    private function getDefaultHierarchy(?User $user): string
    {
        return $user?->parent_id ? "{$user->parent_id}|" : '';
    }
}
