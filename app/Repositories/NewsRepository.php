<?php

namespace App\Repositories;

use App\Contracts\HandlesFileUploadInterface;
use App\Models\News;
use App\Services\UploadService;
use App\Traits\HandlesFileUpload;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class NewsRepository implements HandlesFileUploadInterface
{
    use HandlesFileUpload;

    protected UploadService $uploadService;

    public function __construct(UploadService $uploadService)
    {
        $this->uploadService = $uploadService;
    }

    /**
     * Get the UploadService instance
     */
    public function getUploadService(): UploadService
    {
        return $this->uploadService;
    }

    /**
     * Upload a file specifically for news and return the full storage path
     *
     * @param UploadedFile $file The file to upload
     * @param bool $allowDuplicate Whether to allow duplicate files
     * @return string The full storage path (e.g., /storage/uploads/news/filename.ext)
     *
     * @throws \Exception If upload fails
     */
    public function uploadNewsFile(UploadedFile $file, bool $allowDuplicate = false): string
    {
        // Create a custom upload instance for news files
        $uploadService = \App\Services\LarkeOverWrite\Upload::create();

        // Set custom directory for news files
        $uploadService->dir('news');

        // Generate unique filename
        $uploadService->uniqueName();

        try {
            // Upload the file
            $path = $uploadService->upload($file);

            if (!$path) {
                throw new \Exception('Failed to upload news file');
            }

            // Return full storage path (e.g., /storage/uploads/news/filename.ext)
            return '/storage/uploads/' . $path;
        } catch (\Exception $e) {
            throw new \Exception('Failed to upload news file: ' . $e->getMessage());
        }
    }

    /**
     * Delete a news file with backward compatibility for old path formats
     *
     * @param string $path The path to delete (supports both old and new formats)
     */
    public function deleteNewsFile(string $path): void
    {
        if (empty($path)) {
            return;
        }

        try {
            $uploadService = \App\Services\LarkeOverWrite\Upload::create();

            // Handle different path formats for backward compatibility
            $pathToDelete = $this->normalizeNewsFilePath($path);

            // Delete the file
            $uploadService->destroy($pathToDelete);
        } catch (\Exception $e) {
            // Log error but don't throw to avoid breaking the application
            \Illuminate\Support\Facades\Log::warning('Failed to delete news file: ' . $e->getMessage(), [
                'path' => $path,
                'normalized_path' => $pathToDelete ?? null,
            ]);
        }
    }

    /**
     * Normalize file path for deletion (handles old and new formats)
     *
     * @param string $path The original path
     * @return string The normalized path for deletion
     */
    private function normalizeNewsFilePath(string $path): string
    {
        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Handle old format: /storage/uploads/... or storage/uploads/...
        if (str_starts_with($path, 'storage/uploads/')) {
            return substr($path, 8); // Remove 'storage/' prefix, keep 'uploads/...'
        }

        // Handle old format: /storage/...
        if (str_starts_with($path, 'storage/')) {
            return substr($path, 8); // Remove 'storage/' prefix
        }

        // Handle new format: uploads/news/... (already correct)
        if (str_starts_with($path, 'uploads/')) {
            return $path;
        }

        // Handle legacy formats: images/..., files/..., medias/...
        if (preg_match('/^(images|files|medias)\//', $path)) {
            return $path;
        }

        // If path doesn't match any known format, return as-is
        return $path;
    }

    /**
     * Get paginated news
     */
    public function paginate(int $perPage = 10, ?string $locale = null): LengthAwarePaginator
    {
        $query = News::query();

        // If locale is specified, filter news that have content in that locale
        if ($locale) {
            $query->whereHas('translations', function ($q) use ($locale) {
                $q->where('locale', $locale)
                    ->whereIn('field_key', ['title', 'description', 'content']);
            });
        }

        $paginatedResult = $query->orderBy('created_at', 'DESC')->paginate($perPage);

        // If locale is specified, transform the data to return localized content
        if ($locale) {
            $paginatedResult->getCollection()->transform(function ($news) use ($locale) {
                return [
                    'id' => $news->id,
                    'author' => $news->author,
                    'thumbnail' => $news->getThumbnailUrl(), // Handles both old and new formats
                    'active' => $news->active,
                    'title' => $news->getTitle($locale),
                    'description' => $news->getDescription($locale),
                    'content' => $news->getContent($locale),
                    'created_at' => $news->created_at,
                    'updated_at' => $news->updated_at,
                    // Include translations object for frontend compatibility
                    'translations' => [
                        'title' => $news->getTranslations('title'),
                        'description' => $news->getTranslations('description'),
                        'content' => $news->getTranslations('content'),
                    ],
                ];
            });
        } else {
            // Return with all translations for no specific locale
            $paginatedResult->getCollection()->transform(function ($news) {
                return $news->toArrayWithTranslations();
            });
        }

        return $paginatedResult;
    }

    /**
     * Find news by ID
     */
    public function findById(int $id): ?News
    {
        return News::find($id);
    }

    /**
     * Create new news with translations
     */
    public function create(array $data): News
    {
        $news = News::create([
            'author' => $data['author'] ?? null,
            'thumbnail' => $data['thumbnail'] ?? null,
            'active' => $data['active'] ?? true,
            'title' => $data['title']['en'] ?? $data['title'] ?? '',
            'description' => $data['description']['en'] ?? $data['description'] ?? null,
            'content' => $data['content']['en'] ?? $data['content'] ?? null,
        ]);

        // Only set translations if the news was created successfully and has an ID
        if ($news && $news->id) {
            $news->updateTranslationsFromRequest($data);
        }

        return $news;
    }

    /**
     * Update news with translations
     */
    public function update(News $news, array $data): News
    {
        // Prepare data for main model fields (non-translatable fields + English content)
        $updateData = [];

        if (isset($data['author'])) {
            $updateData['author'] = $data['author'];
        }

        if (isset($data['active'])) {
            $updateData['active'] = $data['active'];
        }

        if (isset($data['thumbnail'])) {
            $updateData['thumbnail'] = $data['thumbnail'];
        }

        // Handle translatable fields - extract English content for main model
        if (isset($data['title'])) {
            if (is_array($data['title']) && isset($data['title']['en'])) {
                $updateData['title'] = $data['title']['en'];
            } elseif (is_string($data['title'])) {
                $updateData['title'] = $data['title'];
            }
        }

        if (isset($data['description'])) {
            if (is_array($data['description']) && isset($data['description']['en'])) {
                $updateData['description'] = $data['description']['en'];
            } elseif (is_string($data['description'])) {
                $updateData['description'] = $data['description'];
            }
        }

        if (isset($data['content'])) {
            if (is_array($data['content']) && isset($data['content']['en'])) {
                $updateData['content'] = $data['content']['en'];
            } elseif (is_string($data['content'])) {
                $updateData['content'] = $data['content'];
            }
        }

        // Update main model fields first
        if (!empty($updateData)) {
            $news->update($updateData);
        }

        // Handle translations for all languages (including creating new ones)
        // This must be done after the main model is updated to ensure we have a valid ID
        if ($news->id) {
            $news->updateTranslationsFromRequest($data);
        }

        // Return the updated model (no need to refresh since updateTranslationsFromRequest saves)
        return $news;
    }

    /**
     * Update news active status
     */
    public function updateActive(News $news, bool $active): News
    {
        $news->active = $active;
        $news->save();

        return $news;
    }

    /**
     * Delete news
     */
    public function delete(News $news): bool
    {
        return $news->delete();
    }

    /**
     * Set translations for a news
     */
    private function setTranslations(News $news, array $data): void
    {
        // Ensure the news model has an ID before setting translations
        if (!$news || !$news->id) {
            return;
        }

        // Set title translations
        if (isset($data['title']) && is_array($data['title'])) {
            foreach ($data['title'] as $locale => $title) {
                if (is_string($title) && trim($title) !== '') {
                    $news->setTranslation('title', $locale, $title);
                }
            }
        }

        // Set description translations
        if (isset($data['description']) && is_array($data['description'])) {
            foreach ($data['description'] as $locale => $description) {
                if (is_string($description) && trim($description) !== '') {
                    $news->setTranslation('description', $locale, $description);
                }
            }
        }

        // Set content translations
        if (isset($data['content']) && is_array($data['content'])) {
            foreach ($data['content'] as $locale => $content) {
                if (is_string($content) && trim($content) !== '') {
                    $news->setTranslation('content', $locale, $content);
                }
            }
        }
    }

    /**
     * Upload thumbnail for news
     *
     * @return string The path to the uploaded file (full storage path: /storage/uploads/news/filename.ext)
     */
    public function uploadThumbnail(News $news, UploadedFile $file): string
    {
        // Delete existing thumbnail if it exists
        if ($news->thumbnail) {
            $this->deleteThumbnail($news);
        }

        // Upload file using new news-specific upload method
        $path = $this->uploadNewsFile($file, false);

        // Update news with new thumbnail path (full storage path)
        $news->update(['thumbnail' => $path]);

        return $path;
    }

    /**
     * Delete thumbnail for news
     */
    public function deleteThumbnail(News $news): bool
    {
        if ($news->thumbnail) {
            // Delete file using new news-specific delete method (handles old and new formats)
            $this->deleteNewsFile($news->thumbnail);

            // Remove thumbnail path from news
            $news->update(['thumbnail' => null]);

            return true;
        }

        return false;
    }

    /**
     * Get the full URL for a news file path (handles both old and new formats)
     *
     * @param string|null $path The file path stored in database
     * @return string|null The full URL or null if path is empty
     */
    public function getNewsFileUrl(?string $path): ?string
    {
        if (empty($path)) {
            return null;
        }

        // If path already starts with /storage/, return as-is (old format)
        if (str_starts_with($path, '/storage/')) {
            return $path;
        }

        // If path starts with http, return as-is (full URL)
        if (str_starts_with($path, 'http')) {
            return $path;
        }

        // For new format (relative path), prepend /storage/
        return '/storage/' . ltrim($path, '/');
    }

    /**
     * Migrate existing thumbnail paths to new format (utility method)
     * This method can be used to update existing news thumbnails to the new path format
     *
     * @param bool $dryRun If true, only shows what would be updated without making changes
     * @return array Summary of migration results
     */
    public function migrateThumbnailPaths(bool $dryRun = true): array
    {
        $results = [
            'total_checked' => 0,
            'needs_migration' => 0,
            'migrated' => 0,
            'errors' => [],
            'dry_run' => $dryRun,
        ];

        $newsWithThumbnails = News::whereNotNull('thumbnail')
            ->where('thumbnail', '!=', '')
            ->get();

        $results['total_checked'] = $newsWithThumbnails->count();

        foreach ($newsWithThumbnails as $news) {
            // Check if thumbnail needs migration (old format)
            if (str_starts_with($news->thumbnail, '/storage/')) {
                $results['needs_migration']++;

                if (!$dryRun) {
                    try {
                        // Convert old format to new format
                        $oldPath = $news->thumbnail;
                        $newPath = $this->convertOldPathToNew($oldPath);

                        $news->update(['thumbnail' => $newPath]);
                        $results['migrated']++;
                    } catch (\Exception $e) {
                        $results['errors'][] = [
                            'news_id' => $news->id,
                            'old_path' => $oldPath,
                            'error' => $e->getMessage(),
                        ];
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Convert old thumbnail path format to new format
     *
     * @param string $oldPath Old path format (e.g., /storage/uploads/images/filename.ext)
     * @return string New path format (e.g., /storage/uploads/news/filename.ext)
     */
    private function convertOldPathToNew(string $oldPath): string
    {
        // Extract filename from old path
        $filename = basename($oldPath);

        // Return new format with /storage/ prefix
        return '/storage/uploads/news/' . $filename;
    }

    // Legacy methods for backward compatibility
    public static function getList()
    {
        return News::query()->orderBy('created_at', 'DESC');
    }

    public static function getNews(string $newsId): ?News
    {
        return News::where('id', $newsId)->first();
    }
}
