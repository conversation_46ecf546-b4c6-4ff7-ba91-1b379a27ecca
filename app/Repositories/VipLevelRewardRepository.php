<?php

namespace App\Repositories;

use App\Models\VipLevelReward;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class VipLevelRewardRepository
{
    protected VipLevelReward $model;

    public function __construct(VipLevelReward $model)
    {
        $this->model = $model;
    }

    /**
     * Get paginated list of VIP level rewards
     *
     * @param int $pageSize Number of items per page
     */
    public function paginate(int $pageSize = 10): LengthAwarePaginator
    {
        return $this->model->query()
            ->with(['user', 'vipLevel'])
            ->orderBy('created_at', 'desc')
            ->paginate($pageSize);
    }

    /**
     * Get paginated list of VIP level rewards with filters
     *
     * @param int $pageSize Number of items per page
     * @param array $filters Filter parameters
     */
    public function paginateWithFilters(int $pageSize = 10, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query()
            ->with(['user:id,name,email', 'vipLevel:id,name,level_order']);

        // Apply filters only if they have valid values
        $this->applyFilters($query, $filters);

        return $query->orderBy('vip_level_rewards.created_at', 'desc')->paginate($pageSize);
    }

    /**
     * Apply filters to the query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     */
    protected function applyFilters($query, array $filters): void
    {
        // Filter by user ID
        if (isset($filters['user_id'])) {
            $query->where('vip_level_rewards.user_id', $filters['user_id']);
        }

        // Filter by VIP level ID
        if (isset($filters['vip_level_id'])) {
            $query->where('vip_level_rewards.vip_level_id', $filters['vip_level_id']);
        }

        // Filter by user name (case-insensitive partial match)
        if (isset($filters['user_name'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['user_name'] . '%');
            });
        }

        // filter by email
        if (isset($filters['email'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('email', 'like', '%' . $filters['email'] . '%');
            });
        }

        // Filter by VIP level name (case-insensitive partial match)
        if (isset($filters['vip_level_name'])) {
            $query->whereHas('vipLevel', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['vip_level_name'] . '%');
            });
        }

        // Filter by transaction code (case-insensitive partial match)
        if (isset($filters['transaction_code'])) {
            $query->where('vip_level_rewards.transaction_code', 'like', '%' . $filters['transaction_code'] . '%');
        }

        // Filter by date range
        if (isset($filters['date_from'])) {
            $query->whereDate('vip_level_rewards.claimed_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('vip_level_rewards.claimed_at', '<=', $filters['date_to']);
        }
    }

    /**
     * Find VIP level reward by ID with relationships
     */
    public function findWithRelations(int $id, array $relations = ['user', 'vipLevel']): ?VipLevelReward
    {
        return $this->model->with($relations)->find($id);
    }

    /**
     * Find VIP level reward by ID
     */
    public function find(int $id): ?VipLevelReward
    {
        return $this->model->find($id);
    }

    /**
     * Get all VIP level rewards ordered by created_at
     */
    public function getAll(): Collection
    {
        return $this->model->with(['user', 'vipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if VIP level reward exists by ID
     */
    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * Get rewards by user ID
     */
    public function getByUserId(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
            ->with(['vipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get rewards by VIP level ID
     */
    public function getByVipLevelId(int $vipLevelId): Collection
    {
        return $this->model->where('vip_level_id', $vipLevelId)
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
