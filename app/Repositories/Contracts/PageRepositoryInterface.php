<?php

namespace App\Repositories\Contracts;

use App\Models\Page;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;

interface PageRepositoryInterface
{
    /**
     * Get paginated pages
     */
    public function paginate(int $perPage = 10, ?string $locale = null): LengthAwarePaginator;

    /**
     * Find page by ID
     */
    public function findById(int $id): ?Page;

    /**
     * Create new page with translations
     */
    public function create(array $data): Page;

    /**
     * Update page with translations
     */
    public function update(Page $page, array $data): Page;

    /**
     * Update page active status
     */
    public function updateActive(Page $page, bool $active): Page;

    /**
     * Delete page
     */
    public function delete(Page $page): bool;

    /**
     * Upload thumbnail for page
     *
     * @return string The path to the uploaded file
     */
    public function uploadThumbnail(Page $page, UploadedFile $file): string;

    /**
     * Delete thumbnail for page
     */
    public function deleteThumbnail(Page $page): bool;
}
