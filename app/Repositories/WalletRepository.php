<?php

namespace App\Repositories;

use App\Constants\WalletType;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

readonly class WalletRepository
{
    public function __construct(private Wallet $wallet)
    {
        //
    }

    public static function getWallet($walletId): ?Wallet
    {
        return Wallet::find($walletId);
    }

    public function createWallets(User $user): bool
    {
        DB::beginTransaction();
        try {
            $now = now();
            $walletData = [];

            // Create wallet data for each wallet type
            foreach (WalletType::cases() as $walletType) {
                $walletData[] = $this->createWalletData($user->id, $walletType, $now);
            }

            $this->wallet->insert($walletData);
            DB::commit();

            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception->getMessage());
        }

        return false;
    }

    public static function getWalletByUser(int $userId, string $type): ?Wallet
    {
        return Wallet::where('user_id', $userId)->where('type', $type)->first();
    }

    public static function getUserWallets(int $userId): Collection
    {
        return Wallet::where('user_id', $userId)->get();
    }

    private function createWalletData(int $userId, WalletType $type, string $now): array
    {
        return [
            'user_id' => $userId,
            'type' => $type->value,
            'active' => 1,
            'created_at' => $now,
            'updated_at' => $now,
        ];
    }
}
