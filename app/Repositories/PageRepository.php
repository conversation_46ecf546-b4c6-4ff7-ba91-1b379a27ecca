<?php

namespace App\Repositories;

use App\Contracts\HandlesFileUploadInterface;
use App\Models\Page;
use App\Repositories\Contracts\PageRepositoryInterface;
use App\Services\UploadService;
use App\Traits\HandlesFileUpload;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class PageRepository implements HandlesFileUploadInterface, PageRepositoryInterface
{
    use HandlesFileUpload;

    protected UploadService $uploadService;

    public function __construct(UploadService $uploadService)
    {
        $this->uploadService = $uploadService;
    }

    /**
     * Get the UploadService instance
     */
    public function getUploadService(): UploadService
    {
        return $this->uploadService;
    }

    /**
     * Upload a file specifically for pages and return the full storage path
     *
     * @param UploadedFile $file The file to upload
     * @param bool $allowDuplicate Whether to allow duplicate files
     * @return string The full storage path (e.g., /storage/uploads/pages/filename.ext)
     *
     * @throws \Exception If upload fails
     */
    public function uploadPageFile(UploadedFile $file, bool $allowDuplicate = false): string
    {
        // Create a custom upload instance for page files
        $uploadService = \App\Services\LarkeOverWrite\Upload::create();

        // Set custom directory for page files
        $uploadService->dir('pages');

        // Generate unique filename
        $uploadService->uniqueName();

        try {
            // Upload the file
            $path = $uploadService->upload($file);

            if (!$path) {
                throw new \Exception('Failed to upload page file');
            }

            // Return full storage path (e.g., /storage/uploads/pages/filename.ext)
            return '/storage/uploads/' . $path;
        } catch (\Exception $e) {
            throw new \Exception('Failed to upload page file: ' . $e->getMessage());
        }
    }

    /**
     * Delete a page file with backward compatibility for old path formats
     *
     * @param string $path The path to delete (supports both old and new formats)
     */
    public function deletePageFile(string $path): void
    {
        if (empty($path)) {
            return;
        }

        try {
            $uploadService = \App\Services\LarkeOverWrite\Upload::create();

            // Handle different path formats for backward compatibility
            $pathToDelete = $this->normalizePageFilePath($path);

            // Delete the file
            $uploadService->destroy($pathToDelete);
        } catch (\Exception $e) {
            // Log error but don't throw to avoid breaking the application
            \Illuminate\Support\Facades\Log::warning('Failed to delete page file: ' . $e->getMessage(), [
                'path' => $path,
                'normalized_path' => $pathToDelete ?? null,
            ]);
        }
    }

    /**
     * Normalize file path for deletion (handles old and new formats)
     *
     * @param string $path The original path
     * @return string The normalized path for deletion
     */
    private function normalizePageFilePath(string $path): string
    {
        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Handle old format: /storage/uploads/... or storage/uploads/...
        if (str_starts_with($path, 'storage/uploads/')) {
            return substr($path, 8); // Remove 'storage/' prefix, keep 'uploads/...'
        }

        // Handle old format: /storage/...
        if (str_starts_with($path, 'storage/')) {
            return substr($path, 8); // Remove 'storage/' prefix
        }

        // Handle new format: uploads/pages/... (already correct)
        if (str_starts_with($path, 'uploads/')) {
            return $path;
        }

        // Handle legacy formats: images/..., files/..., medias/...
        if (preg_match('/^(images|files|medias)\//', $path)) {
            return $path;
        }

        // If path doesn't match any known format, return as-is
        return $path;
    }

    /**
     * Get paginated pages
     */
    public function paginate(int $perPage = 10, ?string $locale = null): LengthAwarePaginator
    {
        $query = Page::query();

        // If locale is specified, filter pages that have content in that locale
        if ($locale) {
            $query->whereHas('translations', function ($q) use ($locale) {
                $q->where('locale', $locale)
                    ->whereIn('field_key', ['title', 'description', 'content']);
            });
        }

        $paginatedResult = $query->paginate($perPage);

        // If locale is specified, transform the data to return localized content
        if ($locale) {
            $paginatedResult->getCollection()->transform(function ($page) use ($locale) {
                return [
                    'id' => $page->id,
                    'slug' => $page->slug,
                    'thumbnail' => $page->getThumbnailUrl(), // Handles both old and new formats
                    'active' => $page->active,
                    'title' => $page->getTitle($locale),
                    'description' => $page->getDescription($locale),
                    'content' => $page->getContent($locale),
                    'created_at' => $page->created_at,
                    'updated_at' => $page->updated_at,
                    // Include translations object for frontend compatibility
                    'translations' => [
                        'title' => $page->getTranslations('title'),
                        'description' => $page->getTranslations('description'),
                        'content' => $page->getTranslations('content'),
                    ],
                ];
            });
        } else {
            // Return with all translations for no specific locale
            $paginatedResult->getCollection()->transform(function ($page) {
                return $page->toArrayWithTranslations();
            });
        }

        return $paginatedResult;
    }

    /**
     * Find page by ID
     */
    public function findById(int $id): ?Page
    {
        return Page::find($id);
    }

    /**
     * Create new page with translations
     */
    public function create(array $data): Page
    {
        $page = Page::create([
            'slug' => $data['slug'],
            'thumbnail' => $data['thumbnail'] ?? null,
            'active' => $data['active'] ?? true,
            'title' => $data['title']['en'] ?? $data['title'] ?? '',
            'description' => $data['description']['en'] ?? $data['description'] ?? null,
            'content' => $data['content']['en'] ?? $data['content'] ?? null,
        ]);

        // Only set translations if the page was created successfully and has an ID
        if ($page && $page->id) {
            $page->updateTranslationsFromRequest($data);
        }

        return $page;
    }

    /**
     * Update page with translations
     */
    public function update(Page $page, array $data): Page
    {
        // Prepare data for main model fields (non-translatable fields + English content)
        $updateData = [];

        if (isset($data['slug'])) {
            $updateData['slug'] = $data['slug'];
        }

        if (isset($data['active'])) {
            $updateData['active'] = $data['active'];
        }

        if (isset($data['thumbnail'])) {
            $updateData['thumbnail'] = $data['thumbnail'];
        }

        // Handle translatable fields - extract English content for main model
        if (isset($data['title'])) {
            if (is_array($data['title']) && isset($data['title']['en'])) {
                $updateData['title'] = $data['title']['en'];
            } elseif (is_string($data['title'])) {
                $updateData['title'] = $data['title'];
            }
        }

        if (isset($data['description'])) {
            if (is_array($data['description']) && isset($data['description']['en'])) {
                $updateData['description'] = $data['description']['en'];
            } elseif (is_string($data['description'])) {
                $updateData['description'] = $data['description'];
            }
        }

        if (isset($data['content'])) {
            if (is_array($data['content']) && isset($data['content']['en'])) {
                $updateData['content'] = $data['content']['en'];
            } elseif (is_string($data['content'])) {
                $updateData['content'] = $data['content'];
            }
        }

        // Update main model fields first
        if (!empty($updateData)) {
            $page->update($updateData);
        }

        // Handle translations for all languages (including creating new ones)
        // This must be done after the main model is updated to ensure we have a valid ID
        if ($page->id) {
            $page->updateTranslationsFromRequest($data);
        }

        // Return the updated model
        return $page;
    }

    /**
     * Update page active status
     */
    public function updateActive(Page $page, bool $active): Page
    {
        $page->active = $active;
        $page->save();

        return $page;
    }

    /**
     * Delete page
     */
    public function delete(Page $page): bool
    {
        return $page->delete();
    }

    /**
     * Upload thumbnail for page
     *
     * @return string The path to the uploaded file (full storage path: /storage/uploads/pages/filename.ext)
     */
    public function uploadThumbnail(Page $page, UploadedFile $file): string
    {
        // Delete existing thumbnail if it exists
        if ($page->thumbnail) {
            $this->deleteThumbnail($page);
        }

        // Upload file using new page-specific upload method
        $path = $this->uploadPageFile($file, false);

        // Update page with new thumbnail path (full storage path)
        $page->update(['thumbnail' => $path]);

        return $path;
    }

    /**
     * Delete thumbnail for page
     */
    public function deleteThumbnail(Page $page): bool
    {
        if ($page->thumbnail) {
            // Delete file using new page-specific delete method (handles old and new formats)
            $this->deletePageFile($page->thumbnail);

            // Remove thumbnail path from page
            $page->update(['thumbnail' => null]);

            return true;
        }

        return false;
    }

    /**
     * Get the full URL for a page file path (handles both old and new formats)
     *
     * @param string|null $path The file path stored in database
     * @return string|null The full URL or null if path is empty
     */
    public function getPageFileUrl(?string $path): ?string
    {
        if (empty($path)) {
            return null;
        }

        // If path already starts with /storage/, return as-is (old format)
        if (str_starts_with($path, '/storage/')) {
            return $path;
        }

        // If path starts with http, return as-is (full URL)
        if (str_starts_with($path, 'http')) {
            return $path;
        }

        // For new format (relative path), prepend /storage/
        return '/storage/' . ltrim($path, '/');
    }

    /**
     * Migrate existing thumbnail paths to new format (utility method)
     * This method can be used to update existing page thumbnails to the new path format
     *
     * @param bool $dryRun If true, only shows what would be updated without making changes
     * @return array Summary of migration results
     */
    public function migrateThumbnailPaths(bool $dryRun = true): array
    {
        $results = [
            'total_checked' => 0,
            'needs_migration' => 0,
            'migrated' => 0,
            'errors' => [],
            'dry_run' => $dryRun,
        ];

        $pagesWithThumbnails = Page::whereNotNull('thumbnail')
            ->where('thumbnail', '!=', '')
            ->get();

        $results['total_checked'] = $pagesWithThumbnails->count();

        foreach ($pagesWithThumbnails as $page) {
            // Check if thumbnail needs migration (old format)
            if (str_starts_with($page->thumbnail, '/storage/')) {
                $results['needs_migration']++;

                if (!$dryRun) {
                    try {
                        // Convert old format to new format
                        $oldPath = $page->thumbnail;
                        $newPath = $this->convertOldPathToNew($oldPath);

                        $page->update(['thumbnail' => $newPath]);
                        $results['migrated']++;
                    } catch (\Exception $e) {
                        $results['errors'][] = [
                            'page_id' => $page->id,
                            'old_path' => $oldPath,
                            'error' => $e->getMessage(),
                        ];
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Convert old thumbnail path format to new format
     *
     * @param string $oldPath Old path format (e.g., /storage/uploads/images/filename.ext)
     * @return string New path format (e.g., /storage/uploads/pages/filename.ext)
     */
    private function convertOldPathToNew(string $oldPath): string
    {
        // Extract filename from old path
        $filename = basename($oldPath);

        // Return new format with /storage/ prefix
        return '/storage/uploads/pages/' . $filename;
    }
}
