<?php

namespace App\Repositories;

use App\Models\VipLevelHistory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class VipLevelHistoryRepository
{
    protected VipLevelHistory $model;

    public function __construct(VipLevelHistory $model)
    {
        $this->model = $model;
    }

    /**
     * Get paginated list of VIP level history
     *
     * @param int $pageSize Number of items per page
     */
    public function paginate(int $pageSize = 10): LengthAwarePaginator
    {
        return $this->model->query()
            ->with(['user', 'oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->paginate($pageSize);
    }

    /**
     * Get paginated list of VIP level history with filters
     *
     * @param int $pageSize Number of items per page
     * @param array $filters Filter parameters
     */
    public function paginateWithFilters(int $pageSize = 10, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query()
            ->with(['user:id,name,email', 'oldVipLevel:id,name,level_order', 'newVipLevel:id,name,level_order']);

        // Apply filters only if they have valid values
        $this->applyFilters($query, $filters);

        return $query->orderBy('vip_level_histories.created_at', 'desc')->paginate($pageSize);
    }

    /**
     * Apply filters to the query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     */
    protected function applyFilters($query, array $filters): void
    {
        // Filter by user ID
        if (isset($filters['user_id'])) {
            $query->where('vip_level_histories.user_id', $filters['user_id']);
        }

        // Filter by old VIP level ID
        if (isset($filters['old_vip_level_id'])) {
            $query->where('vip_level_histories.old_vip_level_id', $filters['old_vip_level_id']);
        }

        // Filter by new VIP level ID
        if (isset($filters['new_vip_level_id'])) {
            $query->where('vip_level_histories.new_vip_level_id', $filters['new_vip_level_id']);
        }

        // Filter by trigger event
        if (isset($filters['trigger_event'])) {
            $query->where('vip_level_histories.trigger_event', $filters['trigger_event']);
        }

        // Filter by action type
        if (isset($filters['action_type'])) {
            $query->where('vip_level_histories.action_type', $filters['action_type']);
        }

        // Filter by user name (case-insensitive partial match)
        if (isset($filters['user_name'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['user_name'] . '%');
            });
        }

        // filter by email
        if (isset($filters['email'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('email', 'like', '%' . $filters['email'] . '%');
            });
        }

        // Filter by old VIP level name (case-insensitive partial match)
        if (isset($filters['old_vip_level_name'])) {
            $query->whereHas('oldVipLevel', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['old_vip_level_name'] . '%');
            });
        }

        // Filter by new VIP level name (case-insensitive partial match)
        if (isset($filters['new_vip_level_name'])) {
            $query->whereHas('newVipLevel', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['new_vip_level_name'] . '%');
            });
        }

        // Filter by date range
        if (isset($filters['date_from'])) {
            $query->whereDate('vip_level_histories.created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('vip_level_histories.created_at', '<=', $filters['date_to']);
        }
    }

    /**
     * Find VIP level history by ID with relationships
     */
    public function findWithRelations(int $id, array $relations = ['user', 'oldVipLevel', 'newVipLevel']): ?VipLevelHistory
    {
        return $this->model->with($relations)->find($id);
    }

    /**
     * Find VIP level history by ID
     */
    public function find(int $id): ?VipLevelHistory
    {
        return $this->model->find($id);
    }

    /**
     * Get all VIP level history ordered by created_at
     */
    public function getAll(): Collection
    {
        return $this->model->with(['user', 'oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if VIP level history exists by ID
     */
    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * Get history by user ID
     */
    public function getByUserId(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
            ->with(['oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get history by VIP level ID (old or new)
     */
    public function getByVipLevelId(int $vipLevelId): Collection
    {
        return $this->model->where(function ($query) use ($vipLevelId) {
            $query->where('old_vip_level_id', $vipLevelId)
                ->orWhere('new_vip_level_id', $vipLevelId);
        })
            ->with(['user', 'oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get history by trigger event
     */
    public function getByTriggerEvent(string $triggerEvent): Collection
    {
        return $this->model->where('trigger_event', $triggerEvent)
            ->with(['user', 'oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get recent history for a user
     */
    public function getRecentByUserId(int $userId, int $limit = 10): Collection
    {
        return $this->model->where('user_id', $userId)
            ->with(['oldVipLevel', 'newVipLevel'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
