<?php

namespace App\Repositories;

use App\Models\VipLevel;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class VipLevelRepository
{
    protected VipLevel $model;

    public function __construct(VipLevel $model)
    {
        $this->model = $model;
    }

    /**
     * Get paginated list of VIP levels
     *
     * @param int $pageSize Number of items per page
     * @param string|null $locale Optional locale for filtering and localized content
     */
    public function paginate(int $pageSize = 10, ?string $locale = null): LengthAwarePaginator
    {
        $query = $this->model->query()->with('withdrawSettings');

        // If locale is specified, filter VIP levels that have content in that locale
        if ($locale) {
            $query->whereHas('translations', function ($q) use ($locale) {
                $q->where('locale', $locale)
                    ->whereIn('field_key', ['name', 'description']);
            });
        }

        $paginatedResult = $query->orderBy('level_order', 'asc')->paginate($pageSize);

        // If locale is specified, transform the data to return localized content
        if ($locale) {
            $paginatedResult->getCollection()->transform(function ($vipLevel) use ($locale) {
                return [
                    'id' => $vipLevel->id,
                    'name' => $vipLevel->getName($locale),
                    'description' => $vipLevel->getDescription($locale),
                    'level_order' => $vipLevel->level_order,
                    'discount' => $vipLevel->discount,
                    'withdraw_fee' => $vipLevel->withdraw_fee,
                    'min_withdraw_amount' => $vipLevel->min_withdraw_amount,
                    'contribute_amount_min' => $vipLevel->contribute_amount_min,
                    'contribute_amount_max' => $vipLevel->contribute_amount_max,
                    'contribute_amount_fixed' => $vipLevel->contribute_amount_fixed,
                    'profit_rate_bonus' => $vipLevel->profit_rate_bonus,
                    'level_up_reward' => $vipLevel->level_up_reward,
                    'created_at' => $vipLevel->created_at,
                    'updated_at' => $vipLevel->updated_at,
                    'withdraw_settings' => $vipLevel->withdrawSettings,
                    // Include translations object for frontend compatibility
                    'translations' => [
                        'name' => $vipLevel->getTranslations('name'),
                        'description' => $vipLevel->getTranslations('description'),
                    ],
                ];
            });
        } else {
            // Return with all translations for no specific locale
            $paginatedResult->getCollection()->transform(function ($vipLevel) {
                return $vipLevel->toArrayWithTranslations();
            });
        }

        return $paginatedResult;
    }

    /**
     * Find VIP level by ID with relationships
     */
    public function findWithRelations(int $id, array $relations = ['condition', 'activeCondition']): ?VipLevel
    {
        return $this->model->with($relations)->find($id);
    }

    /**
     * Find VIP level by ID
     */
    public function find(int $id): ?VipLevel
    {
        return $this->model->find($id);
    }

    /**
     * Create a new VIP level with translations
     */
    public function create(array $data): VipLevel
    {
        $vipLevel = $this->model->create([
            'name' => $data['name']['en'] ?? $data['name'] ?? '',
            'description' => $data['description']['en'] ?? $data['description'] ?? null,
            'level_order' => $data['level_order'] ?? null,
            'discount' => $data['discount'],
            'withdraw_fee' => $data['withdraw_fee'],
            'min_withdraw_amount' => $data['min_withdraw_amount'],
            'contribute_amount_min' => $data['contribute_amount_min'] ?? null,
            'contribute_amount_max' => $data['contribute_amount_max'] ?? null,
            'contribute_amount_fixed' => $data['contribute_amount_fixed'] ?? null,
            'profit_rate_bonus' => $data['profit_rate_bonus'],
            'level_up_reward' => $data['level_up_reward'],
        ]);

        // Only set translations if the VIP level was created successfully and has an ID
        if ($vipLevel && $vipLevel->id) {
            $vipLevel->updateTranslationsFromRequest($data);
        }

        return $vipLevel;
    }

    /**
     * Update VIP level with translations
     */
    public function update(VipLevel $vipLevel, array $data): VipLevel
    {
        // Prepare data for main model fields (non-translatable fields + English content)
        $updateData = [];

        if (isset($data['level_order'])) {
            $updateData['level_order'] = $data['level_order'];
        }

        if (isset($data['discount'])) {
            $updateData['discount'] = $data['discount'];
        }

        if (isset($data['withdraw_fee'])) {
            $updateData['withdraw_fee'] = $data['withdraw_fee'];
        }

        if (isset($data['min_withdraw_amount'])) {
            $updateData['min_withdraw_amount'] = $data['min_withdraw_amount'];
        }

        if (isset($data['contribute_amount_min'])) {
            $updateData['contribute_amount_min'] = $data['contribute_amount_min'];
        }

        if (isset($data['contribute_amount_max'])) {
            $updateData['contribute_amount_max'] = $data['contribute_amount_max'];
        }

        if (isset($data['contribute_amount_fixed'])) {
            $updateData['contribute_amount_fixed'] = $data['contribute_amount_fixed'];
        }

        if (isset($data['profit_rate_bonus'])) {
            $updateData['profit_rate_bonus'] = $data['profit_rate_bonus'];
        }

        if (isset($data['investment_profit_rebate_rate'])) {
            $updateData['investment_profit_rebate_rate'] = $data['investment_profit_rebate_rate'];
        }

        if (isset($data['level_up_reward'])) {
            $updateData['level_up_reward'] = $data['level_up_reward'];
        }

        // Handle translatable fields - extract English content for main model
        if (isset($data['name'])) {
            if (is_array($data['name']) && isset($data['name']['en'])) {
                $updateData['name'] = $data['name']['en'];
            } elseif (is_string($data['name'])) {
                $updateData['name'] = $data['name'];
            }
        }

        if (isset($data['description'])) {
            if (is_array($data['description']) && isset($data['description']['en'])) {
                $updateData['description'] = $data['description']['en'];
            } elseif (is_string($data['description'])) {
                $updateData['description'] = $data['description'];
            }
        }

        // Update the main model with non-translatable fields and English content
        if (!empty($updateData)) {
            $vipLevel->update($updateData);
        }

        // Handle translations for all languages (including creating new ones)
        // This must be done after the main model is updated to ensure we have a valid ID
        if ($vipLevel->id) {
            $vipLevel->updateTranslationsFromRequest($data);
        }

        // Return the updated model
        return $vipLevel;
    }

    /**
     * Delete VIP level
     */
    public function delete(VipLevel $vipLevel): bool
    {
        return $vipLevel->delete();
    }

    /**
     * Get all VIP levels ordered by level_order
     */
    public function getAll(): Collection
    {
        return $this->model->orderBy('level_order', 'asc')->get();
    }

    /**
     * Check if VIP level exists by ID
     */
    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * Get VIP level with fresh data from database
     */
    public function fresh(VipLevel $vipLevel): VipLevel
    {
        return $vipLevel->fresh();
    }

    /**
     * Check if VIP level name is unique (excluding given ID)
     */
    public function isNameUnique(string $name, ?int $excludeId = null): bool
    {
        $query = $this->model->where('name', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Check if level order is unique (excluding given ID)
     */
    public function isLevelOrderUnique(int $levelOrder, ?int $excludeId = null): bool
    {
        $query = $this->model->where('level_order', $levelOrder);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }
}
