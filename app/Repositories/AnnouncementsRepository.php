<?php

namespace App\Repositories;

use App\Models\Announcement;

class AnnouncementsRepository
{
    public static function create(array $data): ?Announcement
    {
        return Announcement::create($data);
    }

    public static function update(array $data, Announcement $model): bool
    {
        return $model->update($data);
    }

    public static function getList()
    {
        return Announcement::orderBy('created_at', 'DESC')->with(['user']);
    }

    public static function getAnnouncement(string $newsId): ?Announcement
    {
        return Announcement::where('id', $newsId)->with(['user'])->first();
    }
}
