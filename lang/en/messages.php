<?php

return [
    'success' => 'Success',
    'unknown_error' => 'Unknown error',
    'please_refresh_page_and_try_again' => 'Please refresh the page and try again',
    'invalid_invitation_code' => 'Invalid invitation code 1',
    'invalid_credentials' => 'Invalid credentials',
    'inactive_account' => 'Account is inactive',
    'registered_successfully' => 'User registered successfully',
    'password_changed' => 'Password changed successfully',
    'invalid_current_password' => 'Invalid current password',
    'identity_verified' => 'Identity verified successfully',
    'insufficient_balance' => 'Insufficient Balance',
    'invalid_amount' => 'Invalid amount',
    'unsupported_transfer' => 'Unsupported transfer Wallet',
    'invalid_captcha' => 'Invalid captcha',
    'out_of_stock' => 'Out of stock',
    'try_again' => 'Please try again',
    'duplicated_order' => 'Duplicated order',
    'invalid_current_passcode' => 'Invalid current transaction passcode',
    'passcode_changed' => 'Transaction passcode changed successfully',
    'failed_to_verify_identity' => 'Failed to verify identity',
    'force_bypass_id_verification_success' => 'Force bypass id verification success',
    'rollback_withdraw' => 'Rollback withdraw :amount',
    'bypass_face_auth_enabled' => 'Bypass face authentication enabled successfully',
    'bypass_face_auth_disabled' => 'Bypass face authentication disabled successfully',
    'wallet_types' => [
        'general' => 'general',
        'saudi' => 'saudi',
        'commission' => 'commission',
    ],
    'withdraw_status' => [
        'Approved' => 'Approved',
        'Pending' => 'Pending',
        'Rejected' => 'Rejected',
    ],
    'withdrawal_result_notification' => 'Your withdrawal request: :wallet, amount: :amount, :status',
    'withdrawal_request' => 'Withdrawal request',
    'bank_account_invalid' => 'Bank account invalid',
    'bind_bank_account_failed' => 'Failed to bind bank account',
    'this_bank_account_already_bound_to_this_user' => 'This bank account already bound to this user',
    'failed_to_verify' => 'Failed to verify',
    'remark_approved' => 'Approved loan amount :amount',
    'invalid_loan_request' => 'Invalid loan request',
    'remark' => [
        'operation_request_profit' => 'Operation service profit :amount',
        'operation_service_profit' => ':date Operation profit :net_profit',
        'operation_service_reward' => ':date Operation reward :net_profit',
        'store_daily_profit' => ':store_type :operation_date Business income distribution',
    ],
    'stores' => [
        'title' => 'Type',
        'types' => [
            'micro' => 'Micro Store',
            'medium' => 'Medium Size Store',
            'high_end' => 'High End Store',
            'flagship' => 'Flagship Store',
        ],
    ],
    'reload' => 'This item was outdated. Please reload the page first.',
    'member' => [
        'gold_super_agent_cannot_change' => 'Gold super agent cannot be changed',
        'invalid_agent_type_change' => 'Invalid agent type change',
    ],
    'two_fa' => [
        'have_to_verify_2fa' => 'You have to verify 2FA',
        'verified_2fa' => '2FA verified successfully',
        'invalid_2fa_code' => 'Invalid 2FA code',
    ],
    'vip_level_condition' => [
        'vip_level_not_found' => 'VIP level not found',
        'condition_already_exists' => 'A condition already exists for this VIP level. Only one condition per VIP level is allowed.',
        'failed_to_create' => 'Failed to create VIP level condition',
        'failed_to_update' => 'Failed to update VIP level condition',
        'threshold_conflicts' => [
            'remaining_balance' => 'remaining balance',
            'total_deposit' => 'total deposit',
            'subordinates_balance' => 'subordinates balance',
            'subordinates_deposit' => 'subordinates deposit',
            'complete_conflict' => 'Threshold values conflict with VIP Level',
            'partial_conflict' => 'Threshold conflict detected with VIP Level :vip_level_name in: :conflict_types. This may cause ambiguous VIP level qualification.',
        ],
    ],
    'user_created_successfully' => 'User created successfully',
    'user_creation_failed' => 'User creation failed',
];
