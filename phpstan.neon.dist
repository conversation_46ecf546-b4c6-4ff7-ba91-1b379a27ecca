includes:
    - vendor/larastan/larastan/extension.neon

parameters:

    paths:
        - app/Services/
        - app/Http
        - app/Console/Commands
        - app/Jobs
        - app/Events
        - app/Models
        - app/Observers
        - app/Listeners
        - app/Traits
        - app/Rules

    # Level 9 is the highest level
    level: 5

    excludePaths:
        - app/Providers/*

    checkMissingIterableValueType: false
    treatPhpDocTypesAsCertain: false
