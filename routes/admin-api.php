<?php

use App\Admin\Http\Controllers\ActivityController;
use App\Admin\Http\Controllers\ActivityTransactionController;
use App\Admin\Http\Controllers\AdminController;
use App\Admin\Http\Controllers\AnnouncementController;
use App\Admin\Http\Controllers\Auth\Passport;
use App\Admin\Http\Controllers\BankAccountController;
use App\Admin\Http\Controllers\BannerSliderController;
use App\Admin\Http\Controllers\CategoryController;
use App\Admin\Http\Controllers\CommonController;
use App\Admin\Http\Controllers\ContributeController;
use App\Admin\Http\Controllers\CouponController;
use App\Admin\Http\Controllers\DashboardController;
use App\Admin\Http\Controllers\DepositController;
use App\Admin\Http\Controllers\DepositRequestController;
use App\Admin\Http\Controllers\ExchangeRateController;
use App\Admin\Http\Controllers\FinanceController;
use App\Admin\Http\Controllers\GlobalController;
use App\Admin\Http\Controllers\GroupController;
use App\Admin\Http\Controllers\LocalesController;
use App\Admin\Http\Controllers\LuckySpinController;
use App\Admin\Http\Controllers\MemberController;
use App\Admin\Http\Controllers\NewsController;
use App\Admin\Http\Controllers\NotificationController;
use App\Admin\Http\Controllers\OrderController;
use App\Admin\Http\Controllers\OtpController;
use App\Admin\Http\Controllers\PagesController;
use App\Admin\Http\Controllers\PaymentChannelController;
use App\Admin\Http\Controllers\PaymentOrganizationController;
use App\Admin\Http\Controllers\ProductController;
use App\Admin\Http\Controllers\ProfileController;
use App\Admin\Http\Controllers\PromotionCardController;
use App\Admin\Http\Controllers\RefundController;
use App\Admin\Http\Controllers\RewardController;
use App\Admin\Http\Controllers\SavingReportController;
use App\Admin\Http\Controllers\SettingController;
use App\Admin\Http\Controllers\VipLevelConditionController;
use App\Admin\Http\Controllers\VipLevelController;
use App\Admin\Http\Controllers\VipLevelHistoryController;
use App\Admin\Http\Controllers\VipLevelRewardController;
use App\Admin\Http\Controllers\WithdrawController;
use Illuminate\Support\Facades\Route;

/* overwrite original route */

Route::controller(ProfileController::class)->group(function () {
    Route::put('profile/update', 'update')->name('profile.update');
});

Route::name('passport.')->withoutMiddleware(['auth:larke', 'larke-admin.auth'])->group(function () {
    Route::post('passport/login', [Passport::class, 'login'])->name('login');
    Route::put('passport/refresh-token', [Passport::class, 'refreshToken'])->name('refresh-token');
});
Route::name('passport.')->group(function () {
    Route::delete('passport/logout', [Passport::class, 'logout'])->name('logout');
});
Route::name('admin.')->group(function () {
    Route::post('admin', [AdminController::class, 'create'])->name('create');
    Route::get('admin/two-fa/qr', [AdminController::class, 'getTwoFaQr'])->name('getTwoFaQr');
    Route::post('admin/two-fa/verify', [AdminController::class, 'verifyTwoFa'])->name('verifyTwoFa');
    Route::get('admin/{id}', [AdminController::class, 'detail'])->name('detail');
    Route::put('admin/{id}', [AdminController::class, 'update'])->name('update');
});

/* other routes */
Route::name('news.')->group(function () {
    Route::get('/news', [NewsController::class, 'index'])->name('index');
    Route::get('/news/{news}', [NewsController::class, 'show'])->name('show');
    Route::post('/news', [NewsController::class, 'store'])->name('store');
    Route::put('/news/{news}', [NewsController::class, 'update'])->name('update');
    Route::put('/news/{news}/active', [NewsController::class, 'updateActive'])->name('update.active');
    Route::post('/news/{news}/thumbnail', [NewsController::class, 'uploadThumbnail'])->name('upload.thumbnail');
    Route::delete('/news/{news}/thumbnail', [NewsController::class, 'deleteThumbnail'])->name('delete.thumbnail');
    Route::get('/news/{news}/translations', [NewsController::class, 'translations'])->name('translations');
    Route::put('/news/{news}/translation', [NewsController::class, 'updateTranslation'])->name('update.translation');
    Route::delete('/news/{news}/translation', [NewsController::class, 'deleteTranslation'])->name('delete.translation');
    Route::get('/news-locales', [NewsController::class, 'localeConfig'])->name('locale.config');
});
Route::post('/members/{member}/verify', [MemberController::class, 'verifyIdentify'])->name('members.verify');
Route::post('members/{member}/generate-otp', [MemberController::class, 'generateOtp'])->name('members.generate-otp');
Route::post('members/{member}/generate-transaction-code', [MemberController::class, 'generateTransactionCode'])->name('members.generate-transaction-code');
Route::delete('members/access', [MemberController::class, 'lockMember'])->name('members.lock');
Route::patch('members/access', [MemberController::class, 'unlockMember'])->name('members.unlock');
Route::patch('members/send-promotion-card', [MemberController::class, 'sendPromotionCard'])->name('members.sendPromotionCard');
Route::post('members/send-coupon', [MemberController::class, 'sendCoupon'])->name('members.sendCoupon');
Route::put('members/group', [MemberController::class, 'updateMemberGroup'])->name('members.update-group');
Route::post('members/create', [MemberController::class, 'createUser'])->name('members.create');
Route::resource('members', MemberController::class)->only(['index', 'show', 'update', 'destroy']);
Route::get('categories/all', [CategoryController::class, 'getAll']);
Route::resource('categories', CategoryController::class)->only(['index', 'show', 'update', 'destroy']);
Route::resource('products', ProductController::class)->only(['index', 'store', 'update', 'destroy']);
Route::delete('announcements/{announcement}/cancel', [AnnouncementController::class, 'cancel'])->name('announcements.cancel');
Route::resource('announcements', AnnouncementController::class)->only(['index', 'show', 'store', 'update', 'destroy']);
Route::get('/members-options', [MemberController::class, 'userOptions'])->name('members.options');
Route::get('members/{member}/investment', [MemberController::class, 'getMemberInvestment'])->name('members.investment');
Route::get('members/{member}/team-investment', [MemberController::class, 'getTeamInvestment'])->name('members.team-investment');
Route::patch('members/{member}/bypass-face-auth', [MemberController::class, 'toggleBypassFaceAuth'])->name('members.toggle-bypass-face-auth');

Route::name('finances.')->group(function () {
    Route::get('finances/commissions', [FinanceController::class, 'getCommissions'])->name('commissions');
    Route::get('finances/rebate', [FinanceController::class, 'getRebate'])->name('rebate');
    Route::get('finances/transactions', [FinanceController::class, 'getTransactions'])->name('transactions');
});

Route::name('settings.')->group(function () {
    Route::get('settings', [SettingController::class, 'index'])->name('list');
    Route::put('settings', [SettingController::class, 'update'])->name('update');

    Route::resource('rewards', RewardController::class)
        ->only(['index', 'store', 'update', 'destroy']);
});

Route::name('activities.')->group(function () {
    Route::get('/activities', [ActivityController::class, 'index'])->name('index');
    Route::post('/activities', [ActivityController::class, 'create'])->name('create');
    Route::put('/activities/{id}', [ActivityController::class, 'update'])->name('update');
    Route::delete('/activities/{id}', [ActivityController::class, 'delete'])->name('delete');
    Route::get('/activities/detail/{id}', [ActivityController::class, 'detail'])->name('detail');
    Route::patch('/activities/{id}/toggleStatus', [ActivityController::class, 'toggleStatus'])->name('toggleStatus');
    Route::get('/activities/new-year-activity-summary', [ActivityController::class, 'newYearActivitySummary'])->name('newYearActivitySummary');
});
Route::get('/activities/types', [CommonController::class, 'activityTypes'])->name('common.activityTypes');
Route::get('/activities/cycles', [CommonController::class, 'activityCycles'])->name('common.activityCycles');
Route::get('/wallet/types', [CommonController::class, 'walletTypes'])->name('common.walletTypes');
Route::get('/activity-transactions', [ActivityTransactionController::class, 'index'])->name('activity_transactions.index');
Route::get('/activity-transactions/{id}', [ActivityTransactionController::class, 'detail'])->name('activity_transactions.detail');
Route::get('/activity-transaction/report', [ActivityTransactionController::class, 'report'])->name('activity_transactions.report');

Route::resource('withdraws', WithdrawController::class)->only(['index', 'update']);
Route::resource('deposits', DepositController::class)->only(['index']);
Route::resource('payment_channels', PaymentChannelController::class)->only(['index', 'update', 'store', 'destroy']);
Route::get('payment-providers', [PaymentChannelController::class, 'getProviders'])->name('payment-providers');
Route::get('exchange-rates', [ExchangeRateController::class, 'exchangeRates'])->name('exchange-rates');

Route::put('notifications/{notification}/status', [NotificationController::class, 'updateNotificationStatus'])->name('notifications.update-status');
Route::resource('notifications', NotificationController::class)->only(['index', 'update', 'store', 'destroy']);
Route::name('balances.')->group(function () {
    Route::post('balances/modify', [MemberController::class, 'modifyBalance'])->name('modify');
});

Route::name('reports.')->middleware(['tags:reports'])->group(function () {
    Route::get('/reports/global', [GlobalController::class, 'index'])->name('global');
    Route::get('/reports/hourly-withdraw', [DashboardController::class, 'getHourlyWithdraw'])->name('getHourlyWithdraw');
    Route::get('/reports/hourly-deposit', [DashboardController::class, 'getHourlyDeposit'])->name('getHourlyDeposit');
    Route::get('/reports/hourly-order', [DashboardController::class, 'getHourlyOrder'])->name('getHourlyOrder');
    Route::get('/reports/user-statistic', [DashboardController::class, 'userStatistic'])->name('userStatistic');
    Route::get('/reports/user-register-statistic', [DashboardController::class, 'userRegisterHourly'])->name('userRegisterHourly');
    Route::get('/reports/wallet-summary', [GlobalController::class, 'getWalletSummary'])->name('getWalletSummary');
    Route::get('/reports/payment-channel', [PaymentChannelController::class, 'paymentChannelStatistic'])->name('paymentChannelStatistic');
    Route::post('/reports/agent-report', [GlobalController::class, 'agentReport'])->name('agent-report');
});

Route::name('luckyspin.')->group(function () {
    //    Route::get('/lucky-spin/result/list', [LuckySpinController::class, 'result'])->name('result');
    Route::get('/lucky-spin', [LuckySpinController::class, 'index'])->name('list');
    Route::post('/lucky-spin', [LuckySpinController::class, 'store'])->name('store');
    Route::post('/lucky-spin/update/{id}', [LuckySpinController::class, 'update'])->name('update');
    Route::post('/lucky-spin/{event}/rewards', [LuckySpinController::class, 'createEventReward'])->name('create-reward');
    Route::put('/lucky-spin/rewards/{reward}', [LuckySpinController::class, 'updateEventReward'])->name('update-reward');
    Route::post('/lucky-spin/assign', [LuckySpinController::class, 'assignLuckySpin'])->name('assign-lucky-spin');
    Route::get('/lucky-spin/results', [LuckySpinController::class, 'luckySpinResults'])->name('results');
});

Route::name('deposit-request.')->group(function () {
    Route::get('/deposit-request', [DepositRequestController::class, 'index'])->name('index');
    Route::post('/deposit-request/check-status', [DepositRequestController::class, 'checkStatus'])->name('checkStatus');
});

Route::resource('otp', OtpController::class)->only(['index']);
Route::resource('orders', OrderController::class)->only(['index']);

Route::name('bank_account.')->group(function () {
    Route::get('/bank-account', [BankAccountController::class, 'getList'])->name('index');
    Route::get('/bank-account/check-binded', [BankAccountController::class, 'checkBankBinded'])->name('checkBankBinded');
    Route::post('/bank-account/bind', [BankAccountController::class, 'bind'])->name('bind');
});

Route::resource('promotion-cards', PromotionCardController::class)->only(['index', 'update', 'store']);
Route::resource('coupons', CouponController::class)->only(['index', 'update', 'store']);

Route::name('saving.')->group(function () {
    Route::get('saving', [SavingReportController::class, 'index'])->name('list');
    Route::get('saving/user-summary', [SavingReportController::class, 'userSummaryLists'])->name('list.user.summary');
});

Route::get('locales', [LocalesController::class, 'index'])->name('locales.index');

// Pages resource routes (JSON only)
Route::put('pages/{page}/active', [PagesController::class, 'updateActive'])->name('pages.active-update');
Route::resource('pages', PagesController::class)->only(['index', 'show', 'update', 'store']);

// Pages file upload routes (multipart/form-data)
Route::post('pages/{page}/thumbnail', [PagesController::class, 'uploadThumbnail'])->name('pages.thumbnail.upload');
Route::delete('pages/{page}/thumbnail', [PagesController::class, 'deleteThumbnail'])->name('pages.thumbnail.delete');
Route::resource('banner-sliders', BannerSliderController::class)->only(['index', 'update', 'store', 'destroy']);
Route::resource('refunds', RefundController::class)->only(['index', 'update', 'show']);
Route::resource('payment-organizations', PaymentOrganizationController::class)->only(['index', 'update', 'store']);

// VIP Level routes
Route::put('vip-levels/{vipLevel}/withdrawal-settings', [VipLevelController::class, 'updateWithdrawalSetting'])->name('vip-levels.update-withdrawal-settings');
Route::resource('vip-levels', VipLevelController::class)->only(['index', 'show', 'store', 'update', 'destroy']);

// VIP Level Condition routes
Route::resource('vip-level-conditions', VipLevelConditionController::class)->only(['index', 'show', 'store', 'update', 'destroy']);

// VIP Level Reward routes
Route::resource('vip-level-rewards', VipLevelRewardController::class)->only(['index', 'show']);

// VIP Level History routes
Route::resource('vip-level-history', VipLevelHistoryController::class)->only(['index', 'show']);

// contribute
Route::resource('contributes', ContributeController::class)->only(['index']);
Route::resource('groups', GroupController::class)->only(['index', 'update', 'store', 'destroy']);
