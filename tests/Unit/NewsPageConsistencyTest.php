<?php

use App\Models\News;
use App\Models\Page;
use App\Repositories\NewsRepository;
use App\Repositories\PageRepository;

/**
 * Tests to verify that NewsController and PagesController
 * handle multilingual content identically after the consistency fix
 */
test('news and page repositories handle multilingual updates identically', function () {
    // Create mock News model
    $news = new class extends News
    {
        public $id = 1;

        public $title = 'Original Title';

        public $description = 'Original Description';

        public $content = 'Original Content';

        public $author = 'Test Author';

        public $active = 1;

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }

        public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
        {
            if ($locale === 'en' || $locale === null) {
                return $this->{$field};
            }

            return $this->translations[$field][$locale] ?? $this->{$field};
        }

        public function getAllMockTranslations(): array
        {
            return $this->translations;
        }
    };

    // Create mock Page model
    $page = new class extends Page
    {
        public $id = 1;

        public $slug = 'test-page';

        public $title = 'Original Title';

        public $description = 'Original Description';

        public $content = 'Original Content';

        public $active = 1;

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }

        public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
        {
            if ($locale === 'en' || $locale === null) {
                return $this->{$field};
            }

            return $this->translations[$field][$locale] ?? $this->{$field};
        }

        public function getAllMockTranslations(): array
        {
            return $this->translations;
        }
    };

    $newsRepository = new NewsRepository();
    $pageRepository = new PageRepository();

    // Identical test data for both
    $updateData = [
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
            'vi' => 'Tiêu đề tiếng Việt',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
        'active' => 1,
    ];

    // Add model-specific fields
    $newsUpdateData = array_merge($updateData, ['author' => 'Updated Author']);
    $pageUpdateData = array_merge($updateData, ['slug' => 'updated-page']);

    // Update both models
    $updatedNews = $newsRepository->update($news, $newsUpdateData);
    $updatedPage = $pageRepository->update($page, $pageUpdateData);

    // Verify both handle English content identically
    expect($updatedNews->title)->toBe($updatedPage->title);
    expect($updatedNews->description)->toBe($updatedPage->description);
    expect($updatedNews->content)->toBe($updatedPage->content);
    expect($updatedNews->active)->toBe($updatedPage->active);

    // Verify both create translations identically
    $locales = ['zh_cn', 'vi'];
    $fields = ['title', 'description', 'content'];

    foreach ($fields as $field) {
        foreach ($locales as $locale) {
            $newsHasTranslation = $updatedNews->hasTranslation($field, $locale);
            $pageHasTranslation = $updatedPage->hasTranslation($field, $locale);

            expect($newsHasTranslation)->toBe($pageHasTranslation,
                "News and Page should have identical translation status for {$field}.{$locale}");

            if ($newsHasTranslation) {
                $newsTranslation = $updatedNews->getTranslatedAttribute($field, $locale);
                $pageTranslation = $updatedPage->getTranslatedAttribute($field, $locale);

                expect($newsTranslation)->toBe($pageTranslation,
                    "News and Page should have identical translation content for {$field}.{$locale}");
            }
        }
    }

    // Verify both handle the same number of translations
    expect(count($updatedNews->getAllMockTranslations()))->toBe(count($updatedPage->getAllMockTranslations()));
});

test('news and page repositories handle empty content identically', function () {
    $news = new class extends News
    {
        public $id = 1;

        public $title = 'Original Title';

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }
    };

    $page = new class extends Page
    {
        public $id = 1;

        public $title = 'Original Title';

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }
    };

    $newsRepository = new NewsRepository();
    $pageRepository = new PageRepository();

    // Test data with empty and whitespace content
    $updateData = [
        'title' => [
            'en' => 'Valid English Title',
            'zh_cn' => '   ',  // Whitespace only
            'vi' => '',        // Empty string
            'fr' => 'Valid French Title',
        ],
    ];

    $updatedNews = $newsRepository->update($news, $updateData);
    $updatedPage = $pageRepository->update($page, $updateData);

    // Verify both handle empty content identically
    $testLocales = ['zh_cn', 'vi', 'fr'];

    foreach ($testLocales as $locale) {
        $newsHasTranslation = $updatedNews->hasTranslation('title', $locale);
        $pageHasTranslation = $updatedPage->hasTranslation('title', $locale);

        expect($newsHasTranslation)->toBe($pageHasTranslation,
            "News and Page should handle empty content identically for locale: {$locale}");
    }

    // Specifically verify that both skip empty/whitespace content
    expect($updatedNews->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($updatedPage->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($updatedNews->hasTranslation('title', 'vi'))->toBeFalse();
    expect($updatedPage->hasTranslation('title', 'vi'))->toBeFalse();

    // And both save valid content
    expect($updatedNews->hasTranslation('title', 'fr'))->toBeTrue();
    expect($updatedPage->hasTranslation('title', 'fr'))->toBeTrue();
});

test('news and page models use identical translation traits and methods', function () {
    // Verify both models use the same trait
    $newsTraits = class_uses(News::class);
    $pageTraits = class_uses(Page::class);

    expect($newsTraits)->toContain('App\Traits\HasContentTranslations');
    expect($pageTraits)->toContain('App\Traits\HasContentTranslations');

    // Verify both have the same translatable fields
    $news = new News();
    $page = new Page();

    expect($news->translatable)->toBe($page->translatable);
    expect($news->translatable)->toBe(['title', 'description', 'content']);

    // Verify both have the same translation methods
    $translationMethods = [
        'getTranslatedAttribute',
        'setTranslation',
        'hasTranslation',
        'getAllTranslations',
        'updateTranslationsFromRequest',
        'toArrayWithTranslations',
    ];

    foreach ($translationMethods as $method) {
        expect(method_exists($news, $method))->toBeTrue("News model should have {$method} method");
        expect(method_exists($page, $method))->toBeTrue("Page model should have {$method} method");
    }
});
