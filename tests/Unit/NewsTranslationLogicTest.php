<?php

// Test the translation logic without database
test('translation content validation logic works correctly', function () {
    // Test the fixed logic that should now work
    $testCases = [
        // Should save - valid content
        ['content' => '<p>Valid Chinese Content</p>', 'expected' => true],
        ['content' => 'Simple text content', 'expected' => true],

        // Should NOT save - empty or whitespace
        ['content' => '', 'expected' => false],
        ['content' => '   ', 'expected' => false],
        ['content' => "\n\t  \n", 'expected' => false],

        // Should save - content that looks empty but has actual content
        ['content' => '<p></p>', 'expected' => true], // HTML tags count as content
        ['content' => '0', 'expected' => true], // String '0' is valid content
    ];

    foreach ($testCases as $case) {
        $content = $case['content'];
        $expected = $case['expected'];

        // This mimics the fixed logic: is_string($content) && trim($content) !== ''
        $shouldSave = is_string($content) && trim($content) !== '';

        expect($shouldSave)->toBe($expected, "Content: '{$content}' should " . ($expected ? 'be saved' : 'NOT be saved'));
    }
});

test('translation array processing logic works correctly', function () {
    // Simulate the data structure that comes from the frontend
    $translationData = [
        'title' => [
            'en' => 'English Title',
            'zh_cn' => '中文标题',
        ],
        'description' => [
            'en' => 'English Description',
            'zh_cn' => '   ',  // Whitespace only - should not save
        ],
        'content' => [
            'en' => '<p>English Content</p>',
            'zh_cn' => '<p>中文内容</p>',
        ],
    ];

    $expectedTranslations = [];

    // Process like the fixed setTranslations method would
    foreach (['title', 'description', 'content'] as $field) {
        if (isset($translationData[$field]) && is_array($translationData[$field])) {
            foreach ($translationData[$field] as $locale => $content) {
                if (is_string($content) && trim($content) !== '') {
                    $expectedTranslations[$field][$locale] = $content;
                }
            }
        }
    }

    // Verify the results match our expectations
    expect($expectedTranslations['title'])->toHaveKeys(['en', 'zh_cn']);
    expect($expectedTranslations['title']['zh_cn'])->toBe('中文标题');

    expect($expectedTranslations['description'])->toHaveKey('en');
    expect($expectedTranslations['description'])->not->toHaveKey('zh_cn'); // Whitespace should be filtered out

    expect($expectedTranslations['content'])->toHaveKeys(['en', 'zh_cn']);
    expect($expectedTranslations['content']['zh_cn'])->toBe('<p>中文内容</p>');
});
