<?php

// Unit test to verify the logic changes in HasContentTranslations trait
// This test doesn't require database connection

test('announcement translation logic stores English in both places', function () {
    // Mock the behavior we expect from our updated updateTranslationsFromRequest method

    // Test data structure that comes from the frontend
    $requestData = [
        'title' => [
            'en' => 'english noti',
            'zh_cn' => 'chinese noti',
        ],
        'message' => [
            'en' => '<p>english noti</p>',
            'zh_cn' => '<p>chinese noti</p>',
        ],
        'button_text' => [
            'en' => 'english noti',
            'zh_cn' => 'chinese noti',
        ],
    ];

    // Simulate the logic from our updated updateTranslationsFromRequest method
    $mainTableData = [];
    $translationsToStore = [];

    $translatableFields = ['title', 'message', 'button_text'];

    foreach ($translatableFields as $field) {
        if (isset($requestData[$field]) && is_array($requestData[$field])) {
            foreach ($requestData[$field] as $locale => $content) {
                if ($locale === 'en') {
                    // English content goes to BOTH main model field AND translations table
                    $mainTableData[$field] = $content;

                    // Also store English content in translations table
                    if (is_string($content) && trim($content) !== '') {
                        $translationsToStore[] = [
                            'field' => $field,
                            'locale' => $locale,
                            'content' => $content,
                        ];
                    }
                } elseif (is_string($content) && trim($content) !== '') {
                    // Non-English content goes to translations table
                    $translationsToStore[] = [
                        'field' => $field,
                        'locale' => $locale,
                        'content' => $content,
                    ];
                }
            }
        }
    }

    // Verify English content is stored in main table
    expect($mainTableData)->toHaveKey('title');
    expect($mainTableData)->toHaveKey('message');
    expect($mainTableData)->toHaveKey('button_text');
    expect($mainTableData['title'])->toBe('english noti');
    expect($mainTableData['message'])->toBe('<p>english noti</p>');
    expect($mainTableData['button_text'])->toBe('english noti');

    // Verify ALL languages (including English) are stored in translations
    $expectedTranslations = [
        ['field' => 'title', 'locale' => 'en', 'content' => 'english noti'],
        ['field' => 'title', 'locale' => 'zh_cn', 'content' => 'chinese noti'],
        ['field' => 'message', 'locale' => 'en', 'content' => '<p>english noti</p>'],
        ['field' => 'message', 'locale' => 'zh_cn', 'content' => '<p>chinese noti</p>'],
        ['field' => 'button_text', 'locale' => 'en', 'content' => 'english noti'],
        ['field' => 'button_text', 'locale' => 'zh_cn', 'content' => 'chinese noti'],
    ];

    expect($translationsToStore)->toHaveCount(6);

    // Check that English translations are included
    $englishTranslations = array_filter($translationsToStore, fn ($t) => $t['locale'] === 'en');
    expect($englishTranslations)->toHaveCount(3);

    // Check that Chinese translations are included
    $chineseTranslations = array_filter($translationsToStore, fn ($t) => $t['locale'] === 'zh_cn');
    expect($chineseTranslations)->toHaveCount(3);

    // Verify specific English translations exist
    $englishTitleTranslation = array_filter($translationsToStore, fn ($t) => $t['field'] === 'title' && $t['locale'] === 'en'
    );
    expect($englishTitleTranslation)->toHaveCount(1);
    expect(array_values($englishTitleTranslation)[0]['content'])->toBe('english noti');
});

test('announcement translation logic handles empty content correctly', function () {
    // Test data with empty/whitespace content
    $requestData = [
        'title' => [
            'en' => 'valid english title',
            'zh_cn' => '   ',  // Whitespace only - should not be saved
        ],
        'message' => [
            'en' => '<p>valid english message</p>',
            'zh_cn' => '',  // Empty string - should not be saved
        ],
        'button_text' => [
            'en' => 'valid button',
            'zh_cn' => 'valid chinese button',  // Valid content - should be saved
        ],
    ];

    // Simulate the logic from our updated updateTranslationsFromRequest method
    $mainTableData = [];
    $translationsToStore = [];

    $translatableFields = ['title', 'message', 'button_text'];

    foreach ($translatableFields as $field) {
        if (isset($requestData[$field]) && is_array($requestData[$field])) {
            foreach ($requestData[$field] as $locale => $content) {
                if ($locale === 'en') {
                    // English content goes to BOTH main model field AND translations table
                    $mainTableData[$field] = $content;

                    // Also store English content in translations table
                    if (is_string($content) && trim($content) !== '') {
                        $translationsToStore[] = [
                            'field' => $field,
                            'locale' => $locale,
                            'content' => $content,
                        ];
                    }
                } elseif (is_string($content) && trim($content) !== '') {
                    // Non-English content goes to translations table
                    $translationsToStore[] = [
                        'field' => $field,
                        'locale' => $locale,
                        'content' => $content,
                    ];
                }
            }
        }
    }

    // Verify English content is always stored in main table (even if empty)
    expect($mainTableData)->toHaveKey('title');
    expect($mainTableData)->toHaveKey('message');
    expect($mainTableData)->toHaveKey('button_text');

    // Verify only valid content is stored in translations
    // Should have: 3 English translations + 1 valid Chinese translation = 4 total
    expect($translationsToStore)->toHaveCount(4);

    // Check English translations (all should be stored since they're valid)
    $englishTranslations = array_filter($translationsToStore, fn ($t) => $t['locale'] === 'en');
    expect($englishTranslations)->toHaveCount(3);

    // Check Chinese translations (only the valid button_text should be stored)
    $chineseTranslations = array_filter($translationsToStore, fn ($t) => $t['locale'] === 'zh_cn');
    expect($chineseTranslations)->toHaveCount(1);

    $chineseButtonTranslation = array_filter($translationsToStore, fn ($t) => $t['field'] === 'button_text' && $t['locale'] === 'zh_cn'
    );
    expect($chineseButtonTranslation)->toHaveCount(1);
    expect(array_values($chineseButtonTranslation)[0]['content'])->toBe('valid chinese button');
});

test('announcement translation logic validates content correctly', function () {
    // Test various content validation scenarios
    $testCases = [
        // Valid content - should be saved
        ['content' => 'Valid text', 'expected' => true],
        ['content' => '<p>Valid HTML</p>', 'expected' => true],
        ['content' => '0', 'expected' => true], // String '0' is valid

        // Invalid content - should NOT be saved
        ['content' => '', 'expected' => false],
        ['content' => '   ', 'expected' => false],
        ['content' => "\n\t  \n", 'expected' => false],
    ];

    foreach ($testCases as $case) {
        $content = $case['content'];
        $expected = $case['expected'];

        // This is the validation logic from our updated method
        $shouldSave = is_string($content) && trim($content) !== '';

        expect($shouldSave)->toBe($expected, "Content: '{$content}' should " . ($expected ? 'be saved' : 'NOT be saved'));
    }
});
