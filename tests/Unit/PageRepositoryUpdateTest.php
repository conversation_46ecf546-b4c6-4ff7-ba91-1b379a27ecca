<?php

use App\Models\Page;
use App\Repositories\PageRepository;

/**
 * Unit tests for PageRepository update functionality
 * These tests verify that PageRepository now has the same multilingual
 * capabilities as NewsRepository after the consistency fix
 */
test('page repository update method processes multilingual data correctly', function () {
    // Mock the Page model to avoid database operations
    $page = new class extends Page
    {
        public $id = 1;

        public $slug = 'original-page';

        public $title = 'Original Title';

        public $description = 'Original Description';

        public $content = 'Original Content';

        public $active = 1;

        private $translations = [];

        private $updateCalled = false;

        private $saveCalled = false;

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }
            $this->updateCalled = true;

            return true;
        }

        public function save(array $options = [])
        {
            $this->saveCalled = true;

            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }

        public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
        {
            if ($locale === 'en' || $locale === null) {
                return $this->{$field};
            }

            return $this->translations[$field][$locale] ?? $this->{$field};
        }

        public function wasUpdateCalled(): bool
        {
            return $this->updateCalled;
        }

        public function wasSaveCalled(): bool
        {
            return $this->saveCalled;
        }
    };

    $repository = new PageRepository();

    // Test data with multilingual content
    $updateData = [
        'slug' => 'updated-page',
        'active' => 1,
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
            'vi' => 'Tiêu đề tiếng Việt',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
    ];

    $result = $repository->update($page, $updateData);

    // Verify the main model was updated
    expect($result->wasUpdateCalled())->toBeTrue();
    expect($result->slug)->toBe('updated-page');
    expect($result->title)->toBe('Updated English Title');
    expect($result->description)->toBe('Updated English Description');
    expect($result->content)->toBe('<p>Updated English Content</p>');
    expect($result->active)->toBe(1);

    // Verify translations were created
    expect($result->hasTranslation('title', 'zh_cn'))->toBeTrue();
    expect($result->hasTranslation('title', 'vi'))->toBeTrue();
    expect($result->hasTranslation('description', 'zh_cn'))->toBeTrue();
    expect($result->hasTranslation('content', 'zh_cn'))->toBeTrue();

    // Verify translation content
    expect($result->getTranslatedAttribute('title', 'zh_cn'))->toBe('更新的中文标题');
    expect($result->getTranslatedAttribute('title', 'vi'))->toBe('Tiêu đề tiếng Việt');
    expect($result->getTranslatedAttribute('description', 'zh_cn'))->toBe('更新的中文描述');
    expect($result->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>更新的中文内容</p>');
});

test('page repository reproduces the exact bug scenario that was fixed in news', function () {
    $page = new class extends Page
    {
        public $id = 1;

        public $slug = 'test-page';

        public $title = 'Original English Title';

        public $description = 'Original English Description';

        public $content = '<p>Original English Content</p>';

        public $active = 1;

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }

        public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
        {
            if ($locale === 'en' || $locale === null) {
                return $this->{$field};
            }

            return $this->translations[$field][$locale] ?? $this->{$field};
        }
    };

    $repository = new PageRepository();

    // Verify initial state - only English content exists
    expect($page->title)->toBe('Original English Title');
    expect($page->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($page->hasTranslation('description', 'zh_cn'))->toBeFalse();
    expect($page->hasTranslation('content', 'zh_cn'))->toBeFalse();

    // Update the same page with both English and Chinese content
    $updateData = [
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
    ];

    $updatedPage = $repository->update($page, $updateData);

    // Verify English content was updated
    expect($updatedPage->title)->toBe('Updated English Title');
    expect($updatedPage->description)->toBe('Updated English Description');
    expect($updatedPage->content)->toBe('<p>Updated English Content</p>');

    // THIS SHOULD NOW WORK: Chinese translations should be created
    expect($updatedPage->hasTranslation('title', 'zh_cn'))->toBeTrue('Chinese title translation should be created');
    expect($updatedPage->hasTranslation('description', 'zh_cn'))->toBeTrue('Chinese description translation should be created');
    expect($updatedPage->hasTranslation('content', 'zh_cn'))->toBeTrue('Chinese content translation should be created');

    // Verify the Chinese content is correctly saved
    expect($updatedPage->getTranslatedAttribute('title', 'zh_cn'))->toBe('更新的中文标题');
    expect($updatedPage->getTranslatedAttribute('description', 'zh_cn'))->toBe('更新的中文描述');
    expect($updatedPage->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>更新的中文内容</p>');
});

test('page repository handles empty and whitespace translations correctly', function () {
    $page = new class extends Page
    {
        public $id = 1;

        public $title = 'Original Title';

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }
    };

    $repository = new PageRepository();

    // Test data with empty and whitespace content
    $updateData = [
        'title' => [
            'en' => 'Valid English Title',
            'zh_cn' => '   ',  // Whitespace only
            'vi' => '',        // Empty string
            'fr' => 'Valid French Title',
        ],
    ];

    $result = $repository->update($page, $updateData);

    // Verify only valid translations were saved
    expect($result->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($result->hasTranslation('title', 'vi'))->toBeFalse();
    expect($result->hasTranslation('title', 'fr'))->toBeTrue();
});
