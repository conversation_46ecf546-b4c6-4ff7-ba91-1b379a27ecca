<?php

use App\Models\News;
use App\Repositories\NewsRepository;

/**
 * Unit tests for NewsRepository update functionality
 * These tests don't require database connection
 */
test('news repository update method processes multilingual data correctly', function () {
    // Mock the News model to avoid database operations
    $news = new class extends News
    {
        public $id = 1;

        public $title = 'Original Title';

        public $description = 'Original Description';

        public $content = 'Original Content';

        public $author = 'Original Author';

        public $active = 1;

        private $translations = [];

        private $updateCalled = false;

        private $saveCalled = false;

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }
            $this->updateCalled = true;

            return true;
        }

        public function save(array $options = [])
        {
            $this->saveCalled = true;

            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }

        public function getTranslatedAttribute(string $field, ?string $locale = null): ?string
        {
            if ($locale === 'en' || $locale === null) {
                return $this->{$field};
            }

            return $this->translations[$field][$locale] ?? $this->{$field};
        }

        public function wasUpdateCalled(): bool
        {
            return $this->updateCalled;
        }

        public function wasSaveCalled(): bool
        {
            return $this->saveCalled;
        }
    };

    $repository = new NewsRepository();

    // Test data with multilingual content
    $updateData = [
        'author' => 'Updated Author',
        'active' => 1,
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
            'vi' => 'Tiêu đề tiếng Việt',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
    ];

    $result = $repository->update($news, $updateData);

    // Verify the main model was updated
    expect($result->wasUpdateCalled())->toBeTrue();
    expect($result->title)->toBe('Updated English Title');
    expect($result->description)->toBe('Updated English Description');
    expect($result->content)->toBe('<p>Updated English Content</p>');
    expect($result->author)->toBe('Updated Author');
    expect($result->active)->toBe(1);

    // Verify translations were created
    expect($result->hasTranslation('title', 'zh_cn'))->toBeTrue();
    expect($result->hasTranslation('title', 'vi'))->toBeTrue();
    expect($result->hasTranslation('description', 'zh_cn'))->toBeTrue();
    expect($result->hasTranslation('content', 'zh_cn'))->toBeTrue();

    // Verify translation content
    expect($result->getTranslatedAttribute('title', 'zh_cn'))->toBe('更新的中文标题');
    expect($result->getTranslatedAttribute('title', 'vi'))->toBe('Tiêu đề tiếng Việt');
    expect($result->getTranslatedAttribute('description', 'zh_cn'))->toBe('更新的中文描述');
    expect($result->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>更新的中文内容</p>');
});

test('news repository update handles empty and whitespace translations correctly', function () {
    $news = new class extends News
    {
        public $id = 1;

        public $title = 'Original Title';

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }
    };

    $repository = new NewsRepository();

    // Test data with empty and whitespace content
    $updateData = [
        'title' => [
            'en' => 'Valid English Title',
            'zh_cn' => '   ',  // Whitespace only
            'vi' => '',        // Empty string
            'fr' => 'Valid French Title',
        ],
    ];

    $result = $repository->update($news, $updateData);

    // Verify only valid translations were saved
    expect($result->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($result->hasTranslation('title', 'vi'))->toBeFalse();
    expect($result->hasTranslation('title', 'fr'))->toBeTrue();
});

test('news repository update handles partial updates correctly', function () {
    $news = new class extends News
    {
        public $id = 1;

        public $title = 'Original Title';

        public $author = 'Original Author';

        public $active = 0;

        private $translations = [];

        public function update(array $attributes = [], array $options = [])
        {
            foreach ($attributes as $key => $value) {
                $this->{$key} = $value;
            }

            return true;
        }

        public function save(array $options = [])
        {
            return true;
        }

        public function setTranslation(string $field, string $locale, string $content): void
        {
            $this->translations[$field][$locale] = $content;
        }

        public function hasTranslation(string $field, string $locale): bool
        {
            return isset($this->translations[$field][$locale]);
        }
    };

    $repository = new NewsRepository();

    // Test partial update (only title and active status)
    $updateData = [
        'active' => 1,
        'title' => [
            'en' => 'Updated Title',
            'zh_cn' => '更新标题',
        ],
    ];

    $result = $repository->update($news, $updateData);

    // Verify only specified fields were updated
    expect($result->title)->toBe('Updated Title');
    expect($result->active)->toBe(1);
    expect($result->author)->toBe('Original Author'); // Should remain unchanged
    expect($result->hasTranslation('title', 'zh_cn'))->toBeTrue();
});
