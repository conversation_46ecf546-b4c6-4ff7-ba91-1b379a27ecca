<?php

use App\Models\Announcement;
use App\Services\AnnouncementService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('announcement translations are stored correctly for both English and Chinese', function () {
    $service = new AnnouncementService();

    // Test data matching the user's sample request
    $announcementData = [
        'title' => [
            'en' => 'english noti',
            'zh_cn' => 'chinese noti',
        ],
        'message' => [
            'en' => '<p>english noti</p>',
            'zh_cn' => '<p>chinese noti</p>',
        ],
        'button_text' => [
            'en' => 'english noti',
            'zh_cn' => 'chinese noti',
        ],
        'release_time' => '2025-07-23T00:00:00+08:00',
        'user_phone' => '+84975070003',
        'subordinate_type' => 1,
        'force_popup' => 0,
        'status' => 1,
    ];

    // Create announcement
    $announcement = $service->create($announcementData);

    // Verify announcement was created
    expect($announcement)->toBeInstanceOf(Announcement::class);
    expect($announcement->id)->toBeGreaterThan(0);

    // VERIFY DESIRED BEHAVIOR: English content should be in BOTH main table AND translations table

    // 1. English content should be in main table
    expect($announcement->title)->toBe('english noti');
    expect($announcement->message)->toBe('<p>english noti</p>');
    expect($announcement->button_text)->toBe('english noti');

    // 2. English content should ALSO be in translations table
    expect($announcement->hasTranslation('title', 'en'))->toBeTrue('English title should be in translations table');
    expect($announcement->hasTranslation('message', 'en'))->toBeTrue('English message should be in translations table');
    expect($announcement->hasTranslation('button_text', 'en'))->toBeTrue('English button_text should be in translations table');

    // 3. Chinese content should be in translations table
    expect($announcement->hasTranslation('title', 'zh_cn'))->toBeTrue('Chinese title should be in translations table');
    expect($announcement->hasTranslation('message', 'zh_cn'))->toBeTrue('Chinese message should be in translations table');
    expect($announcement->hasTranslation('button_text', 'zh_cn'))->toBeTrue('Chinese button_text should be in translations table');

    // 4. Verify content retrieval works correctly
    expect($announcement->getTranslatedAttribute('title', 'en'))->toBe('english noti');
    expect($announcement->getTranslatedAttribute('title', 'zh_cn'))->toBe('chinese noti');
    expect($announcement->getTranslatedAttribute('message', 'en'))->toBe('<p>english noti</p>');
    expect($announcement->getTranslatedAttribute('message', 'zh_cn'))->toBe('<p>chinese noti</p>');
    expect($announcement->getTranslatedAttribute('button_text', 'en'))->toBe('english noti');
    expect($announcement->getTranslatedAttribute('button_text', 'zh_cn'))->toBe('chinese noti');

    // 5. Verify getAllTranslations returns both languages
    $allTranslations = $announcement->getAllTranslations();
    expect($allTranslations)->toHaveKey('title');
    expect($allTranslations)->toHaveKey('message');
    expect($allTranslations)->toHaveKey('button_text');

    expect($allTranslations['title'])->toHaveKey('en');
    expect($allTranslations['title'])->toHaveKey('zh_cn');
    expect($allTranslations['title']['en'])->toBe('english noti');
    expect($allTranslations['title']['zh_cn'])->toBe('chinese noti');
});

test('announcement update preserves both English and Chinese translations', function () {
    $service = new AnnouncementService();

    // Create initial announcement
    $initialData = [
        'title' => [
            'en' => 'initial english',
            'zh_cn' => 'initial chinese',
        ],
        'message' => [
            'en' => '<p>initial english message</p>',
            'zh_cn' => '<p>initial chinese message</p>',
        ],
        'button_text' => [
            'en' => 'initial button',
            'zh_cn' => 'initial button chinese',
        ],
        'release_time' => '2025-07-23T00:00:00+08:00',
        'subordinate_type' => 1,
        'force_popup' => 0,
        'status' => 0,  // Status 0 so we can update
    ];

    $announcement = $service->create($initialData);

    // Update with new content
    $updateData = [
        'title' => [
            'en' => 'updated english',
            'zh_cn' => 'updated chinese',
        ],
        'message' => [
            'en' => '<p>updated english message</p>',
            'zh_cn' => '<p>updated chinese message</p>',
        ],
        'button_text' => [
            'en' => 'updated button',
            'zh_cn' => 'updated button chinese',
        ],
        'subordinate_type' => 1,
        'force_popup' => 0,
        'status' => 0,
    ];

    $service->update($updateData, $announcement);
    $announcement->refresh();

    // Verify updated English content is in both main table and translations
    expect($announcement->title)->toBe('updated english');
    expect($announcement->message)->toBe('<p>updated english message</p>');
    expect($announcement->button_text)->toBe('updated button');

    expect($announcement->hasTranslation('title', 'en'))->toBeTrue();
    expect($announcement->hasTranslation('message', 'en'))->toBeTrue();
    expect($announcement->hasTranslation('button_text', 'en'))->toBeTrue();

    // Verify updated Chinese content is in translations table
    expect($announcement->hasTranslation('title', 'zh_cn'))->toBeTrue();
    expect($announcement->hasTranslation('message', 'zh_cn'))->toBeTrue();
    expect($announcement->hasTranslation('button_text', 'zh_cn'))->toBeTrue();

    expect($announcement->getTranslatedAttribute('title', 'zh_cn'))->toBe('updated chinese');
    expect($announcement->getTranslatedAttribute('message', 'zh_cn'))->toBe('<p>updated chinese message</p>');
    expect($announcement->getTranslatedAttribute('button_text', 'zh_cn'))->toBe('updated button chinese');
});

test('announcement handles empty or whitespace translations correctly', function () {
    $service = new AnnouncementService();

    $announcementData = [
        'title' => [
            'en' => 'valid english title',
            'zh_cn' => '   ',  // Whitespace only - should not be saved
        ],
        'message' => [
            'en' => '<p>valid english message</p>',
            'zh_cn' => '',  // Empty string - should not be saved
        ],
        'button_text' => [
            'en' => 'valid button',
            'zh_cn' => 'valid chinese button',  // Valid content - should be saved
        ],
        'release_time' => '2025-07-23T00:00:00+08:00',
        'subordinate_type' => 1,
        'force_popup' => 0,
        'status' => 1,
    ];

    $announcement = $service->create($announcementData);

    // English content should always be saved (both main table and translations)
    expect($announcement->hasTranslation('title', 'en'))->toBeTrue();
    expect($announcement->hasTranslation('message', 'en'))->toBeTrue();
    expect($announcement->hasTranslation('button_text', 'en'))->toBeTrue();

    // Empty/whitespace Chinese content should NOT be saved
    expect($announcement->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($announcement->hasTranslation('message', 'zh_cn'))->toBeFalse();

    // Valid Chinese content should be saved
    expect($announcement->hasTranslation('button_text', 'zh_cn'))->toBeTrue();
    expect($announcement->getTranslatedAttribute('button_text', 'zh_cn'))->toBe('valid chinese button');
});
