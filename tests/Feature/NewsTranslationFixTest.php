<?php

use App\Models\News;
use App\Repositories\NewsRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Test to reproduce the exact bug scenario described by the user

test('news translations are properly saved for multiple languages including zh_cn', function () {
    // Create a NewsRepository instance
    $repository = new NewsRepository();

    // Test data with multi-language content
    $newsData = [
        'author' => 'Test Author',
        'active' => 1,
        'title' => [
            'en' => 'English News Title',
            'zh_cn' => '中文新闻标题',
        ],
        'description' => [
            'en' => 'English news description',
            'zh_cn' => '中文新闻描述',
        ],
        'content' => [
            'en' => '<p>English news content with HTML</p>',
            'zh_cn' => '<p>中文新闻内容包含HTML</p>',
        ],
    ];

    // Create news with translations
    $news = $repository->create($newsData);

    // Verify the news was created
    expect($news)->toBeInstanceOf(News::class);
    expect($news->id)->toBeGreaterThan(0);

    // Verify English content is stored in main fields
    expect($news->title)->toBe('English News Title');
    expect($news->description)->toBe('English news description');
    expect($news->content)->toBe('<p>English news content with HTML</p>');

    // Verify translations exist in content_translations table
    expect($news->getTranslatedAttribute('title', 'zh_cn'))->toBe('中文新闻标题');
    expect($news->getTranslatedAttribute('description', 'zh_cn'))->toBe('中文新闻描述');
    expect($news->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>中文新闻内容包含HTML</p>');

    // Verify all translations are properly returned
    $allTranslations = $news->getAllTranslations();
    expect($allTranslations)->toHaveKeys(['title', 'description', 'content']);
    expect($allTranslations['title'])->toHaveKeys(['en', 'zh_cn']);
    expect($allTranslations['title']['zh_cn'])->toBe('中文新闻标题');
    expect($allTranslations['content']['zh_cn'])->toBe('<p>中文新闻内容包含HTML</p>');
});

test('news translations are properly updated for multiple languages', function () {
    // Create initial news
    $repository = new NewsRepository();
    $news = $repository->create([
        'author' => 'Test Author',
        'active' => 1,
        'title' => ['en' => 'Original Title'],
        'description' => ['en' => 'Original Description'],
        'content' => ['en' => '<p>Original Content</p>'],
    ]);

    // Update with Chinese translations
    $updateData = [
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
    ];

    $updatedNews = $repository->update($news, $updateData);

    // Verify English content was updated
    expect($updatedNews->title)->toBe('Updated English Title');
    expect($updatedNews->description)->toBe('Updated English Description');
    expect($updatedNews->content)->toBe('<p>Updated English Content</p>');

    // Verify Chinese translations were saved
    expect($updatedNews->getTranslatedAttribute('title', 'zh_cn'))->toBe('更新的中文标题');
    expect($updatedNews->getTranslatedAttribute('description', 'zh_cn'))->toBe('更新的中文描述');
    expect($updatedNews->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>更新的中文内容</p>');

    // Test the specific scenario from the issue: edit existing news and add zh_cn content
    $finalUpdateData = [
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '最终的中文标题',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>最终的中文内容</p>',
        ],
    ];

    $finalUpdatedNews = $repository->update($updatedNews, $finalUpdateData);

    // Verify the final Chinese content was properly saved
    expect($finalUpdatedNews->getTranslatedAttribute('title', 'zh_cn'))->toBe('最终的中文标题');
    expect($finalUpdatedNews->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>最终的中文内容</p>');
});

test('empty or whitespace-only translations are not saved', function () {
    $repository = new NewsRepository();

    $newsData = [
        'author' => 'Test Author',
        'active' => 1,
        'title' => [
            'en' => 'English Title',
            'zh_cn' => '   ',  // Whitespace only
        ],
        'description' => [
            'en' => 'English Description',
            'zh_cn' => '',     // Empty string
        ],
        'content' => [
            'en' => '<p>English Content</p>',
            'zh_cn' => '<p>Valid Chinese Content</p>',
        ],
    ];

    $news = $repository->create($newsData);

    // Verify empty/whitespace translations are not saved
    expect($news->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($news->hasTranslation('description', 'zh_cn'))->toBeFalse();

    // But valid content should be saved
    expect($news->hasTranslation('content', 'zh_cn'))->toBeTrue();
    expect($news->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>Valid Chinese Content</p>');
});

test('reproduces the exact bug scenario: create news with one language then update with multiple languages', function () {
    $repository = new NewsRepository();

    // Step 1: Create news with only English content (simulating initial creation)
    $initialData = [
        'author' => 'Test Author',
        'active' => 1,
        'title' => [
            'en' => 'Original English Title',
        ],
        'description' => [
            'en' => 'Original English Description',
        ],
        'content' => [
            'en' => '<p>Original English Content</p>',
        ],
    ];

    $news = $repository->create($initialData);

    // Verify initial state - only English content exists
    expect($news->title)->toBe('Original English Title');
    expect($news->hasTranslation('title', 'zh_cn'))->toBeFalse();
    expect($news->hasTranslation('description', 'zh_cn'))->toBeFalse();
    expect($news->hasTranslation('content', 'zh_cn'))->toBeFalse();

    // Step 2: Update the same news with both English and Chinese content
    $updateData = [
        'title' => [
            'en' => 'Updated English Title',
            'zh_cn' => '更新的中文标题',
        ],
        'description' => [
            'en' => 'Updated English Description',
            'zh_cn' => '更新的中文描述',
        ],
        'content' => [
            'en' => '<p>Updated English Content</p>',
            'zh_cn' => '<p>更新的中文内容</p>',
        ],
    ];

    $updatedNews = $repository->update($news, $updateData);

    // Verify English content was updated
    expect($updatedNews->title)->toBe('Updated English Title');
    expect($updatedNews->description)->toBe('Updated English Description');
    expect($updatedNews->content)->toBe('<p>Updated English Content</p>');

    // THIS IS THE BUG: Chinese translations should be created but might not be
    expect($updatedNews->hasTranslation('title', 'zh_cn'))->toBeTrue('Chinese title translation should be created');
    expect($updatedNews->hasTranslation('description', 'zh_cn'))->toBeTrue('Chinese description translation should be created');
    expect($updatedNews->hasTranslation('content', 'zh_cn'))->toBeTrue('Chinese content translation should be created');

    // Verify the Chinese content is correctly saved
    expect($updatedNews->getTranslatedAttribute('title', 'zh_cn'))->toBe('更新的中文标题');
    expect($updatedNews->getTranslatedAttribute('description', 'zh_cn'))->toBe('更新的中文描述');
    expect($updatedNews->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>更新的中文内容</p>');
});
