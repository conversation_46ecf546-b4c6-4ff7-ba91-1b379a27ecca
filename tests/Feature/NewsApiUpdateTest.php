<?php

use App\Http\Requests\UpdateNewsRequest;
use App\Models\News;
use App\Repositories\NewsRepository;

test('update news request validation works correctly', function () {
    $requestData = [
        'author' => 'hungnguhen',
        'active' => 1,
        'title' => [
            'en' => 'english news',
            'zh_cn' => 'chinese news',
        ],
        'description' => [
            'en' => 'english news',
            'zh_cn' => 'chinese news',
        ],
        'content' => [
            'en' => '<p>english news</p>',
            'zh_cn' => '<p>chinese news</p>',
        ],
    ];

    // Create a request instance and validate
    $request = new UpdateNewsRequest();
    $rules = $request->rules();

    // Create a validator manually
    $validator = validator($requestData, $rules);

    expect($validator->passes())->toBeTrue('Validation should pass with valid data');

    $validated = $validator->validated();
    expect($validated)->toHaveKey('title');
    expect($validated['title'])->toHaveKeys(['en', 'zh_cn']);
    expect($validated['content'])->toHaveKeys(['en', 'zh_cn']);
});

test('news repository update processes validated data correctly', function () {
    // Create initial news
    $news = News::create([
        'title' => 'Original Title',
        'description' => 'Original Description',
        'content' => '<p>Original Content</p>',
        'author' => 'Original Author',
        'active' => 0,
    ]);

    $repository = new NewsRepository();

    // Simulate validated data from UpdateNewsRequest
    $validatedData = [
        'author' => 'hungnguhen',
        'active' => 1,
        'title' => [
            'en' => 'english news',
            'zh_cn' => 'chinese news',
        ],
        'description' => [
            'en' => 'english news',
            'zh_cn' => 'chinese news',
        ],
        'content' => [
            'en' => '<p>english news</p>',
            'zh_cn' => '<p>chinese news</p>',
        ],
    ];

    $updatedNews = $repository->update($news, $validatedData);

    // Verify main fields updated
    expect($updatedNews->title)->toBe('english news');
    expect($updatedNews->author)->toBe('hungnguhen');
    expect($updatedNews->active)->toBe(1);

    // Verify Chinese translations saved
    expect($updatedNews->getTranslatedAttribute('title', 'zh_cn'))->toBe('chinese news');
    expect($updatedNews->getTranslatedAttribute('description', 'zh_cn'))->toBe('chinese news');
    expect($updatedNews->getTranslatedAttribute('content', 'zh_cn'))->toBe('<p>chinese news</p>');

    // Verify the toArrayWithTranslations method includes translations
    $newsWithTranslations = $updatedNews->toArrayWithTranslations();
    expect($newsWithTranslations)->toHaveKey('translations');
    expect($newsWithTranslations['translations']['title']['zh_cn'])->toBe('chinese news');
    expect($newsWithTranslations['translations']['content']['zh_cn'])->toBe('<p>chinese news</p>');
});
