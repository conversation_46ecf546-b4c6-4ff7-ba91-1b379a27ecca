<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AdminUserCreationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_admin_can_create_user_with_valid_data()
    {
        // Create a parent user first
        $parent = User::factory()->create([
            'invitation_code' => '********',
            'account_type' => 'regular',
        ]);

        $userData = [
            'email' => $this->faker->unique()->safeEmail,
            'phone' => '***********', // Valid phone number
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'invitation_code' => '********',
            'transaction_passcode' => '123456',
            'transaction_passcode_confirmation' => '123456',
        ];

        $response = $this->postJson('/admin-api/members/create', $userData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'invitation_code',
                        'created_at',
                    ],
                ],
            ]);

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
            'phone' => $userData['phone'],
            'parent_id' => $parent->id,
        ]);
    }

    public function test_admin_user_creation_validates_required_fields()
    {
        $response = $this->postJson('/admin-api/members/create', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'phone',
                'password',
                'invitation_code',
                'transaction_passcode',
            ]);
    }

    public function test_admin_user_creation_validates_invitation_code()
    {
        $userData = [
            'email' => $this->faker->unique()->safeEmail,
            'phone' => '***********',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'invitation_code' => '99999999', // Non-existent invitation code
            'transaction_passcode' => '123456',
            'transaction_passcode_confirmation' => '123456',
        ];

        $response = $this->postJson('/admin-api/members/create', $userData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['invitation_code']);
    }
}
