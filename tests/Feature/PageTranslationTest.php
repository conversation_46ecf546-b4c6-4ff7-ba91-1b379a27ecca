<?php

use App\Models\Page;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PageTranslationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test environment with supported locales
        config(['translatable.locales' => ['en', 'zh_CN']]);
        config(['translatable.fallback_locale' => 'en']);
    }

    public function test_page_can_be_created_with_translations(): void
    {
        $pageData = [
            'slug' => 'test-page',
            'title' => ['en' => 'Test Page', 'zh_CN' => '测试页面'],
            'description' => ['en' => 'Test Description', 'zh_CN' => '测试描述'],
            'content' => ['en' => 'Test Content', 'zh_CN' => '测试内容'],
            'active' => 1,
        ];

        $page = Page::create($pageData);

        $this->assertNotNull($page);
        $this->assertEquals('test-page', $page->slug);
        $this->assertEquals(1, $page->active);

        // Test English translations
        app()->setLocale('en');
        $this->assertEquals('Test Page', $page->title);
        $this->assertEquals('Test Description', $page->description);
        $this->assertEquals('Test Content', $page->content);

        // Test Chinese translations
        app()->setLocale('zh_CN');
        $this->assertEquals('测试页面', $page->title);
        $this->assertEquals('测试描述', $page->description);
        $this->assertEquals('测试内容', $page->content);
    }

    public function test_page_fallback_locale_works(): void
    {
        $pageData = [
            'slug' => 'test-page',
            'title' => ['en' => 'Test Page'],
            'description' => ['en' => 'Test Description'],
            'content' => ['en' => 'Test Content'],
            'active' => 1,
        ];

        $page = Page::create($pageData);

        // Switch to Chinese locale (which doesn't have translation)
        app()->setLocale('zh_CN');

        // Should fallback to English
        $this->assertEquals('Test Page', $page->title);
        $this->assertEquals('Test Description', $page->description);
        $this->assertEquals('Test Content', $page->content);
    }

    public function test_page_can_be_updated_with_translations(): void
    {
        $page = Page::create([
            'slug' => 'test-page',
            'title' => ['en' => 'Original Title'],
            'description' => ['en' => 'Original Description'],
            'content' => ['en' => 'Original Content'],
            'active' => 1,
        ]);

        $updateData = [
            'title' => ['en' => 'Updated Title', 'zh_CN' => '更新标题'],
            'description' => ['en' => 'Updated Description', 'zh_CN' => '更新描述'],
            'content' => ['en' => 'Updated Content', 'zh_CN' => '更新内容'],
        ];

        $page->update($updateData);

        // Test English
        app()->setLocale('en');
        $this->assertEquals('Updated Title', $page->fresh()->title);

        // Test Chinese
        app()->setLocale('zh_CN');
        $this->assertEquals('更新标题', $page->fresh()->title);
    }

    public function test_page_unique_slug_constraint(): void
    {
        Page::create([
            'slug' => 'unique-page',
            'title' => ['en' => 'First Page'],
            'description' => ['en' => 'First Description'],
            'content' => ['en' => 'First Content'],
            'active' => 1,
        ]);

        // Creating another page with same slug should work since slug is not unique
        // (depends on your business logic)
        $secondPage = Page::create([
            'slug' => 'another-page',
            'title' => ['en' => 'Second Page'],
            'description' => ['en' => 'Second Description'],
            'content' => ['en' => 'Second Content'],
            'active' => 1,
        ]);

        $this->assertNotNull($secondPage);
        $this->assertEquals('another-page', $secondPage->slug);
    }

    public function test_page_with_single_locale_content(): void
    {
        app()->setLocale('en');

        $pageData = [
            'slug' => 'simple-page',
            'title' => 'Simple Title',
            'description' => 'Simple Description',
            'content' => 'Simple Content',
            'active' => 1,
        ];

        // This should work if the model handles single locale data properly
        $page = Page::create([
            'slug' => $pageData['slug'],
            'title' => ['en' => $pageData['title']],
            'description' => ['en' => $pageData['description']],
            'content' => ['en' => $pageData['content']],
            'active' => $pageData['active'],
        ]);

        $this->assertNotNull($page);
        $this->assertEquals('Simple Title', $page->title);
    }

    public function test_page_translations_are_loaded_correctly(): void
    {
        $page = Page::create([
            'slug' => 'test-page',
            'title' => ['en' => 'English Title', 'zh_CN' => '中文标题'],
            'description' => ['en' => 'English Description', 'zh_CN' => '中文描述'],
            'content' => ['en' => 'English Content', 'zh_CN' => '中文内容'],
            'active' => 1,
        ]);

        // Load page with translations
        $loadedPage = Page::with('translations')->find($page->id);

        $this->assertNotNull($loadedPage);
        $this->assertTrue($loadedPage->relationLoaded('translations'));

        // Check that we have translations for both locales
        $translations = $loadedPage->translations;
        $this->assertCount(2, $translations);

        $locales = $translations->pluck('locale')->toArray();
        $this->assertContains('en', $locales);
        $this->assertContains('zh_CN', $locales);
    }
}
