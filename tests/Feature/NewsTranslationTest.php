<?php

use App\Models\News;
use App\Services\NewsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NewsTranslationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test environment with supported locales
        config(['translatable.locales' => ['en', 'zh_CN']]);
        config(['translatable.fallback_locale' => 'en']);
    }

    public function test_news_can_be_created_with_translations(): void
    {
        $newsData = [
            'title' => ['en' => 'Test News', 'zh_CN' => '测试新闻'],
            'description' => ['en' => 'Test Description', 'zh_CN' => '测试描述'],
            'content' => ['en' => 'Test Content', 'zh_CN' => '测试内容'],
            'author' => 'Test Author',
            'active' => 1,
        ];

        $news = News::create($newsData);

        $this->assertNotNull($news);
        $this->assertEquals('Test Author', $news->author);
        $this->assertEquals(1, $news->active);

        // Test English translations
        app()->setLocale('en');
        $this->assertEquals('Test News', $news->title);
        $this->assertEquals('Test Description', $news->description);
        $this->assertEquals('Test Content', $news->content);

        // Test Chinese translations
        app()->setLocale('zh_CN');
        $this->assertEquals('测试新闻', $news->title);
        $this->assertEquals('测试描述', $news->description);
        $this->assertEquals('测试内容', $news->content);
    }

    public function test_news_service_handles_translation_data(): void
    {
        $newsService = new NewsService();

        $newsData = [
            'title' => 'Test News',
            'description' => 'Test Description',
            'content' => 'Test Content',
            'author' => 'Test Author',
            'active' => true,
        ];

        app()->setLocale('en');
        $news = $newsService->create($newsData);

        $this->assertNotNull($news);
        $this->assertEquals('Test News', $news->title);
        $this->assertEquals('Test Author', $news->author);
    }

    public function test_news_fallback_locale_works(): void
    {
        $newsData = [
            'title' => ['en' => 'Test News'],
            'description' => ['en' => 'Test Description'],
            'content' => ['en' => 'Test Content'],
            'author' => 'Test Author',
            'active' => 1,
        ];

        $news = News::create($newsData);

        // Switch to Chinese locale (which doesn't have translation)
        app()->setLocale('zh_CN');

        // Should fallback to English
        $this->assertEquals('Test News', $news->title);
        $this->assertEquals('Test Description', $news->description);
        $this->assertEquals('Test Content', $news->content);
    }

    public function test_news_can_be_updated_with_translations(): void
    {
        $news = News::create([
            'title' => ['en' => 'Original Title'],
            'description' => ['en' => 'Original Description'],
            'content' => ['en' => 'Original Content'],
            'author' => 'Test Author',
            'active' => 1,
        ]);

        $updateData = [
            'title' => ['en' => 'Updated Title', 'zh_CN' => '更新标题'],
            'description' => ['en' => 'Updated Description', 'zh_CN' => '更新描述'],
            'content' => ['en' => 'Updated Content', 'zh_CN' => '更新内容'],
        ];

        $news->update($updateData);

        // Test English
        app()->setLocale('en');
        $this->assertEquals('Updated Title', $news->fresh()->title);

        // Test Chinese
        app()->setLocale('zh_CN');
        $this->assertEquals('更新标题', $news->fresh()->title);
    }

    public function test_news_service_get_news_by_locale(): void
    {
        $news = News::create([
            'title' => ['en' => 'English Title', 'zh_CN' => '中文标题'],
            'description' => ['en' => 'English Description', 'zh_CN' => '中文描述'],
            'content' => ['en' => 'English Content', 'zh_CN' => '中文内容'],
            'author' => 'Test Author',
            'active' => 1,
        ]);

        $newsService = new NewsService();

        // Test English locale
        $englishNews = $newsService->getNewsByLocale($news->id, 'en');
        $this->assertEquals('English Title', $englishNews->title);

        // Test Chinese locale
        $chineseNews = $newsService->getNewsByLocale($news->id, 'zh_CN');
        $this->assertEquals('中文标题', $chineseNews->title);
    }

    public function test_news_service_get_all_translations(): void
    {
        $news = News::create([
            'title' => ['en' => 'English Title', 'zh_CN' => '中文标题'],
            'description' => ['en' => 'English Description', 'zh_CN' => '中文描述'],
            'content' => ['en' => 'English Content', 'zh_CN' => '中文内容'],
            'author' => 'Test Author',
            'active' => 1,
        ]);

        $newsService = new NewsService();
        $translations = $newsService->getNewsTranslations($news->id);

        $this->assertIsArray($translations);
        $this->assertArrayHasKey('en', $translations);
        $this->assertArrayHasKey('zh_CN', $translations);

        $this->assertEquals('English Title', $translations['en']['title']);
        $this->assertEquals('中文标题', $translations['zh_CN']['title']);
    }

    public function test_news_with_single_locale_string_gets_converted(): void
    {
        app()->setLocale('en');

        $newsService = new NewsService();
        $newsData = [
            'title' => 'Simple Title',
            'description' => 'Simple Description',
            'content' => 'Simple Content',
            'author' => 'Test Author',
            'active' => true,
        ];

        $news = $newsService->create($newsData);

        $this->assertNotNull($news);
        $this->assertEquals('Simple Title', $news->title);
    }
}
