# VIP Level Rewards API Documentation

This directory contains the OpenAPI 3.0 specification for the VIP Level Rewards API endpoints.

## Files

- `vip-level-rewards-api.yaml` - Complete OpenAPI 3.0 specification for VIP Level Rewards API

## API Overview

The VIP Level Rewards API provides read-only access to VIP level reward records with comprehensive filtering capabilities. This API is designed for admin users to view and search through reward claims made by users when they achieve specific VIP levels.

### Base URL
- **Development**: `http://localhost/admin-api`
- **Production**: `https://api.example.com/admin-api`

### Authentication
All endpoints require Bearer token authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Available Endpoints

### 1. List VIP Level Rewards
**GET** `/vip-level-rewards`

Retrieve a paginated list of VIP level rewards with optional filtering.

#### Query Parameters
| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| `user_id` | integer | No | Filter by specific user ID | Must exist in users table |
| `vip_level_id` | integer | No | Filter by specific VIP level ID | Must exist in vip_levels table |
| `user_name` | string | No | Filter by user name (partial match) | Max 255 characters |
| `vip_level_name` | string | No | Filter by VIP level name (partial match) | Max 255 characters |
| `transaction_code` | string | No | Filter by transaction code (partial match) | Max 255 characters |
| `date_from` | date | No | Filter rewards from this date | Must be before or equal to date_to |
| `date_to` | date | No | Filter rewards until this date | Must be after or equal to date_from |
| `pageSize` | integer | No | Records per page (default: 10) | Min: 1, Max: 100 |

#### Example Requests
```bash
# Basic list
GET /admin-api/vip-level-rewards

# Filter by user name and VIP level
GET /admin-api/vip-level-rewards?user_name=john&vip_level_name=gold

# Filter by date range with pagination
GET /admin-api/vip-level-rewards?date_from=2024-01-01&date_to=2024-12-31&pageSize=20

# Combined filters
GET /admin-api/vip-level-rewards?user_id=123&transaction_code=TXN&pageSize=50
```

### 2. Show VIP Level Reward
**GET** `/vip-level-rewards/{id}`

Retrieve detailed information about a specific VIP level reward record.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | VIP Level Reward ID |

#### Example Request
```bash
GET /admin-api/vip-level-rewards/1
```

## Response Format

All API responses follow a consistent format:

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // Response data here
  }
}
```

### List Response Structure
The list endpoint returns Laravel pagination format:

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "vip_level_id": 5,
        "reward_amount": "1000.00",
        "transaction_code": "TXN123456789",
        "claimed_at": "2024-01-15T10:30:00.000000Z",
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "user": {
          "id": 123,
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "vip_level": {
          "id": 5,
          "name": "Gold",
          "level_order": 3
        }
      }
    ],
    "first_page_url": "http://localhost/admin-api/vip-level-rewards?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://localhost/admin-api/vip-level-rewards?page=5",
    "next_page_url": "http://localhost/admin-api/vip-level-rewards?page=2",
    "path": "http://localhost/admin-api/vip-level-rewards",
    "per_page": 10,
    "prev_page_url": null,
    "to": 10,
    "total": 50
  }
}
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

### Common Error Responses

#### 400 Bad Request
```json
{
  "code": 400,
  "message": "Bad Request",
  "errors": {
    "general": ["Invalid request parameters"]
  }
}
```

#### 401 Unauthorized
```json
{
  "code": 401,
  "message": "Unauthorized",
  "errors": {
    "auth": ["Authentication token required"]
  }
}
```

#### 404 Not Found
```json
{
  "code": 404,
  "message": "Not Found",
  "errors": {
    "id": ["VIP Level Reward not found"]
  }
}
```

#### 422 Validation Error
```json
{
  "code": 422,
  "message": "The given data was invalid.",
  "errors": {
    "user_id": ["The selected user does not exist."],
    "pageSize": ["Page size cannot exceed 100."],
    "date_from": ["Date from must be before or equal to date to."]
  }
}
```

#### 500 Internal Server Error
```json
{
  "code": 500,
  "message": "Internal Server Error",
  "errors": {
    "server": ["An unexpected error occurred"]
  }
}
```

## Data Models

### VipLevelReward
- `id` (integer) - Unique identifier
- `user_id` (integer) - User who claimed the reward
- `vip_level_id` (integer) - VIP level for the reward
- `reward_amount` (decimal string) - Amount of the reward
- `transaction_code` (string) - Unique transaction identifier
- `claimed_at` (datetime) - When the reward was claimed
- `created_at` (datetime) - Record creation time
- `updated_at` (datetime) - Record last update time
- `user` (object) - Related user information
- `vip_level` (object) - Related VIP level information

### User (Relationship)
- `id` (integer) - User identifier
- `name` (string) - User's full name
- `email` (string) - User's email address

### VipLevel (Relationship)
- `id` (integer) - VIP level identifier
- `name` (string) - VIP level name
- `level_order` (integer) - Numeric order of the level

## Viewing the Documentation

You can view this OpenAPI specification using:

1. **Swagger UI**: Upload the YAML file to [Swagger Editor](https://editor.swagger.io/)
2. **Postman**: Import the OpenAPI specification into Postman
3. **Insomnia**: Import the specification into Insomnia
4. **VS Code**: Use OpenAPI extensions for VS Code

## Testing the API

Use the provided examples and schemas to test the API endpoints. Make sure to:

1. Obtain a valid JWT token from the admin authentication endpoint
2. Include the token in the Authorization header
3. Use the correct base URL for your environment
4. Follow the validation rules for all parameters

## Support

For API support or questions, contact the development team or refer to the main application documentation.
