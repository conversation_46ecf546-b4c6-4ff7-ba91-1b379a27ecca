{"openapi": "3.0.3", "info": {"title": "Tornado Admin API", "description": "Comprehensive API documentation for the Tornado Admin system. This API provides administrative access to manage users, finances, activities, VIP levels, and various other system components.", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost/admin-api", "description": "Local development server"}, {"url": "https://api.example.com/admin-api", "description": "Production server"}], "security": [{"bearerAuth": []}], "paths": {"/passport/login": {"post": {"summary": "<PERSON><PERSON>", "description": "Authenticate admin user and obtain JWT token", "operationId": "adminLogin", "tags": ["Authentication"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/passport/refresh-token": {"put": {"summary": "Refresh JWT Token", "description": "Refresh expired JWT token", "operationId": "refreshToken", "tags": ["Authentication"], "security": [], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/passport/logout": {"delete": {"summary": "<PERSON><PERSON>", "description": "Logout admin user and invalidate JWT token", "operationId": "adminLogout", "tags": ["Authentication"], "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/members": {"get": {"summary": "List Members", "description": "Retrieve a paginated list of members with filtering capabilities", "operationId": "listMembers", "tags": ["Members"], "parameters": [{"name": "member_type", "in": "query", "description": "Filter by member type", "schema": {"type": "string", "enum": ["agent", "individual"]}}, {"name": "member_level", "in": "query", "description": "Filter by member level", "schema": {"type": "string", "enum": ["star", "gold", "silver", "general"]}}, {"name": "partner_type", "in": "query", "description": "Filter by partner type", "schema": {"type": "string", "enum": ["agent", "elementary", "senior", "premium"]}}, {"name": "agent_number", "in": "query", "description": "Filter by agent number", "schema": {"type": "string"}}, {"name": "phone", "in": "query", "description": "Filter by phone number", "schema": {"type": "string"}}, {"name": "vip_level_id", "in": "query", "description": "Filter by VIP level ID", "schema": {"type": "integer"}}, {"name": "sortBy", "in": "query", "description": "Sort by field", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "description": "Sort direction", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "Successful response with paginated members", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/members/{id}": {"get": {"summary": "Show Member", "description": "Retrieve detailed information about a specific member", "operationId": "showMember", "tags": ["Members"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Member ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with member details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"summary": "Update Member", "description": "Update member information", "operationId": "updateMember", "tags": ["Members"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Member ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMemberRequest"}}}}, "responses": {"200": {"description": "Member updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "delete": {"summary": "Delete Member", "description": "Delete a member", "operationId": "deleteMember", "tags": ["Members"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Member ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Member deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/members/{member}/verify": {"post": {"summary": "Verify Member Identity", "description": "Verify member identity", "operationId": "verifyMemberIdentity", "tags": ["Members"], "parameters": [{"name": "member", "in": "path", "required": true, "description": "Member ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Member verified successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/vip-levels": {"get": {"summary": "List VIP Levels", "description": "Retrieve a list of VIP levels", "operationId": "listVipLevels", "tags": ["VIP Levels"], "responses": {"200": {"description": "Successful response with VIP levels", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create VIP Level", "description": "Create a new VIP level", "operationId": "createVipLevel", "tags": ["VIP Levels"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreVipLevelRequest"}}}}, "responses": {"201": {"description": "VIP level created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/vip-levels/{id}": {"get": {"summary": "Show VIP Level", "description": "Retrieve detailed information about a specific VIP level", "operationId": "showVipLevel", "tags": ["VIP Levels"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with VIP level details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"summary": "Update VIP Level", "description": "Update VIP level information", "operationId": "updateVipLevel", "tags": ["VIP Levels"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVipLevelRequest"}}}}, "responses": {"200": {"description": "VIP level updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "delete": {"summary": "Delete VIP Level", "description": "Delete a VIP level", "operationId": "deleteVipLevel", "tags": ["VIP Levels"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "VIP level deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/vip-level-conditions": {"get": {"summary": "List VIP Level Conditions", "description": "Retrieve a list of VIP level conditions with filtering capabilities. Each condition uses threshold-based qualification criteria. Note: Each VIP level can have only one condition.", "operationId": "listVipLevelConditions", "tags": ["VIP Level Conditions"], "parameters": [{"name": "vip_level_id", "in": "query", "description": "Filter by VIP level ID", "schema": {"type": "integer"}}, {"name": "is_active", "in": "query", "description": "Filter by active status", "schema": {"type": "boolean"}}, {"name": "pageSize", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "Successful response with VIP level conditions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelConditionListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create VIP Level Condition", "description": "Create a new VIP level condition using threshold-based qualification criteria. Note: Only one condition per VIP level is allowed. The system automatically detects and prevents threshold conflicts between different VIP levels.", "operationId": "createVipLevelCondition", "tags": ["VIP Level Conditions"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreVipLevelConditionRequest"}}}}, "responses": {"201": {"description": "VIP level condition created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelConditionResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"description": "Validation Error or Business Logic Violation", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"message": {"type": "string", "examples": ["A condition already exists for this VIP level. Only one condition per VIP level is allowed.", "Threshold values conflict with VIP Level Gold. All threshold values are identical, which would create ambiguous VIP level qualification.", "Threshold conflict detected with VIP Level Silver in: remaining balance, total deposit. This may cause ambiguous VIP level qualification."]}}}]}}}}}}}, "/vip-level-conditions/{id}": {"get": {"summary": "Show VIP Level Condition", "description": "Retrieve detailed information about a specific VIP level condition", "operationId": "showVipLevelCondition", "tags": ["VIP Level Conditions"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level Condition ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with VIP level condition details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelConditionResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"summary": "Update VIP Level Condition", "description": "Update VIP level condition information using threshold-based qualification criteria. Note: Only one condition per VIP level is allowed. The system automatically detects and prevents threshold conflicts between different VIP levels.", "operationId": "updateVipLevelCondition", "tags": ["VIP Level Conditions"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level Condition ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVipLevelConditionRequest"}}}}, "responses": {"200": {"description": "VIP level condition updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelConditionResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"description": "Validation Error or Business Logic Violation", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"message": {"type": "string", "examples": ["A condition already exists for this VIP level. Only one condition per VIP level is allowed.", "Threshold values conflict with VIP Level Gold. All threshold values are identical, which would create ambiguous VIP level qualification.", "Threshold conflict detected with VIP Level Silver in: remaining balance, total deposit. This may cause ambiguous VIP level qualification."]}}}]}}}}}}, "delete": {"summary": "Delete VIP Level Condition", "description": "Delete a VIP level condition", "operationId": "deleteVipLevelCondition", "tags": ["VIP Level Conditions"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level Condition ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "VIP level condition deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/vip-level-rewards": {"get": {"summary": "List VIP Level Rewards", "description": "Retrieve a paginated list of VIP level rewards with filtering capabilities", "operationId": "listVipLevelRewards", "tags": ["VIP Level Rewards"], "parameters": [{"name": "user_id", "in": "query", "description": "Filter by user ID", "schema": {"type": "integer"}}, {"name": "vip_level_id", "in": "query", "description": "Filter by VIP level ID", "schema": {"type": "integer"}}, {"name": "user_name", "in": "query", "description": "Filter by user name", "schema": {"type": "string"}}, {"name": "email", "in": "query", "description": "Filter by email", "schema": {"type": "string"}}, {"name": "vip_level_name", "in": "query", "description": "Filter by VIP level name", "schema": {"type": "string"}}, {"name": "transaction_code", "in": "query", "description": "Filter by transaction code", "schema": {"type": "string"}}, {"name": "date_from", "in": "query", "description": "Filter from date", "schema": {"type": "string", "format": "date"}}, {"name": "date_to", "in": "query", "description": "Filter to date", "schema": {"type": "string", "format": "date"}}, {"name": "pageSize", "in": "query", "description": "Number of records per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Successful response with paginated VIP level rewards", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelRewardListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/vip-level-rewards/{id}": {"get": {"summary": "Show VIP Level Reward", "description": "Retrieve detailed information about a specific VIP level reward", "operationId": "showVipLevelReward", "tags": ["VIP Level Rewards"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level Reward ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with VIP level reward details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelRewardResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/vip-level-history": {"get": {"summary": "List VIP Level History", "description": "Retrieve a paginated list of VIP level history with filtering capabilities", "operationId": "listVipLevelHistory", "tags": ["VIP Level History"], "parameters": [{"name": "user_id", "in": "query", "description": "Filter by user ID", "schema": {"type": "integer"}}, {"name": "old_vip_level_id", "in": "query", "description": "Filter by old VIP level ID", "schema": {"type": "integer"}}, {"name": "new_vip_level_id", "in": "query", "description": "Filter by new VIP level ID", "schema": {"type": "integer"}}, {"name": "trigger_event", "in": "query", "description": "Filter by trigger event", "schema": {"type": "string", "enum": ["deposit", "withdrawal", "manual", "bonus"]}}, {"name": "action_type", "in": "query", "description": "Filter by action type", "schema": {"type": "string"}}, {"name": "user_name", "in": "query", "description": "Filter by user name", "schema": {"type": "string"}}, {"name": "email", "in": "query", "description": "Filter by email", "schema": {"type": "string"}}, {"name": "old_vip_level_name", "in": "query", "description": "Filter by old VIP level name", "schema": {"type": "string"}}, {"name": "new_vip_level_name", "in": "query", "description": "Filter by new VIP level name", "schema": {"type": "string"}}, {"name": "date_from", "in": "query", "description": "Filter from date", "schema": {"type": "string", "format": "date"}}, {"name": "date_to", "in": "query", "description": "Filter to date", "schema": {"type": "string", "format": "date"}}, {"name": "pageSize", "in": "query", "description": "Number of records per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Successful response with paginated VIP level history", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelHistoryListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/vip-level-history/{id}": {"get": {"summary": "Show VIP Level History", "description": "Retrieve detailed information about a specific VIP level history record", "operationId": "showVipLevelHistory", "tags": ["VIP Level History"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "VIP Level History ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with VIP level history details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipLevelHistoryResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/activities": {"get": {"summary": "List Activities", "description": "Retrieve a list of activities", "operationId": "listActivities", "tags": ["Activities"], "responses": {"200": {"description": "Successful response with activities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create Activity", "description": "Create a new activity", "operationId": "createActivity", "tags": ["Activities"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateActivityRequest"}}}}, "responses": {"201": {"description": "Activity created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/activities/{id}": {"put": {"summary": "Update Activity", "description": "Update an existing activity", "operationId": "updateActivity", "tags": ["Activities"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Activity ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateActivityRequest"}}}}, "responses": {"200": {"description": "Activity updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "delete": {"summary": "Delete Activity", "description": "Delete an activity", "operationId": "deleteActivity", "tags": ["Activities"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Activity ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Activity deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/news": {"get": {"summary": "List News", "description": "Retrieve a list of news articles", "operationId": "listNews", "tags": ["News"], "responses": {"200": {"description": "Successful response with news articles", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "Create News", "description": "Create a new news article", "operationId": "createNews", "tags": ["News"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNewsRequest"}}}}, "responses": {"201": {"description": "News created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/news/{id}": {"get": {"summary": "Show News", "description": "Retrieve detailed information about a specific news article", "operationId": "showNews", "tags": ["News"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "News ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response with news details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"summary": "Update News", "description": "Update news article", "operationId": "updateNews", "tags": ["News"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "News ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNewsRequest"}}}}, "responses": {"200": {"description": "News updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/withdraws": {"get": {"summary": "List Withdrawals", "description": "Retrieve a list of withdrawal requests", "operationId": "listWithdrawals", "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "Successful response with withdrawals", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WithdrawalListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/withdraws/{id}": {"put": {"summary": "Update <PERSON><PERSON><PERSON>", "description": "Update withdrawal request status", "operationId": "updateWithdrawal", "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Withdrawal ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWithdrawalRequest"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WithdrawalResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "/deposits": {"get": {"summary": "List Deposits", "description": "Retrieve a list of deposit records", "operationId": "listDeposits", "tags": ["Deposits"], "responses": {"200": {"description": "Successful response with deposits", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositListResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/reports/global": {"get": {"summary": "Global Reports", "description": "Retrieve global system reports", "operationId": "getGlobalReports", "tags": ["Reports"], "responses": {"200": {"description": "Successful response with global reports", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalReportResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/finances/commissions": {"get": {"summary": "Get Commission Reports", "description": "Retrieve commission reports", "operationId": "getCommissions", "tags": ["Finance"], "responses": {"200": {"description": "Successful response with commission data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommissionResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/finances/transactions": {"get": {"summary": "Get Transaction Reports", "description": "Retrieve transaction reports", "operationId": "getTransactions", "tags": ["Finance"], "responses": {"200": {"description": "Successful response with transaction data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionResponse"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token obtained from the admin authentication endpoint"}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "HTTP status code", "example": 200}, "message": {"type": "string", "description": "Response message", "example": "Success"}}, "required": ["code", "message"]}, "PaginationMeta": {"type": "object", "properties": {"current_page": {"type": "integer", "example": 1}, "first_page_url": {"type": "string", "example": "http://localhost/admin-api/members?page=1"}, "from": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 5}, "last_page_url": {"type": "string", "example": "http://localhost/admin-api/members?page=5"}, "next_page_url": {"type": "string", "nullable": true, "example": "http://localhost/admin-api/members?page=2"}, "path": {"type": "string", "example": "http://localhost/admin-api/members"}, "per_page": {"type": "integer", "example": 10}, "prev_page_url": {"type": "string", "nullable": true, "example": null}, "to": {"type": "integer", "example": 10}, "total": {"type": "integer", "example": 50}}}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password123"}}, "required": ["email", "password"]}, "LoginResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"access_token": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "token_type": {"type": "string", "example": "Bearer"}, "expires_in": {"type": "integer", "example": 3600}, "user": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Admin User"}, "email": {"type": "string", "example": "<EMAIL>"}}}}}}}]}, "User": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}, "Member": {"allOf": [{"$ref": "#/components/schemas/User"}, {"type": "object", "properties": {"member_type": {"type": "string", "enum": ["agent", "individual"]}, "member_level": {"type": "string", "enum": ["star", "gold", "silver", "general"]}, "partner_type": {"type": "string", "enum": ["agent", "elementary", "senior", "premium"]}, "agent_number": {"type": "string", "example": "AG001"}, "vip_level_id": {"type": "integer", "example": 3}}}]}, "MemberListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/components/schemas/PaginationMeta"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Member"}}}}]}}}]}, "MemberResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Member"}}}]}, "UpdateMemberRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "member_type": {"type": "string", "enum": ["agent", "individual"]}, "member_level": {"type": "string", "enum": ["star", "gold", "silver", "general"]}, "partner_type": {"type": "string", "enum": ["agent", "elementary", "senior", "premium"]}, "agent_number": {"type": "string", "example": "AG001"}, "vip_level_id": {"type": "integer", "example": 3}}}, "VipLevel": {"type": "object", "properties": {"id": {"type": "integer", "example": 5}, "name": {"type": "string", "example": "Gold"}, "level_order": {"type": "integer", "example": 3}, "min_points": {"type": "string", "example": "1000.00"}, "max_points": {"type": "string", "example": "5000.00"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}, "VipLevelListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/VipLevel"}}}}]}, "VipLevelResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/VipLevel"}}}]}, "StoreVipLevelRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Gold"}, "level_order": {"type": "integer", "example": 3}, "min_points": {"type": "string", "example": "1000.00"}, "max_points": {"type": "string", "example": "5000.00"}}, "required": ["name", "level_order", "min_points"]}, "UpdateVipLevelRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Gold"}, "level_order": {"type": "integer", "example": 3}, "min_points": {"type": "string", "example": "1000.00"}, "max_points": {"type": "string", "example": "5000.00"}}}, "VipLevelCondition": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "vip_level_id": {"type": "integer", "example": 5}, "remaining_balance_threshold": {"type": "string", "description": "Minimum remaining balance required to qualify for this VIP level", "example": "1000.00"}, "total_deposit_threshold": {"type": "string", "description": "Minimum total deposit required to qualify for this VIP level", "example": "5000.00"}, "subordinates_balance_threshold": {"type": "string", "description": "Minimum subordinates balance required to qualify for this VIP level", "example": "2000.00"}, "subordinates_deposit_threshold": {"type": "string", "description": "Minimum subordinates deposit required to qualify for this VIP level", "example": "3000.00"}, "is_active": {"type": "boolean", "example": true}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "vipLevel": {"$ref": "#/components/schemas/VipLevel"}}}, "VipLevelConditionListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"current_page": {"type": "integer", "example": 1}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VipLevelCondition"}}, "first_page_url": {"type": "string", "example": "http://localhost/admin-api/vip-level-conditions?page=1"}, "from": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 5}, "last_page_url": {"type": "string", "example": "http://localhost/admin-api/vip-level-conditions?page=5"}, "next_page_url": {"type": "string", "nullable": true, "example": "http://localhost/admin-api/vip-level-conditions?page=2"}, "path": {"type": "string", "example": "http://localhost/admin-api/vip-level-conditions"}, "per_page": {"type": "integer", "example": 10}, "prev_page_url": {"type": "string", "nullable": true, "example": null}, "to": {"type": "integer", "example": 10}, "total": {"type": "integer", "example": 50}}}}}]}, "VipLevelConditionResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/VipLevelCondition"}}}]}, "StoreVipLevelConditionRequest": {"type": "object", "description": "Request to create a VIP level condition. Note: Only one condition per VIP level is allowed. Threshold conflicts with other VIP levels will be automatically detected.", "properties": {"vip_level_id": {"type": "integer", "description": "VIP Level ID (must be unique - only one condition per VIP level allowed)", "example": 5}, "remaining_balance_threshold": {"type": "number", "minimum": 0, "description": "Minimum remaining balance required to qualify for this VIP level", "example": 1000.0}, "total_deposit_threshold": {"type": "number", "minimum": 0, "description": "Minimum total deposit required to qualify for this VIP level", "example": 5000.0}, "subordinates_balance_threshold": {"type": "number", "minimum": 0, "description": "Minimum subordinates balance required to qualify for this VIP level", "example": 2000.0}, "subordinates_deposit_threshold": {"type": "number", "minimum": 0, "description": "Minimum subordinates deposit required to qualify for this VIP level", "example": 3000.0}, "is_active": {"type": "boolean", "description": "Whether this condition is active", "example": true}}, "required": ["vip_level_id", "remaining_balance_threshold", "total_deposit_threshold", "subordinates_balance_threshold", "subordinates_deposit_threshold"]}, "UpdateVipLevelConditionRequest": {"type": "object", "description": "Request to update a VIP level condition. Note: Only one condition per VIP level is allowed. Threshold conflicts with other VIP levels will be automatically detected.", "properties": {"vip_level_id": {"type": "integer", "description": "VIP Level ID (must be unique - only one condition per VIP level allowed)", "example": 5}, "remaining_balance_threshold": {"type": "number", "minimum": 0, "description": "Minimum remaining balance required to qualify for this VIP level", "example": 1000.0}, "total_deposit_threshold": {"type": "number", "minimum": 0, "description": "Minimum total deposit required to qualify for this VIP level", "example": 5000.0}, "subordinates_balance_threshold": {"type": "number", "minimum": 0, "description": "Minimum subordinates balance required to qualify for this VIP level", "example": 2000.0}, "subordinates_deposit_threshold": {"type": "number", "minimum": 0, "description": "Minimum subordinates deposit required to qualify for this VIP level", "example": 3000.0}, "is_active": {"type": "boolean", "description": "Whether this condition is active", "example": true}}}, "VipLevelReward": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "vip_level_id": {"type": "integer", "example": 5}, "reward_amount": {"type": "string", "example": "1000.00"}, "transaction_code": {"type": "string", "example": "TXN123456789"}, "claimed_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "user": {"$ref": "#/components/schemas/User"}, "vip_level": {"$ref": "#/components/schemas/VipLevel"}}}, "VipLevelRewardListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/components/schemas/PaginationMeta"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/VipLevelReward"}}}}]}}}]}, "VipLevelRewardResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/VipLevelReward"}}}]}, "VipLevelHistory": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "old_vip_level_id": {"type": "integer", "nullable": true, "example": 2}, "new_vip_level_id": {"type": "integer", "example": 3}, "trigger_event": {"type": "string", "enum": ["deposit", "withdrawal", "manual", "bonus"], "example": "deposit"}, "trigger_reference_id": {"type": "integer", "nullable": true, "example": 456}, "action_type": {"type": "string", "example": "upgrade"}, "calculated_points": {"type": "string", "example": "1500.00"}, "user_balance": {"type": "string", "example": "5000.00"}, "user_total_deposit": {"type": "string", "example": "10000.00"}, "subordinates_balance": {"type": "string", "example": "2000.00"}, "subordinates_total_deposit": {"type": "string", "example": "8000.00"}, "subordinates_count": {"type": "integer", "example": 5}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "user": {"$ref": "#/components/schemas/User"}, "old_vip_level": {"$ref": "#/components/schemas/VipLevel"}, "new_vip_level": {"$ref": "#/components/schemas/VipLevel"}}}, "VipLevelHistoryListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/components/schemas/PaginationMeta"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/VipLevelHistory"}}}}]}}}]}, "VipLevelHistoryResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/VipLevelHistory"}}}]}, "Activity": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Summer Promotion"}, "description": {"type": "string", "example": "Summer promotion activity"}, "status": {"type": "string", "example": "active"}, "start_date": {"type": "string", "format": "date-time", "example": "2024-06-01T00:00:00.000000Z"}, "end_date": {"type": "string", "format": "date-time", "example": "2024-08-31T23:59:59.000000Z"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}, "ActivityListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}}]}, "ActivityResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Activity"}}}]}, "CreateActivityRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Summer Promotion"}, "description": {"type": "string", "example": "Summer promotion activity"}, "start_date": {"type": "string", "format": "date-time", "example": "2024-06-01T00:00:00.000000Z"}, "end_date": {"type": "string", "format": "date-time", "example": "2024-08-31T23:59:59.000000Z"}}, "required": ["name", "start_date", "end_date"]}, "UpdateActivityRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Summer Promotion"}, "description": {"type": "string", "example": "Summer promotion activity"}, "status": {"type": "string", "example": "active"}, "start_date": {"type": "string", "format": "date-time", "example": "2024-06-01T00:00:00.000000Z"}, "end_date": {"type": "string", "format": "date-time", "example": "2024-08-31T23:59:59.000000Z"}}}, "News": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Important Update"}, "content": {"type": "string", "example": "This is an important update..."}, "status": {"type": "string", "example": "published"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}, "NewsListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/News"}}}}]}, "NewsResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/News"}}}]}, "CreateNewsRequest": {"type": "object", "properties": {"title": {"type": "string", "example": "Important Update"}, "content": {"type": "string", "example": "This is an important update..."}}, "required": ["title", "content"]}, "UpdateNewsRequest": {"type": "object", "properties": {"title": {"type": "string", "example": "Important Update"}, "content": {"type": "string", "example": "This is an important update..."}, "status": {"type": "string", "example": "published"}}}, "Withdrawal": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "amount": {"type": "string", "example": "1000.00"}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "user": {"$ref": "#/components/schemas/User"}}}, "WithdrawalListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Withdrawal"}}}}]}, "WithdrawalResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Withdrawal"}}}]}, "UpdateWithdrawalRequest": {"type": "object", "properties": {"status": {"type": "string", "example": "approved"}}}, "Deposit": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "amount": {"type": "string", "example": "1000.00"}, "status": {"type": "string", "example": "completed"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}, "user": {"$ref": "#/components/schemas/User"}}}, "DepositListResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Deposit"}}}}]}, "GlobalReportResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"total_users": {"type": "integer", "example": 1000}, "total_deposits": {"type": "string", "example": "100000.00"}, "total_withdrawals": {"type": "string", "example": "50000.00"}, "active_users": {"type": "integer", "example": 500}}}}}]}, "CommissionResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"total_commission": {"type": "string", "example": "10000.00"}, "commissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "amount": {"type": "string", "example": "100.00"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}}}}}}]}, "TransactionResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 123}, "type": {"type": "string", "example": "deposit"}, "amount": {"type": "string", "example": "1000.00"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000000Z"}}}}}}}}]}, "ErrorResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "example": {"field": ["Error message"]}}}}]}, "ValidationErrorResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "example": {"field": ["Validation error message"]}}}}]}}, "responses": {"BadRequest": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "ValidationError": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}, "InternalServerError": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "tags": [{"name": "Authentication", "description": "Authentication and session management endpoints"}, {"name": "Members", "description": "Member management operations"}, {"name": "VIP Levels", "description": "VIP level management operations"}, {"name": "VIP Level Rewards", "description": "VIP level reward tracking operations"}, {"name": "VIP Level History", "description": "VIP level history tracking operations"}, {"name": "Activities", "description": "Activity management operations"}, {"name": "News", "description": "News and announcement management"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Withdrawal request management"}, {"name": "Deposits", "description": "Deposit tracking operations"}, {"name": "Reports", "description": "Reporting and analytics operations"}, {"name": "Finance", "description": "Financial operations and reporting"}]}