# VIP Level Rewards API Examples

This document provides practical examples for using the VIP Level Rewards API endpoints.

## Authentication

All requests require a Bearer token in the Authorization header:

```bash
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Base URLs

- **Development**: `http://localhost/admin-api`
- **Production**: `https://api.example.com/admin-api`

## Example Requests

### 1. List All VIP Level Rewards (Basic)

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "vip_level_id": 5,
        "reward_amount": "1000.00",
        "transaction_code": "TXN123456789",
        "claimed_at": "2024-01-15T10:30:00.000000Z",
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "user": {
          "id": 123,
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "vip_level": {
          "id": 5,
          "name": "Gold",
          "level_order": 3
        }
      }
    ],
    "first_page_url": "http://localhost/admin-api/vip-level-rewards?page=1",
    "from": 1,
    "last_page": 5,
    "per_page": 10,
    "total": 50
  }
}
```

### 2. Filter by User Name

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?user_name=john" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 3. Filter by VIP Level Name

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?vip_level_name=gold" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 4. Filter by Specific User ID

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?user_id=123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 5. Filter by Transaction Code

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?transaction_code=TXN123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 6. Filter by Date Range

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?date_from=2024-01-01&date_to=2024-12-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 7. Combined Filters with Pagination

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?user_name=john&vip_level_name=gold&pageSize=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 8. Complex Filter Example

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?user_id=123&vip_level_id=5&date_from=2024-01-01&date_to=2024-06-30&pageSize=50" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

### 9. Show Specific VIP Level Reward

```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "user_id": 123,
    "vip_level_id": 5,
    "reward_amount": "1000.00",
    "transaction_code": "TXN123456789",
    "claimed_at": "2024-01-15T10:30:00.000000Z",
    "created_at": "2024-01-15T10:30:00.000000Z",
    "updated_at": "2024-01-15T10:30:00.000000Z",
    "user": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "vip_level": {
      "id": 5,
      "name": "Gold",
      "level_order": 3
    }
  }
}
```

## Error Examples

### 1. Validation Error (422)

**Request:**
```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards?pageSize=150&date_from=2024-12-31&date_to=2024-01-01" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "code": 422,
  "message": "The given data was invalid.",
  "errors": {
    "pageSize": ["Page size cannot exceed 100."],
    "date_from": ["Date from must be before or equal to date to."]
  }
}
```

### 2. Unauthorized Error (401)

**Request:**
```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "code": 401,
  "message": "Unauthorized",
  "errors": {
    "auth": ["Authentication token required"]
  }
}
```

### 3. Not Found Error (404)

**Request:**
```bash
curl -X GET "http://localhost/admin-api/vip-level-rewards/99999" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "code": 404,
  "message": "Not Found",
  "errors": {
    "id": ["VIP Level Reward not found"]
  }
}
```

## JavaScript/Fetch Examples

### Using Fetch API

```javascript
// List with filters
const listRewards = async () => {
  const response = await fetch('http://localhost/admin-api/vip-level-rewards?user_name=john&pageSize=20', {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN',
      'Accept': 'application/json'
    }
  });
  
  const data = await response.json();
  console.log(data);
};

// Show specific reward
const showReward = async (id) => {
  const response = await fetch(`http://localhost/admin-api/vip-level-rewards/${id}`, {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN',
      'Accept': 'application/json'
    }
  });
  
  const data = await response.json();
  console.log(data);
};
```

### Using Axios

```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost/admin-api',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Accept': 'application/json'
  }
});

// List with filters
const listRewards = async (filters = {}) => {
  try {
    const response = await api.get('/vip-level-rewards', { params: filters });
    return response.data;
  } catch (error) {
    console.error('Error fetching rewards:', error.response.data);
    throw error;
  }
};

// Show specific reward
const showReward = async (id) => {
  try {
    const response = await api.get(`/vip-level-rewards/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching reward:', error.response.data);
    throw error;
  }
};

// Usage examples
listRewards({ user_name: 'john', pageSize: 20 });
listRewards({ vip_level_name: 'gold', date_from: '2024-01-01' });
showReward(1);
```

## Common Use Cases

### 1. Search for User's Rewards
```bash
# Find all rewards for a specific user
curl -X GET "http://localhost/admin-api/vip-level-rewards?user_id=123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Audit Trail by Date
```bash
# Get all rewards claimed in a specific month
curl -X GET "http://localhost/admin-api/vip-level-rewards?date_from=2024-01-01&date_to=2024-01-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. VIP Level Analysis
```bash
# Find all Gold level rewards
curl -X GET "http://localhost/admin-api/vip-level-rewards?vip_level_name=gold" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Transaction Investigation
```bash
# Search for specific transaction
curl -X GET "http://localhost/admin-api/vip-level-rewards?transaction_code=TXN123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Tips for Testing

1. **Use Postman**: Import the OpenAPI specification for easy testing
2. **Check Authentication**: Ensure your JWT token is valid and not expired
3. **Validate Parameters**: Follow the validation rules to avoid 422 errors
4. **Handle Pagination**: Use the pagination links for large datasets
5. **Error Handling**: Always check the response status and handle errors appropriately
