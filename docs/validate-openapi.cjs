#!/usr/bin/env node

/**
 * Simple OpenAPI YAML validation script
 * 
 * This script performs basic validation of the OpenAPI specification file.
 * For full validation, use tools like swagger-codegen or online validators.
 */

const fs = require('fs');
const path = require('path');

const yamlFile = path.join(__dirname, 'vip-level-rewards-api.yaml');

try {
    // Check if file exists
    if (!fs.existsSync(yamlFile)) {
        console.error('❌ OpenAPI file not found:', yamlFile);
        process.exit(1);
    }

    // Read the file
    const content = fs.readFileSync(yamlFile, 'utf8');
    
    // Basic checks
    const checks = [
        {
            name: 'OpenAPI version',
            test: () => content.includes('openapi: 3.0.3'),
            message: 'OpenAPI version 3.0.3 specified'
        },
        {
            name: 'Info section',
            test: () => content.includes('info:') && content.includes('title:') && content.includes('version:'),
            message: 'Info section with title and version present'
        },
        {
            name: 'Paths section',
            test: () => content.includes('paths:'),
            message: 'Paths section present'
        },
        {
            name: 'List endpoint',
            test: () => content.includes('/vip-level-rewards:') && content.includes('get:'),
            message: 'List VIP Level Rewards endpoint defined'
        },
        {
            name: 'Show endpoint',
            test: () => content.includes('/vip-level-rewards/{id}:') && content.includes('get:'),
            message: 'Show VIP Level Reward endpoint defined'
        },
        {
            name: 'Components section',
            test: () => content.includes('components:') && content.includes('schemas:'),
            message: 'Components and schemas sections present'
        },
        {
            name: 'Security schemes',
            test: () => content.includes('securitySchemes:') && content.includes('bearerAuth:'),
            message: 'Bearer authentication scheme defined'
        },
        {
            name: 'Response schemas',
            test: () => content.includes('VipLevelRewardListResponse:') && content.includes('VipLevelRewardResponse:'),
            message: 'Response schemas defined'
        },
        {
            name: 'Error responses',
            test: () => content.includes('ErrorResponse:') && content.includes('ValidationErrorResponse:'),
            message: 'Error response schemas defined'
        },
        {
            name: 'Query parameters',
            test: () => content.includes('user_name') && content.includes('vip_level_name') && content.includes('pageSize'),
            message: 'All required query parameters documented'
        }
    ];

    console.log('🔍 Validating OpenAPI specification...\n');

    let passed = 0;
    let failed = 0;

    checks.forEach(check => {
        try {
            if (check.test()) {
                console.log(`✅ ${check.name}: ${check.message}`);
                passed++;
            } else {
                console.log(`❌ ${check.name}: Check failed`);
                failed++;
            }
        } catch (error) {
            console.log(`❌ ${check.name}: Error during check - ${error.message}`);
            failed++;
        }
    });

    console.log(`\n📊 Validation Summary:`);
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📄 File size: ${(content.length / 1024).toFixed(2)} KB`);
    console.log(`   📝 Lines: ${content.split('\n').length}`);

    if (failed === 0) {
        console.log('\n🎉 OpenAPI specification looks good!');
        console.log('\n💡 Next steps:');
        console.log('   1. Validate with Swagger Editor: https://editor.swagger.io/');
        console.log('   2. Import into Postman for testing');
        console.log('   3. Generate client SDKs if needed');
        process.exit(0);
    } else {
        console.log('\n⚠️  Some checks failed. Please review the specification.');
        process.exit(1);
    }

} catch (error) {
    console.error('❌ Error validating OpenAPI file:', error.message);
    process.exit(1);
}
