openapi: 3.0.3
info:
  title: VIP Level Rewards API
  description: |
    API for managing VIP Level Rewards in the admin system. This API provides read-only access to VIP level reward records with comprehensive filtering capabilities.

    VIP Level Rewards track when users claim rewards for achieving specific VIP levels, including transaction details and timestamps.
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost/admin-api
    description: Local development server
  - url: https://api.example.com/admin-api
    description: Production server

security:
  - bearerAuth: []

paths:
  /vip-level-rewards:
    get:
      summary: List VIP Level Rewards
      description: |
        Retrieve a paginated list of VIP level rewards with optional filtering capabilities.

        This endpoint supports filtering by user information, VIP level details, transaction codes, and date ranges.
        All filters are optional and can be combined for precise searching.
      operationId: listVipLevelRewards
      tags:
        - VIP Level Rewards
      parameters:
        - name: user_id
          in: query
          description: Filter by specific user ID
          required: false
          schema:
            type: integer
            minimum: 1
            example: 123
        - name: vip_level_id
          in: query
          description: Filter by specific VIP level ID
          required: false
          schema:
            type: integer
            minimum: 1
            example: 5
        - name: user_name
          in: query
          description: Filter by user name (partial match, case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 255
            example: "john"
        - name: vip_level_name
          in: query
          description: Filter by VIP level name (partial match, case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 255
            example: "gold"
        - name: transaction_code
          in: query
          description: Filter by transaction code (partial match, case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 255
            example: "TXN123"
        - name: date_from
          in: query
          description: Filter rewards claimed from this date (inclusive)
          required: false
          schema:
            type: string
            format: date
            example: "2024-01-01"
        - name: date_to
          in: query
          description: Filter rewards claimed until this date (inclusive)
          required: false
          schema:
            type: string
            format: date
            example: "2024-12-31"
        - name: pageSize
          in: query
          description: Number of records per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 20
      responses:
        '200':
          description: Successful response with paginated VIP level rewards
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VipLevelRewardListResponse'
              examples:
                success:
                  summary: Successful list response
                  value:
                    code: 200
                    message: "Success"
                    data:
                      current_page: 1
                      data:
                        - id: 1
                          user_id: 123
                          vip_level_id: 5
                          reward_amount: "1000.00"
                          transaction_code: "TXN123456789"
                          claimed_at: "2024-01-15T10:30:00.000000Z"
                          created_at: "2024-01-15T10:30:00.000000Z"
                          updated_at: "2024-01-15T10:30:00.000000Z"
                          user:
                            id: 123
                            name: "John Doe"
                            email: "<EMAIL>"
                          vip_level:
                            id: 5
                            name: "Gold"
                            level_order: 3
                      first_page_url: "http://localhost/admin-api/vip-level-rewards?page=1"
                      from: 1
                      last_page: 5
                      last_page_url: "http://localhost/admin-api/vip-level-rewards?page=5"
                      links:
                        - url: null
                          label: "&laquo; Previous"
                          active: false
                        - url: "http://localhost/admin-api/vip-level-rewards?page=1"
                          label: "1"
                          active: true
                      next_page_url: "http://localhost/admin-api/vip-level-rewards?page=2"
                      path: "http://localhost/admin-api/vip-level-rewards"
                      per_page: 10
                      prev_page_url: null
                      to: 10
                      total: 50
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /vip-level-rewards/{id}:
    get:
      summary: Show VIP Level Reward
      description: |
        Retrieve detailed information about a specific VIP level reward record.

        This endpoint returns the complete reward record including related user and VIP level information.
      operationId: showVipLevelReward
      tags:
        - VIP Level Rewards
      parameters:
        - name: id
          in: path
          description: VIP Level Reward ID
          required: true
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        '200':
          description: Successful response with VIP level reward details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VipLevelRewardResponse'
              examples:
                success:
                  summary: Successful show response
                  value:
                    code: 200
                    message: "Success"
                    data:
                      id: 1
                      user_id: 123
                      vip_level_id: 5
                      reward_amount: "1000.00"
                      transaction_code: "TXN123456789"
                      claimed_at: "2024-01-15T10:30:00.000000Z"
                      created_at: "2024-01-15T10:30:00.000000Z"
                      updated_at: "2024-01-15T10:30:00.000000Z"
                      user:
                        id: 123
                        name: "John Doe"
                        email: "<EMAIL>"
                      vip_level:
                        id: 5
                        name: "Gold"
                        level_order: 3
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the admin authentication endpoint

  schemas:
    VipLevelReward:
      type: object
      description: VIP Level Reward record representing a user's claimed reward for achieving a VIP level
      properties:
        id:
          type: integer
          description: Unique identifier for the VIP level reward
          example: 1
        user_id:
          type: integer
          description: ID of the user who claimed the reward
          example: 123
        vip_level_id:
          type: integer
          description: ID of the VIP level for which the reward was claimed
          example: 5
        reward_amount:
          type: string
          format: decimal
          description: Amount of the reward (stored as decimal with 10 precision)
          example: "1000.00"
        transaction_code:
          type: string
          description: Unique transaction code for the reward claim
          maxLength: 255
          example: "TXN123456789"
        claimed_at:
          type: string
          format: date-time
          description: Timestamp when the reward was claimed
          example: "2024-01-15T10:30:00.000000Z"
        created_at:
          type: string
          format: date-time
          description: Record creation timestamp
          example: "2024-01-15T10:30:00.000000Z"
        updated_at:
          type: string
          format: date-time
          description: Record last update timestamp
          example: "2024-01-15T10:30:00.000000Z"
        user:
          $ref: '#/components/schemas/User'
        vip_level:
          $ref: '#/components/schemas/VipLevel'
      required:
        - id
        - user_id
        - vip_level_id
        - reward_amount
        - transaction_code
        - claimed_at
        - created_at
        - updated_at

    User:
      type: object
      description: User information related to the VIP level reward
      properties:
        id:
          type: integer
          description: User's unique identifier
          example: 123
        name:
          type: string
          description: User's full name
          example: "John Doe"
        email:
          type: string
          format: email
          description: User's email address
          example: "<EMAIL>"
      required:
        - id
        - name
        - email

    VipLevel:
      type: object
      description: VIP Level information related to the reward
      properties:
        id:
          type: integer
          description: VIP level's unique identifier
          example: 5
        name:
          type: string
          description: VIP level name
          example: "Gold"
        level_order:
          type: integer
          description: Numeric order of the VIP level (higher = better)
          example: 3
      required:
        - id
        - name
        - level_order

    PaginationMeta:
      type: object
      description: Laravel pagination metadata
      properties:
        current_page:
          type: integer
          description: Current page number
          example: 1
        first_page_url:
          type: string
          description: URL to the first page
          example: "http://localhost/admin-api/vip-level-rewards?page=1"
        from:
          type: integer
          description: Starting record number for current page
          example: 1
        last_page:
          type: integer
          description: Last page number
          example: 5
        last_page_url:
          type: string
          description: URL to the last page
          example: "http://localhost/admin-api/vip-level-rewards?page=5"
        links:
          type: array
          description: Pagination links
          items:
            type: object
            properties:
              url:
                type: string
                nullable: true
                description: Link URL
                example: "http://localhost/admin-api/vip-level-rewards?page=2"
              label:
                type: string
                description: Link label
                example: "2"
              active:
                type: boolean
                description: Whether this link is active
                example: false
        next_page_url:
          type: string
          nullable: true
          description: URL to the next page
          example: "http://localhost/admin-api/vip-level-rewards?page=2"
        path:
          type: string
          description: Base path for pagination
          example: "http://localhost/admin-api/vip-level-rewards"
        per_page:
          type: integer
          description: Number of records per page
          example: 10
        prev_page_url:
          type: string
          nullable: true
          description: URL to the previous page
          example: null
        to:
          type: integer
          description: Ending record number for current page
          example: 10
        total:
          type: integer
          description: Total number of records
          example: 50

    PaginatedVipLevelRewards:
      allOf:
        - $ref: '#/components/schemas/PaginationMeta'
        - type: object
          properties:
            data:
              type: array
              description: Array of VIP level reward records
              items:
                $ref: '#/components/schemas/VipLevelReward'

    ApiResponse:
      type: object
      description: Standard API response wrapper
      properties:
        code:
          type: integer
          description: HTTP status code
          example: 200
        message:
          type: string
          description: Response message
          example: "Success"
      required:
        - code
        - message

    VipLevelRewardListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PaginatedVipLevelRewards'

    VipLevelRewardResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/VipLevelReward'

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            errors:
              type: object
              description: Detailed error information
              additionalProperties:
                type: array
                items:
                  type: string
              example:
                user_id: ["The selected user does not exist."]
                date_from: ["Date from must be before or equal to date to."]

    ValidationErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            message:
              type: string
              example: "The given data was invalid."
            errors:
              type: object
              description: Field-specific validation errors
              additionalProperties:
                type: array
                items:
                  type: string
              example:
                user_id: ["User ID must be a valid integer.", "The selected user does not exist."]
                vip_level_name: ["VIP level name cannot exceed 255 characters."]
                pageSize: ["Page size must be at least 1.", "Page size cannot exceed 100."]
                date_from: ["Date from must be a valid date.", "Date from must be before or equal to date to."]
                date_to: ["Date range cannot exceed 365 days."]

  responses:
    BadRequest:
      description: Bad Request - Invalid request parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            invalid_parameters:
              summary: Invalid request parameters
              value:
                code: 400
                message: "Bad Request"
                errors:
                  general: ["Invalid request parameters"]

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            missing_token:
              summary: Missing authentication token
              value:
                code: 401
                message: "Unauthorized"
                errors:
                  auth: ["Authentication token required"]
            invalid_token:
              summary: Invalid authentication token
              value:
                code: 401
                message: "Unauthorized"
                errors:
                  auth: ["Invalid or expired token"]

    NotFound:
      description: Not Found - Resource does not exist
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            reward_not_found:
              summary: VIP Level Reward not found
              value:
                code: 404
                message: "Not Found"
                errors:
                  id: ["VIP Level Reward not found"]

    ValidationError:
      description: Validation Error - Request data validation failed
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationErrorResponse'
          examples:
            validation_failed:
              summary: Multiple validation errors
              value:
                code: 422
                message: "The given data was invalid."
                errors:
                  user_id: ["The selected user does not exist."]
                  pageSize: ["Page size cannot exceed 100."]
                  date_from: ["Date from must be before or equal to date to."]

    InternalServerError:
      description: Internal Server Error - Unexpected server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            server_error:
              summary: Internal server error
              value:
                code: 500
                message: "Internal Server Error"
                errors:
                  server: ["An unexpected error occurred"]

tags:
  - name: VIP Level Rewards
    description: |
      Operations for managing VIP Level Rewards

      VIP Level Rewards represent rewards that users have claimed for achieving specific VIP levels.
      Each reward record includes the user information, VIP level details, reward amount, and transaction tracking.
