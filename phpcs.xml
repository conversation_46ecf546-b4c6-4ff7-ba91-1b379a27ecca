<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PSR12" xsi:noNamespaceSchemaLocation="./vendor/squizlabs/php_codesniffer/phpcs.xsd">
    <description>PHPCS configuration for Laravel with PSR-12 standard</description>

    <!-- Define the coding standard to use -->
    <rule ref="PSR12"/>

    <!-- Include specific directories and files -->
    <file>app/</file>
    <file>config/</file>
    <file>database/</file>
    <file>resources/</file>
    <file>routes/</file>

    <!-- Exclude specific directories and files -->
    <exclude-pattern>vendor/*</exclude-pattern>
    <exclude-pattern>node_modules/*</exclude-pattern>
    <exclude-pattern>public/*</exclude-pattern>
    <exclude-pattern>storage/*</exclude-pattern>
    <exclude-pattern>bootstrap/*</exclude-pattern>
    <exclude-pattern>tests/*</exclude-pattern>

    <!-- There MUST NOT be a hard limit on line length.
    The soft limit on line length MUST be 120 characters.
    Lines SHOULD NOT be longer than 80 characters; lines longer than that SHOULD be split into multiple subsequent lines of no more than 80 characters each. -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="180"/>
        </properties>
    </rule>
</ruleset>
