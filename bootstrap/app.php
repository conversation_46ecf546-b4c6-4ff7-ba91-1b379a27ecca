<?php

use App\Http\Middleware\Authenticate;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withRouting(
        using: function () {
            Route::prefix('admin-api')
                ->name('larke-admin.')
                ->middleware([
                    'larke-admin',
                    'auth:larke',
                    \Illuminate\Routing\Middleware\SubstituteBindings::class,
                ])
                ->group(base_path('routes/admin-api.php'));
            Route::prefix('api')
                ->name('api.')
                ->group(base_path('routes/api.php'));
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'tags' => \App\Http\Middleware\TelescopeTagsMiddleware::class,
            'larke-admin.auth' => Authenticate::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Exception $e, Request $request) {
            return (new \App\Exceptions\ExceptionRenderService($e))->renderException();
        });
    })->create();
